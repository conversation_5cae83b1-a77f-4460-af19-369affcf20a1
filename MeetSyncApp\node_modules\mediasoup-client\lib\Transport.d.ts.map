{"version": 3, "file": "Transport.d.ts", "sourceRoot": "", "sources": ["../src/Transport.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AAIxD,OAAO,EACN,KAAK,cAAc,EACnB,gBAAgB,EAEhB,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAE,QAAQ,EAAE,KAAK,eAAe,EAAE,MAAM,YAAY,CAAC;AAC5D,OAAO,EAAE,QAAQ,EAAE,KAAK,eAAe,EAAE,MAAM,YAAY,CAAC;AAC5D,OAAO,EAAE,YAAY,EAAE,KAAK,mBAAmB,EAAE,MAAM,gBAAgB,CAAC;AACxE,OAAO,EAAE,YAAY,EAAE,KAAK,mBAAmB,EAAE,MAAM,gBAAgB,CAAC;AACxE,OAAO,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAChE,OAAO,KAAK,EAAE,cAAc,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AAC7E,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,SAAS,CAAC;AAIvC,MAAM,MAAM,gBAAgB,CAAC,gBAAgB,SAAS,OAAO,GAAG,OAAO,IAAI;IAC1E,EAAE,EAAE,MAAM,CAAC;IACX,aAAa,EAAE,aAAa,CAAC;IAC7B,aAAa,EAAE,YAAY,EAAE,CAAC;IAC9B,cAAc,EAAE,cAAc,CAAC;IAC/B,cAAc,CAAC,EAAE,cAAc,CAAC;IAChC,UAAU,CAAC,EAAE,YAAY,EAAE,CAAC;IAC5B,kBAAkB,CAAC,EAAE,qBAAqB,CAAC;IAC3C,kBAAkB,CAAC,EAAE,GAAG,CAAC;IACzB,sBAAsB,CAAC,EAAE,GAAG,CAAC;IAC7B,OAAO,CAAC,EAAE,gBAAgB,CAAC;CAC3B,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG;IAC9B,KAAK,EAAE,OAAO,CAAC;IACf,KAAK,EAAE,OAAO,CAAC;IACf,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC;CACvB,CAAC;AAEF,MAAM,MAAM,aAAa,GAAG;IAC3B;;SAEK;IACL,gBAAgB,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;CAClB,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG;IAC1B;;;OAGG;IACH,UAAU,EAAE,MAAM,CAAC;IACnB;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAChB;;;OAGG;IACH,EAAE,EAAE,MAAM,CAAC;IACX;;OAEG;IACH,QAAQ,EAAE,KAAK,GAAG,KAAK,CAAC;IACxB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,IAAI,EAAE,MAAM,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;IAC3C;;OAEG;IACH,OAAO,CAAC,EAAE,QAAQ,GAAG,SAAS,GAAG,IAAI,CAAC;CACtC,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG;IAC5B;;OAEG;IACH,IAAI,CAAC,EAAE,QAAQ,CAAC;IAChB;;OAEG;IACH,YAAY,EAAE,eAAe,EAAE,CAAC;CAChC,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,oBAAoB,GAC7B,OAAO,GACP,SAAS,GACT,SAAS,GACT,SAAS,GACT,SAAS,CAAC;AAEb;;;;;GAKG;AACH,MAAM,MAAM,eAAe,GAAG;IAC7B,SAAS,EAAE,oBAAoB,CAAC;IAChC,KAAK,EAAE,MAAM,CAAC;CACd,CAAC;AAEF,MAAM,MAAM,QAAQ,GAAG,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAEpD,MAAM,MAAM,iBAAiB,GAAG,KAAK,GAAG,WAAW,GAAG,UAAU,CAAC;AAEjE,MAAM,MAAM,eAAe,GACxB,KAAK,GACL,YAAY,GACZ,WAAW,GACX,QAAQ,GACR,cAAc,GACd,QAAQ,CAAC;AAEZ,MAAM,MAAM,kBAAkB,GAAG;IAChC,EAAE,EAAE,MAAM,CAAC;IACX,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;CACb,CAAC;AAEF,MAAM,MAAM,eAAe,GAAG;IAC7B,OAAO,EAAE;QACR;YAAE,cAAc,EAAE,cAAc,CAAA;SAAE;QAClC,MAAM,IAAI;QACV,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI;KACtB,CAAC;IACF,uBAAuB,EAAE,CAAC,iBAAiB,CAAC,CAAC;IAC7C,iBAAiB,EAAE,CAAC,8BAA8B,CAAC,CAAC;IACpD,qBAAqB,EAAE,CAAC,eAAe,CAAC,CAAC;IACzC,OAAO,EAAE;QACR;YACC,IAAI,EAAE,SAAS,CAAC;YAChB,aAAa,EAAE,aAAa,CAAC;YAC7B,OAAO,EAAE,OAAO,CAAC;SACjB;QACD,CAAC,EAAE,EAAE,EAAE,EAAE;YAAE,EAAE,EAAE,MAAM,CAAA;SAAE,KAAK,IAAI;QAChC,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI;KACtB,CAAC;IACF,WAAW,EAAE;QACZ;YACC,oBAAoB,EAAE,oBAAoB,CAAC;YAC3C,KAAK,CAAC,EAAE,MAAM,CAAC;YACf,QAAQ,CAAC,EAAE,MAAM,CAAC;YAClB,OAAO,EAAE,OAAO,CAAC;SACjB;QACD,CAAC,EAAE,EAAE,EAAE,EAAE;YAAE,EAAE,EAAE,MAAM,CAAA;SAAE,KAAK,IAAI;QAChC,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI;KACtB,CAAC;CACF,CAAC;AAEF,MAAM,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,uBAAuB,CAAC,CAAC;AAE9E,MAAM,MAAM,uBAAuB,GAAG;IACrC,KAAK,EAAE,EAAE,CAAC;IACV,WAAW,EAAE,CAAC,QAAQ,CAAC,CAAC;IACxB,WAAW,EAAE,CAAC,QAAQ,CAAC,CAAC;IACxB,eAAe,EAAE,CAAC,YAAY,CAAC,CAAC;IAChC,eAAe,EAAE,CAAC,YAAY,CAAC,CAAC;CAChC,CAAC;AAiBF,qBAAa,SAAS,CACrB,gBAAgB,SAAS,OAAO,GAAG,OAAO,CACzC,SAAQ,oBAAoB,CAAC,eAAe,CAAC;IAE9C,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAS;IAE7B,OAAO,CAAC,OAAO,CAAS;IAExB,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAkB;IAE7C,OAAO,CAAC,QAAQ,CAAC,wBAAwB,CAAM;IAG/C,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAmB;IAErD,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAgB;IAErD,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAmB;IAE5C,OAAO,CAAC,kBAAkB,CAA4B;IAEtD,OAAO,CAAC,gBAAgB,CAA0B;IAElD,OAAO,CAAC,QAAQ,CAAmB;IAEnC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAoC;IAE/D,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAoC;IAE/D,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAwC;IAEvE,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAwC;IAEvE,OAAO,CAAC,wBAAwB,CAAS;IAEzC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAoB;IAEhD,OAAO,CAAC,qBAAqB,CAA8B;IAE3D,OAAO,CAAC,2BAA2B,CAAS;IAE5C,OAAO,CAAC,sBAAsB,CAAoC;IAElE,OAAO,CAAC,wBAAwB,CAAS;IAEzC,OAAO,CAAC,uBAAuB,CAAoC;IAEnE,OAAO,CAAC,yBAAyB,CAAS;IAE1C,OAAO,CAAC,sBAAsB,CAAoC;IAElE,OAAO,CAAC,wBAAwB,CAAS;IAEzC,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,iBAAiB,CACM;gBAEzC,EACX,SAAS,EACT,EAAE,EACF,aAAa,EACb,aAAa,EACb,cAAc,EACd,cAAc,EACd,UAAU,EACV,kBAAkB,EAClB,kBAAkB,EAClB,sBAAsB,EACtB,OAAO,EACP,cAAc,EACd,uBAAuB,EACvB,gBAAgB,GAChB,EAAE;QACF,SAAS,EAAE,MAAM,GAAG,MAAM,CAAC;QAC3B,cAAc,EAAE,cAAc,CAAC;QAC/B,uBAAuB,EAAE,GAAG,CAAC;QAC7B,gBAAgB,EAAE,gBAAgB,CAAC;KACnC,GAAG,gBAAgB,CAAC,gBAAgB,CAAC;IA0CtC;;OAEG;IACH,IAAI,EAAE,IAAI,MAAM,CAEf;IAED;;OAEG;IACH,IAAI,MAAM,IAAI,OAAO,CAEpB;IAED;;OAEG;IACH,IAAI,SAAS,IAAI,MAAM,GAAG,MAAM,CAE/B;IAED;;OAEG;IACH,IAAI,OAAO,IAAI,gBAAgB,CAE9B;IAED;;OAEG;IACH,IAAI,iBAAiB,IAAI,iBAAiB,CAEzC;IAED;;OAEG;IACH,IAAI,eAAe,IAAI,eAAe,CAErC;IAED;;OAEG;IACH,IAAI,OAAO,IAAI,gBAAgB,CAE9B;IAED;;OAEG;IACH,IAAI,OAAO,CAAC,OAAO,EAAE,gBAAgB,EAEpC;IAED,IAAI,QAAQ,IAAI,iBAAiB,CAEhC;IAED;;OAEG;IACH,KAAK,IAAI,IAAI;IA+Cb;;;;OAIG;IACG,QAAQ,IAAI,OAAO,CAAC,cAAc,CAAC;IAQzC;;OAEG;IACG,UAAU,CAAC,EAChB,aAAa,GACb,EAAE;QACF,aAAa,EAAE,aAAa,CAAC;KAC7B,GAAG,OAAO,CAAC,IAAI,CAAC;IAgBjB;;OAEG;IACG,gBAAgB,CAAC,EACtB,UAAU,GACV,GAAE;QAAE,UAAU,CAAC,EAAE,YAAY,EAAE,CAAA;KAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAgBvD;;OAEG;IACG,OAAO,CAAC,eAAe,SAAS,OAAO,GAAG,OAAO,EAAE,EACxD,KAAK,EACL,SAAS,EACT,YAAY,EACZ,KAAK,EACL,UAAiB,EACjB,mBAA0B,EAC1B,cAAsB,EACtB,WAAW,EACX,OAA+B,GAC/B,GAAE,eAAe,CAAC,eAAe,CAAM,GAAG,OAAO,CACjD,QAAQ,CAAC,eAAe,CAAC,CACzB;IA0ID;;OAEG;IACG,OAAO,CAAC,eAAe,SAAS,OAAO,GAAG,OAAO,EAAE,EACxD,EAAE,EACF,UAAU,EACV,IAAI,EACJ,aAAa,EACb,QAAQ,EACR,aAAa,EACb,OAA+B,GAC/B,EAAE,eAAe,CAAC,eAAe,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;IA8DxE;;OAEG;IACG,WAAW,CAAC,mBAAmB,SAAS,OAAO,GAAG,OAAO,EAAE,EAChE,OAAc,EACd,iBAAiB,EACjB,cAAc,EACd,KAAU,EACV,QAAa,EACb,OAAmC,GACnC,GAAE,mBAAmB,CAAC,mBAAmB,CAAM,GAAG,OAAO,CACzD,YAAY,CAAC,mBAAmB,CAAC,CACjC;IAqED;;OAEG;IACG,WAAW,CAAC,eAAe,SAAS,OAAO,GAAG,OAAO,EAAE,EAC5D,EAAE,EACF,cAAc,EACd,oBAAoB,EACpB,KAAU,EACV,QAAa,EACb,OAA+B,GAC/B,EAAE,mBAAmB,CAAC,eAAe,CAAC,GAAG,OAAO,CAChD,YAAY,CAAC,eAAe,CAAC,CAC7B;IAuDD,OAAO,CAAC,sBAAsB;IAwH9B,OAAO,CAAC,qBAAqB;IA6C7B,OAAO,CAAC,sBAAsB;IA6C9B,OAAO,CAAC,qBAAqB;IA2C7B,OAAO,CAAC,aAAa;IA+DrB,OAAO,CAAC,cAAc;IAwFtB,OAAO,CAAC,cAAc;IAyEtB,OAAO,CAAC,kBAAkB;IAM1B,OAAO,CAAC,kBAAkB;CAK1B"}