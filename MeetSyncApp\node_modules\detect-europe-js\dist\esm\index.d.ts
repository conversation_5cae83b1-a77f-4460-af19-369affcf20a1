/*! detectEurope.js v0.1.2
    Determine whether a user is from the European Union (EU) area
    https://github.com/faisalman/detect-europe-js
    Author: <PERSON><PERSON><PERSON> <<EMAIL>>
    MIT License */
declare const isFromEU: () => boolean;
declare const isFromEEA: () => boolean;
declare const isFromEFTA: () => boolean;
declare const isFromSchengen: () => boolean;
export { isFromEU, isFromEEA, isFromEFTA, isFromSchengen };
