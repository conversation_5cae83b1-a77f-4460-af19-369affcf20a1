package org.meetsync.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/")
@CrossOrigin(origins = "*", maxAge = 3600)
public class HealthController {

    @Autowired
    private DataSource dataSource;

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<?> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // Check database connection
            String dbStatus = checkDatabaseConnection();
            
            health.put("status", "healthy".equals(dbStatus) ? "正常" : "异常");
            health.put("timestamp", LocalDateTime.now());
            health.put("service", "MeetSync Java 服务器");
            health.put("version", "1.0.0");
            health.put("database", "healthy".equals(dbStatus) ? "数据库连接正常" : "数据库连接异常");
            
            if ("healthy".equals(dbStatus)) {
                return ResponseEntity.ok(health);
            } else {
                return ResponseEntity.status(503).body(health);
            }
            
        } catch (Exception e) {
            health.put("status", "异常");
            health.put("timestamp", LocalDateTime.now());
            health.put("error", e.getMessage());

            return ResponseEntity.status(503).body(health);
        }
    }

    /**
     * Simple ping endpoint
     */
    @GetMapping("/ping")
    public ResponseEntity<?> ping() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "pong");
        response.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(response);
    }

    /**
     * Root endpoint
     */
    @GetMapping("/")
    public ResponseEntity<?> root() {
        Map<String, Object> response = new HashMap<>();
        response.put("service", "MeetSync Java 服务器");
        response.put("version", "1.0.0");
        response.put("status", "运行中");
        response.put("timestamp", LocalDateTime.now());

        return ResponseEntity.ok(response);
    }

    /**
     * Check database connection
     */
    private String checkDatabaseConnection() {
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(5)) {
                return "healthy";
            } else {
                return "unhealthy";
            }
        } catch (Exception e) {
            return "disconnected";
        }
    }
}
