package org.meetsync.ExceptionHandler;


public enum EnumBusinessError implements CommonError {
    PARAM_ENTER_VALIDATION_ERROR(4001, "参数不合法！"),
    UNKNOWN_ERROR(5002, "未知错误！"),

    RPC_API_ERROR(5003, "上游接口异常！"),

    ATTACK_SUB_ERROR(5004, "非法提交！"),

    USER_EXIST_ERROR(1003, "用户已存在!"),
    AUTH_ERROR(1004, "授权失败!"),
    EXPIRED_ERROR(2004, "登录已过期!"),
    INTERFACEISNOTIMPLEMENTED_ERROR(5005, "接口未实现!"),
    //不可操作机器人的角色权限
    ROBOT_ROLE_ERROR(1005, "不可操作机器人的角色权限!"),
    ;
    private final int errCode;
    private String errMsg;

    EnumBusinessError(int errCode, String errMsg) {
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    @Override
    public int getErrorCode() {
        return this.errCode;
    }

    @Override
    public String getErrorMsg() {
        return this.errMsg;
    }

    @Override
    public CommonError setErrorMsg(String errorMsg) {
        errMsg = errorMsg;
        return this;
    }
}
