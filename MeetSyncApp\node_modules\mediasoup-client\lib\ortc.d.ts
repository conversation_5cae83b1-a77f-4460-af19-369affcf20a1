import type { RtpCapabilities, MediaKind, RtpCodecCapability, RtpParameters, RtpCodecParameters } from './RtpParameters';
import type { SctpCapabilities, SctpStreamParameters } from './SctpParameters';
/**
 * Validates RtpCapabilities. It may modify given data by adding missing
 * fields with default values.
 * It throws if invalid.
 */
export declare function validateRtpCapabilities(caps: RtpCapabilities): void;
/**
 * Validates RtpParameters. It may modify given data by adding missing
 * fields with default values.
 * It throws if invalid.
 */
export declare function validateRtpParameters(params: RtpParameters): void;
/**
 * Validates SctpStreamParameters. It may modify given data by adding missing
 * fields with default values.
 * It throws if invalid.
 */
export declare function validateSctpStreamParameters(params: SctpStreamParameters): void;
/**
 * Validates SctpCapabilities. It may modify given data by adding missing
 * fields with default values.
 * It throws if invalid.
 */
export declare function validateSctpCapabilities(caps: SctpCapabilities): void;
/**
 * Generate extended RTP capabilities for sending and receiving.
 *
 * Resulting codecs keep order preferrred by local or remote capabilities
 * depending on `preferLocalCodecsOrder`.
 */
export declare function getExtendedRtpCapabilities(localCaps: RtpCapabilities, remoteCaps: RtpCapabilities, preferLocalCodecsOrder: boolean): any;
/**
 * Generate RTP capabilities for receiving media based on the given extended
 * RTP capabilities.
 */
export declare function getRecvRtpCapabilities(extendedRtpCapabilities: any): RtpCapabilities;
/**
 * Generate RTP parameters of the given kind for sending media.
 * NOTE: mid, encodings and rtcp fields are left empty.
 */
export declare function getSendingRtpParameters(kind: MediaKind, extendedRtpCapabilities: any): RtpParameters;
/**
 * Generate RTP parameters of the given kind suitable for the remote SDP answer.
 */
export declare function getSendingRemoteRtpParameters(kind: MediaKind, extendedRtpCapabilities: any): RtpParameters;
/**
 * Reduce given codecs by returning an array of codecs "compatible" with the
 * given capability codec. If no capability codec is given, take the first
 * one(s).
 *
 * Given codecs must be generated by ortc.getSendingRtpParameters() or
 * ortc.getSendingRemoteRtpParameters().
 *
 * The returned array of codecs also include a RTX codec if available.
 */
export declare function reduceCodecs(codecs: RtpCodecParameters[], capCodec?: RtpCodecCapability): RtpCodecParameters[];
/**
 * Create RTP parameters for a Consumer for the RTP probator.
 */
export declare function generateProbatorRtpParameters(videoRtpParameters: RtpParameters): RtpParameters;
/**
 * Whether media can be sent based on the given RTP capabilities.
 */
export declare function canSend(kind: MediaKind, extendedRtpCapabilities: any): boolean;
/**
 * Whether the given RTP parameters can be received with the given RTP
 * capabilities.
 */
export declare function canReceive(rtpParameters: RtpParameters, extendedRtpCapabilities: any): boolean;
//# sourceMappingURL=ortc.d.ts.map