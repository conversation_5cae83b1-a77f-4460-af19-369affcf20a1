const bcrypt = require('bcryptjs')
const db = require('../database/connection')

class RoomModel {
  constructor(data) {
    this.id = data.id
    this.room_id = data.room_id
    this.name = data.name
    this.description = data.description
    this.creator_id = data.creator_id
    this.max_participants = data.max_participants
    this.is_public = data.is_public
    this.required_role = data.required_role
    this.is_active = data.is_active
    this.created_at = data.created_at
    this.updated_at = data.updated_at
  }

  // Create new room
  static async create(roomData) {
    const { 
      room_id, 
      name, 
      description, 
      creator_id, 
      max_participants = 10, 
      is_public = true, 
      password = null,
      required_role = 'user'
    } = roomData
    
    let password_hash = null
    if (password) {
      const saltRounds = 10
      password_hash = await bcrypt.hash(password, saltRounds)
    }
    
    const sql = `
      INSERT INTO rooms (room_id, name, description, creator_id, max_participants, is_public, password_hash, required_role) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `
    
    try {
      const result = await db.query(sql, [
        room_id, name, description, creator_id, max_participants, is_public, password_hash, required_role
      ])
      return await RoomModel.findById(result.insertId)
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('Room ID already exists')
      }
      throw error
    }
  }

  // Find room by database ID
  static async findById(id) {
    const sql = 'SELECT * FROM rooms WHERE id = ? AND is_active = TRUE'
    const rooms = await db.query(sql, [id])
    return rooms.length > 0 ? new RoomModel(rooms[0]) : null
  }

  // Find room by room_id
  static async findByRoomId(room_id) {
    const sql = 'SELECT * FROM rooms WHERE room_id = ? AND is_active = TRUE'
    const rooms = await db.query(sql, [room_id])
    return rooms.length > 0 ? new RoomModel(rooms[0]) : null
  }

  // Get all public rooms
  static async getPublicRooms(limit = 20, offset = 0) {
    const sql = `
      SELECT r.*, u.username as creator_name
      FROM rooms r
      JOIN users u ON r.creator_id = u.id
      WHERE r.is_public = TRUE AND r.is_active = TRUE
      ORDER BY r.created_at DESC
      LIMIT ? OFFSET ?
    `
    
    const rooms = await db.query(sql, [limit, offset])
    return rooms.map(room => new RoomModel(room))
  }

  // Get rooms created by user
  static async getRoomsByCreator(creator_id, limit = 20, offset = 0) {
    const sql = `
      SELECT * FROM rooms 
      WHERE creator_id = ? AND is_active = TRUE
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `
    
    const rooms = await db.query(sql, [creator_id, limit, offset])
    return rooms.map(room => new RoomModel(room))
  }

  // Verify room password
  async verifyPassword(password) {
    if (!this.password_hash) {
      return true // No password required
    }
    
    return await bcrypt.compare(password, this.password_hash)
  }

  // Update room
  async update(updateData) {
    const allowedFields = ['name', 'description', 'max_participants', 'is_public', 'required_role']
    const updates = []
    const values = []
    
    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key)) {
        updates.push(`${key} = ?`)
        values.push(value)
      }
    }
    
    if (updates.length === 0) {
      return this
    }
    
    values.push(this.id)
    const sql = `UPDATE rooms SET ${updates.join(', ')}, updated_at = NOW() WHERE id = ?`
    
    await db.query(sql, values)
    return await RoomModel.findById(this.id)
  }

  // Set room password
  async setPassword(password) {
    let password_hash = null
    if (password) {
      const saltRounds = 10
      password_hash = await bcrypt.hash(password, saltRounds)
    }
    
    const sql = 'UPDATE rooms SET password_hash = ?, updated_at = NOW() WHERE id = ?'
    await db.query(sql, [password_hash, this.id])
  }

  // Deactivate room
  async deactivate() {
    const sql = 'UPDATE rooms SET is_active = FALSE, updated_at = NOW() WHERE id = ?'
    await db.query(sql, [this.id])
  }

  // Grant permission to user
  async grantPermission(user_id, permission, granted_by, expires_at = null) {
    const sql = `
      INSERT INTO user_room_permissions (user_id, room_id, permission, granted_by, expires_at)
      VALUES (?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE 
      permission = VALUES(permission),
      granted_by = VALUES(granted_by),
      expires_at = VALUES(expires_at),
      granted_at = NOW()
    `
    
    await db.query(sql, [user_id, this.id, permission, granted_by, expires_at])
  }

  // Revoke permission from user
  async revokePermission(user_id) {
    const sql = 'DELETE FROM user_room_permissions WHERE user_id = ? AND room_id = ?'
    await db.query(sql, [user_id, this.id])
  }

  // Get room participants with permissions
  async getParticipants() {
    const sql = `
      SELECT u.id, u.username, u.role, urp.permission, urp.granted_at
      FROM user_room_permissions urp
      JOIN users u ON urp.user_id = u.id
      WHERE urp.room_id = ? AND u.is_active = TRUE
      AND (urp.expires_at IS NULL OR urp.expires_at > NOW())
      ORDER BY urp.granted_at ASC
    `
    
    return await db.query(sql, [this.id])
  }

  // Log room activity
  async logActivity(user_id, action, ip_address = null) {
    const sql = `
      INSERT INTO room_activity_log (room_id, user_id, action, ip_address)
      VALUES (?, ?, ?, ?)
    `
    
    await db.query(sql, [this.id, user_id, action, ip_address])
  }

  // Get room activity log
  async getActivityLog(limit = 50, offset = 0) {
    const sql = `
      SELECT ral.*, u.username
      FROM room_activity_log ral
      LEFT JOIN users u ON ral.user_id = u.id
      WHERE ral.room_id = ?
      ORDER BY ral.timestamp DESC
      LIMIT ? OFFSET ?
    `
    
    return await db.query(sql, [this.id, limit, offset])
  }

  // Check if user can perform action
  canUserPerformAction(userPermission, action) {
    const permissionLevels = {
      view_only: ['view'],
      audio_only: ['view', 'audio'],
      video_audio: ['view', 'audio', 'video'],
      full_access: ['view', 'audio', 'video', 'screen', 'manage']
    }
    
    const allowedActions = permissionLevels[userPermission] || []
    return allowedActions.includes(action)
  }

  // Convert to JSON
  toJSON() {
    return {
      id: this.id,
      room_id: this.room_id,
      name: this.name,
      description: this.description,
      creator_id: this.creator_id,
      max_participants: this.max_participants,
      is_public: this.is_public,
      required_role: this.required_role,
      has_password: !!this.password_hash,
      created_at: this.created_at,
      updated_at: this.updated_at
    }
  }
}

module.exports = RoomModel
