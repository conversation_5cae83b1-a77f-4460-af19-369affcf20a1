package org.meetsync.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import org.meetsync.dto.RoomCreateDto;
import org.meetsync.dto.RoomResponseDto;
import org.meetsync.entity.User;
import org.meetsync.service.RoomService;
import org.meetsync.service.MediaProxyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/rooms")
@CrossOrigin(origins = "*", maxAge = 3600)
public class RoomController {

    private static final Logger logger = LoggerFactory.getLogger(RoomController.class);

    @Autowired
    private RoomService roomService;

    @Autowired
    private MediaProxyService mediaProxyService;

    /**
     * Get all public rooms
     */
    @GetMapping("/public")
    public ResponseEntity<?> getPublicRooms() {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(createErrorResponse("用户未认证"));
            }

            List<RoomResponseDto> rooms = roomService.getAccessiblePublicRooms(currentUser.getId());
            return ResponseEntity.ok(rooms);

        } catch (Exception e) {
            logger.error("Error getting public rooms", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("获取公开房间列表失败"));
        }
    }

    /**
     * Get user's created rooms
     */
    @GetMapping("/my-rooms")
    public ResponseEntity<?> getMyRooms() {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(createErrorResponse("用户未认证"));
            }

            List<RoomResponseDto> rooms = roomService.getUserCreatedRooms(currentUser.getId());
            return ResponseEntity.ok(rooms);

        } catch (Exception e) {
            logger.error("Error getting user rooms", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("获取我的房间列表失败"));
        }
    }

    /**
     * Get rooms user has access to
     */
    @GetMapping("/accessible")
    public ResponseEntity<?> getAccessibleRooms() {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(createErrorResponse("User not authenticated"));
            }

            List<RoomResponseDto> rooms = roomService.getUserAccessibleRooms(currentUser.getId());
            return ResponseEntity.ok(rooms);
            
        } catch (Exception e) {
            logger.error("Error getting accessible rooms", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("Failed to get accessible rooms"));
        }
    }

    /**
     * Get room by ID
     */
    @GetMapping("/{roomId}")
    public ResponseEntity<?> getRoomById(@PathVariable String roomId) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(createErrorResponse("User not authenticated"));
            }

            Optional<RoomResponseDto> roomOpt = roomService.findByRoomId(roomId, currentUser.getId());
            
            if (roomOpt.isPresent()) {
                return ResponseEntity.ok(roomOpt.get());
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(createErrorResponse("Room not found"));
            }
            
        } catch (Exception e) {
            logger.error("Error getting room by ID: {}", roomId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("Failed to get room"));
        }
    }

    /**
     * Create new room
     */
    @PostMapping
    public ResponseEntity<?> createRoom(@Valid @RequestBody RoomCreateDto createDto) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(createErrorResponse("User not authenticated"));
            }

            // Check if user can create rooms (not guest)
            if (currentUser.getRole().getValue().equals("guest")) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(createErrorResponse("访客用户无法创建房间"));
            }

            RoomResponseDto room = roomService.createRoom(createDto, currentUser.getId());

            Map<String, Object> response = new HashMap<>();
            response.put("message", "房间创建成功");
            response.put("room", room);

            return ResponseEntity.status(HttpStatus.CREATED).body(response);

        } catch (RuntimeException e) {
            logger.error("Room creation failed: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(createErrorResponse(e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error during room creation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("房间创建失败"));
        }
    }

    /**
     * Update room
     */
    @PutMapping("/{roomId}")
    public ResponseEntity<?> updateRoom(@PathVariable String roomId, @Valid @RequestBody RoomCreateDto updateDto) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(createErrorResponse("User not authenticated"));
            }

            RoomResponseDto room = roomService.updateRoom(roomId, updateDto, currentUser.getId());

            Map<String, Object> response = new HashMap<>();
            response.put("message", "房间更新成功");
            response.put("room", room);

            return ResponseEntity.ok(response);

        } catch (RuntimeException e) {
            logger.error("Room update failed: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(createErrorResponse(e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error during room update", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("房间更新失败"));
        }
    }

    /**
     * Delete room
     */
    @DeleteMapping("/{roomId}")
    public ResponseEntity<?> deleteRoom(@PathVariable String roomId) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(createErrorResponse("User not authenticated"));
            }

            roomService.deleteRoom(roomId, currentUser.getId());

            Map<String, String> response = new HashMap<>();
            response.put("message", "房间删除成功");

            return ResponseEntity.ok(response);

        } catch (RuntimeException e) {
            logger.error("Room deletion failed: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(createErrorResponse(e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error during room deletion", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("房间删除失败"));
        }
    }

    /**
     * Join room
     */
    @Operation(
            summary = "加入房间",
            description = "用户加入指定房间，验证权限并返回房间信息",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功加入房间"),
            @ApiResponse(responseCode = "401", description = "用户未认证"),
            @ApiResponse(responseCode = "403", description = "无权限访问该房间"),
            @ApiResponse(responseCode = "404", description = "房间不存在")
    })
    @PostMapping("/{roomId}/join")
    public ResponseEntity<?> joinRoom(@PathVariable String roomId, @RequestBody(required = false) Map<String, String> requestBody) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(createErrorResponse("用户未认证"));
            }

            String password = null;
            if (requestBody != null) {
                password = requestBody.get("password");
            }

            // 检查用户是否可以访问房间
            boolean canAccess = roomService.canUserAccessRoom(currentUser.getId(), roomId, password);
            if (!canAccess) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(createErrorResponse("无权限访问该房间"));
            }

            // 获取房间信息
            Optional<RoomResponseDto> roomOpt = roomService.findByRoomId(roomId, currentUser.getId());
            if (!roomOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(createErrorResponse("房间不存在"));
            }

            RoomResponseDto room = roomOpt.get();
            String permission = roomService.getUserPermissionForRoom(currentUser.getId(), roomId);

            Map<String, Object> response = new HashMap<>();
            response.put("message", "成功加入房间");
            response.put("room", room);
            response.put("permission", permission);
            response.put("user", Map.of(
                "id", currentUser.getId(),
                "username", currentUser.getUsername(),
                "role", currentUser.getRole().getValue()
            ));

            // 通知媒体服务器有新用户加入
            try {
                if (mediaProxyService.isMediaServerConnected()) {
                    // 这里可以添加通知媒体服务器的逻辑
                    // 目前WebSocket连接会处理用户加入通知
                }
            } catch (Exception e) {
                logger.warn("Failed to notify media server about user join: {}", e.getMessage());
            }

            logger.info("User {} joined room {}", currentUser.getUsername(), roomId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Error joining room: {}", roomId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("加入房间失败"));
        }
    }

    /**
     * Check room access
     */
    @PostMapping("/{roomId}/check-access")
    public ResponseEntity<?> checkRoomAccess(@PathVariable String roomId, @RequestBody(required = false) Map<String, String> requestBody) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(createErrorResponse("User not authenticated"));
            }

            String password = null;
            if (requestBody != null) {
                password = requestBody.get("password");
            }

            boolean canAccess = roomService.canUserAccessRoom(currentUser.getId(), roomId, password);
            String permission = roomService.getUserPermissionForRoom(currentUser.getId(), roomId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("canAccess", canAccess);
            response.put("permission", permission);
            
            if (!canAccess) {
                response.put("reason", "Access denied");
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error checking room access for room: {}", roomId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("Failed to check room access"));
        }
    }

    /**
     * Search rooms by name
     */
    @GetMapping("/search")
    public ResponseEntity<?> searchRooms(@RequestParam String name) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(createErrorResponse("User not authenticated"));
            }

            List<RoomResponseDto> rooms = roomService.searchRoomsByName(name, currentUser.getId());
            return ResponseEntity.ok(rooms);
            
        } catch (Exception e) {
            logger.error("Error searching rooms", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("Failed to search rooms"));
        }
    }

    /**
     * Get current authenticated user
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication != null && authentication.getPrincipal() instanceof User) {
            return (User) authentication.getPrincipal();
        }
        
        return null;
    }

    /**
     * Create error response
     */
    private Map<String, String> createErrorResponse(String message) {
        Map<String, String> error = new HashMap<>();
        error.put("error", message);
        return error;
    }
}
