package org.meetsync.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.meetsync.entity.InviteCode;
import org.meetsync.entity.User;
import org.meetsync.service.InviteCodeService;
import org.meetsync.service.UserService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 邀请码管理控制器
 * 
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/api/invite-codes")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "邀请码管理", description = "邀请码生成、验证和管理相关接口")
public class InviteCodeController {
    
    private final InviteCodeService inviteCodeService;
    private final UserService userService;
    
    /**
     * 生成邀请码（需要管理员权限）
     */
    @PostMapping("/generate")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "生成邀请码", description = "管理员生成新的邀请码")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "邀请码生成成功"),
        @ApiResponse(responseCode = "403", description = "权限不足"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<Map<String, Object>> generateInviteCode(Authentication authentication) {
        try {
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);
            
            InviteCode inviteCode = inviteCodeService.generateInviteCode(currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "邀请码生成成功");
            response.put("inviteCode", inviteCode.getInviteCode());
            response.put("inviteTime", inviteCode.getInviteTime());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("生成邀请码失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "生成邀请码失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 验证邀请码
     */
    @GetMapping("/validate/{inviteCode}")
    @Operation(summary = "验证邀请码", description = "验证邀请码是否有效")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "验证完成"),
        @ApiResponse(responseCode = "400", description = "邀请码格式错误")
    })
    public ResponseEntity<Map<String, Object>> validateInviteCode(
            @Parameter(description = "邀请码", required = true)
            @PathVariable String inviteCode) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean isValid = inviteCodeService.validateInviteCode(inviteCode);
            
            response.put("success", true);
            response.put("valid", isValid);
            response.put("message", isValid ? "邀请码有效" : "邀请码无效或已使用");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("验证邀请码失败", e);
            response.put("success", false);
            response.put("valid", false);
            response.put("message", "验证失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 获取我的邀请码列表（需要管理员权限）
     */
    @GetMapping("/my")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取我的邀请码", description = "获取当前用户创建的邀请码列表")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "权限不足")
    })
    public ResponseEntity<Map<String, Object>> getMyInviteCodes(Authentication authentication) {
        try {
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);
            
            List<InviteCode> inviteCodes = inviteCodeService.getUserInviteCodes(currentUser.getId());
            long[] stats = inviteCodeService.getUserInviteCodeStats(currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("inviteCodes", inviteCodes);
            response.put("stats", Map.of(
                "total", stats[0],
                "used", stats[1],
                "unused", stats[2]
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取邀请码列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取邀请码详情（需要管理员权限）
     */
    @GetMapping("/{inviteCode}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取邀请码详情", description = "获取指定邀请码的详细信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "404", description = "邀请码不存在"),
        @ApiResponse(responseCode = "403", description = "权限不足")
    })
    public ResponseEntity<Map<String, Object>> getInviteCodeDetails(
            @Parameter(description = "邀请码", required = true)
            @PathVariable String inviteCode) {
        
        try {
            Optional<InviteCode> codeOpt = inviteCodeService.getInviteCodeDetails(inviteCode);
            
            Map<String, Object> response = new HashMap<>();
            
            if (codeOpt.isPresent()) {
                response.put("success", true);
                response.put("inviteCode", codeOpt.get());
            } else {
                response.put("success", false);
                response.put("message", "邀请码不存在");
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取邀请码详情失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取系统邀请码统计（需要管理员权限）
     */
    @GetMapping("/stats")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "获取系统邀请码统计", description = "获取整个系统的邀请码使用统计")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "403", description = "权限不足")
    })
    public ResponseEntity<Map<String, Object>> getSystemStats() {
        try {
            long[] stats = inviteCodeService.getSystemInviteCodeStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("stats", Map.of(
                "total", stats[0],
                "used", stats[1],
                "unused", stats[2]
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取系统统计失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
