package org.meetsync.repository;

import org.meetsync.entity.InviteCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 邀请码数据访问层
 * 
 * <AUTHOR> Team
 */
@Repository
public interface InviteCodeRepository extends JpaRepository<InviteCode, Long> {
    
    /**
     * 根据邀请码查找
     * 
     * @param inviteCode 邀请码
     * @return 邀请码实体
     */
    Optional<InviteCode> findByInviteCode(String inviteCode);
    
    /**
     * 根据邀请码和使用状态查找
     * 
     * @param inviteCode 邀请码
     * @param isUsed 是否已使用
     * @return 邀请码实体
     */
    Optional<InviteCode> findByInviteCodeAndIsUsed(String inviteCode, Boolean isUsed);
    
    /**
     * 查找可用的邀请码
     * 
     * @param inviteCode 邀请码
     * @return 邀请码实体
     */
    default Optional<InviteCode> findAvailableByInviteCode(String inviteCode) {
        return findByInviteCodeAndIsUsed(inviteCode, false);
    }
    
    /**
     * 根据邀请人ID查找所有邀请码
     * 
     * @param inviterId 邀请人ID
     * @return 邀请码列表
     */
    List<InviteCode> findByInviterIdOrderByInviteTimeDesc(Long inviterId);
    
    /**
     * 根据邀请人ID和使用状态查找邀请码
     * 
     * @param inviterId 邀请人ID
     * @param isUsed 是否已使用
     * @return 邀请码列表
     */
    List<InviteCode> findByInviterIdAndIsUsedOrderByInviteTimeDesc(Long inviterId, Boolean isUsed);
    
    /**
     * 根据使用人ID查找邀请码
     * 
     * @param userId 使用人ID
     * @return 邀请码实体
     */
    Optional<InviteCode> findByUserId(Long userId);
    
    /**
     * 统计邀请人的邀请码数量
     * 
     * @param inviterId 邀请人ID
     * @return 邀请码数量
     */
    long countByInviterId(Long inviterId);
    
    /**
     * 统计邀请人的已使用邀请码数量
     * 
     * @param inviterId 邀请人ID
     * @return 已使用邀请码数量
     */
    long countByInviterIdAndIsUsed(Long inviterId, Boolean isUsed);
    
    /**
     * 查找指定时间范围内创建的邀请码
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 邀请码列表
     */
    List<InviteCode> findByInviteTimeBetweenOrderByInviteTimeDesc(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 检查邀请码是否存在
     * 
     * @param inviteCode 邀请码
     * @return 是否存在
     */
    boolean existsByInviteCode(String inviteCode);
    
    /**
     * 查找过期的未使用邀请码（可选功能，如果需要设置邀请码过期时间）
     * 
     * @param expireTime 过期时间
     * @return 过期的邀请码列表
     */
    @Query("SELECT ic FROM InviteCode ic WHERE ic.isUsed = false AND ic.inviteTime < :expireTime")
    List<InviteCode> findExpiredUnusedInviteCodes(@Param("expireTime") LocalDateTime expireTime);
    
    /**
     * 统计总的邀请码数量
     * 
     * @return 总数量
     */
    @Query("SELECT COUNT(ic) FROM InviteCode ic")
    long countTotalInviteCodes();
    
    /**
     * 统计已使用的邀请码数量
     * 
     * @return 已使用数量
     */
    @Query("SELECT COUNT(ic) FROM InviteCode ic WHERE ic.isUsed = true")
    long countUsedInviteCodes();
}
