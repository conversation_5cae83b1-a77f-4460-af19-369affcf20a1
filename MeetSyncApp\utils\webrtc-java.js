/**
 * WebRTC 客户端工具类 - Java后端适配版本
 * 通过Java后端的媒体代理API进行WebRTC通信
 */
import * as mediasoupClient from 'mediasoup-client'
import authManager from './auth.js'
import apiManager from './api.js'

// 媒体类型枚举
const MediaType = {
  AUDIO: 'audio',
  VIDEO: 'video',
  SCREEN: 'screen'
}

// 事件类型
const Events = {
  ROOM_JOINED: 'roomJoined',
  ROOM_LEFT: 'roomLeft',
  PEER_JOINED: 'peerJoined',
  PEER_LEFT: 'peerLeft',
  PRODUCER_ADDED: 'producerAdded',
  PRODUCER_REMOVED: 'producerRemoved',
  PRODUCER_CLOSED: 'producerClosed',
  CONSUMER_ADDED: 'consumerAdded',
  CONSUMER_REMOVED: 'consumerRemoved',
  PARTICIPANT_COUNT_UPDATED: 'participantCountUpdated'
}

class WebRTCJavaClient {
  constructor() {
    this.device = null
    this.producerTransport = null
    this.consumerTransport = null
    
    this.roomId = null
    this.isConnected = false
    
    // 生产者和消费者管理
    this.producers = new Map()
    this.consumers = new Map()

    // 生产者ID映射 (备用方案)
    this.producerIds = new Map()
    
    // 事件监听器
    this.eventListeners = new Map()
    
    // 用户信息
    this.user = null
    this.userPermission = null
    
    // 设备信息
    this.audioDevices = []
    this.videoDevices = []
    this.selectedAudioDevice = null
    this.selectedVideoDevice = null
    
    // 当前摄像头方向（前置/后置）
    this.currentFacingMode = 'user' // 'user' 前置, 'environment' 后置

    // 切换状态控制
    this._switchingCamera = false

    // WebSocket连接
    this.ws = null
    this.wsUrl = null

    this.initEventListeners()
  }

  initEventListeners() {
    Object.values(Events).forEach(event => {
      this.eventListeners.set(event, [])
    })
  }

  // 事件监听
  on(event, callback) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).push(callback)
    }
  }

  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event)
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  // 移除所有事件监听器
  removeAllListeners() {
    this.eventListeners.forEach((listeners) => {
      listeners.length = 0 // 清空数组
    })
    console.log('已清除所有事件监听器')
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`Event listener error for ${event}:`, error)
        }
      })
    }
  }

  // 连接到房间
  async connect(roomId, password = null) {
    try {
      this.user = authManager.getCurrentUser()
      if (!this.user) {
        throw new Error('用户未登录')
      }

      this.roomId = roomId

      // 检查媒体服务器状态
      await this.checkMediaServerStatus()

      // 清理可能存在的旧生产者（防止刷新页面后的重复生产者）
      await this.cleanupOldProducers()

      // 创建媒体房间
      await this.createMediaRoom(roomId)

      // 初始化 mediasoup Device
      await this.initDevice()

      // 创建传输通道
      await this.createTransports()

      // 建立WebSocket连接用于实时通信
      await this.connectWebSocket()

      // 获取房间内已存在的生产者
      await this.getExistingProducers()

      this.isConnected = true
      this.emit(Events.ROOM_JOINED, { roomId })

      return true
    } catch (error) {
      console.error('连接房间失败:', error)
      throw error
    }
  }

  // 检查媒体服务器状态
  async checkMediaServerStatus() {
    try {
      const response = await apiManager.getMediaServerStatus()
      console.log('媒体服务器状态:', response)
      return response
    } catch (error) {
      console.error('媒体服务器连接失败:', error)
      throw new Error('媒体服务器不可用')
    }
  }

  // 清理可能存在的旧生产者（防止刷新页面后的重复生产者）
  async cleanupOldProducers() {
    try {
      console.log('清理可能存在的旧生产者...')
      await apiManager.cleanupUserProducers(this.roomId)
      console.log('旧生产者清理完成')
    } catch (error) {
      console.warn('清理旧生产者失败:', error.message)
      // 不抛出错误，因为这不是关键操作
    }
  }

  // 创建媒体房间
  async createMediaRoom(roomId) {
    try {
      const response = await apiManager.createMediaRoom(roomId)
      console.log('媒体房间创建成功:', response)
      return response
    } catch (error) {
      console.log('创建媒体房间失败 (可能已存在):', error.message)
    }
  }

  // 初始化 mediasoup Device
  async initDevice() {
    try {
      const response = await apiManager.getRtpCapabilities(this.roomId)
      const routerRtpCapabilities = response.rtpCapabilities
      
      this.device = new mediasoupClient.Device()
      await this.device.load({ routerRtpCapabilities })
      
      console.log('mediasoup Device 初始化成功')
    } catch (error) {
      console.error('初始化Device失败:', error)
      throw error
    }
  }

  // 创建传输通道
  async createTransports() {
    try {
      console.log('开始创建传输通道...')

      // 创建发送传输
      const producerTransportResponse = await apiManager.createTransport(this.roomId, 'send')
      console.log('发送传输响应:', producerTransportResponse)

      const producerTransportInfo = producerTransportResponse.transport
      console.log('发送传输信息:', producerTransportInfo)

      // 检查必需的字段
      if (!producerTransportInfo.id) {
        console.error('发送传输缺少id字段:', producerTransportInfo)
        throw new Error('发送传输数据格式错误：缺少id字段')
      }

      this.producerTransport = this.device.createSendTransport(producerTransportInfo)
      
      this.producerTransport.on('connect', async ({ dtlsParameters }, callback, errback) => {
        try {
          await apiManager.connectTransport(this.roomId, producerTransportInfo.id, dtlsParameters)
          callback()
        } catch (error) {
          errback(error)
        }
      })

      this.producerTransport.on('produce', async (parameters, callback, errback) => {
        try {
          const response = await apiManager.createProducer(
            this.roomId,
            producerTransportInfo.id,
            parameters.kind,
            parameters.rtpParameters,
            parameters.appData
          )

          console.log('创建生产者API响应:', response)

          // 从响应中提取生产者数据
          const producerData = response.producer || response
          console.log('生产者数据:', producerData)

          // 获取生产者ID
          const producerId = producerData.producerId || producerData.id
          console.log('提取的生产者ID:', producerId)

          // 保存生产者ID映射
          const mediaType = parameters.appData?.mediaType
          if (mediaType && producerId) {
            this.producerIds.set(mediaType, producerId)
            console.log(`保存生产者ID映射: ${mediaType} -> ${producerId}`)
          }

          callback({ id: producerId })
        } catch (error) {
          console.error('创建生产者失败:', error)
          errback(error)
        }
      })

      // 创建接收传输
      const consumerTransportResponse = await apiManager.createTransport(this.roomId, 'recv')
      console.log('接收传输响应:', consumerTransportResponse)

      const consumerTransportInfo = consumerTransportResponse.transport
      console.log('接收传输信息:', consumerTransportInfo)

      // 检查必需的字段
      if (!consumerTransportInfo.id) {
        console.error('接收传输缺少id字段:', consumerTransportInfo)
        throw new Error('接收传输数据格式错误：缺少id字段')
      }

      this.consumerTransport = this.device.createRecvTransport(consumerTransportInfo)
      
      this.consumerTransport.on('connect', async ({ dtlsParameters }, callback, errback) => {
        try {
          await apiManager.connectTransport(this.roomId, consumerTransportInfo.id, dtlsParameters)
          callback()
        } catch (error) {
          errback(error)
        }
      })

      console.log('传输通道创建成功')
    } catch (error) {
      console.error('创建传输通道失败:', error, this)
      throw error
    }
  }

  // 建立WebSocket连接
  async connectWebSocket() {
    return new Promise((resolve, reject) => {
      const config = require('../config/index.js').default
      const token = authManager.getAuthToken()
      
      // 构建WebSocket URL
      this.wsUrl = `${config.server.wsUrl}?token=${encodeURIComponent(token)}&roomId=${encodeURIComponent(this.roomId)}`
      
      try {
        console.log('WebSocket URL:', this.wsUrl);
        this.ws = new WebSocket(this.wsUrl)
        
        this.ws.onopen = () => {
          console.log('WebSocket连接成功')
          this.setupWebSocketListeners()
          resolve()
        }
        
        this.ws.onerror = (error) => {
          console.error('WebSocket连接失败:', error)
          reject(new Error('WebSocket连接失败'))
        }
        
        this.ws.onclose = (event) => {
          console.log('WebSocket连接关闭:', event.code, event.reason)
          this.isConnected = false
        }
      } catch (error) {
        console.error('创建WebSocket失败:', error)
        reject(error)
      }
    })
  }

  // 设置WebSocket事件监听
  setupWebSocketListeners() {
    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data)
        this.handleWebSocketMessage(message)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }
  }

  // 处理WebSocket消息
  handleWebSocketMessage(message) {
    const { type, data } = message
    
    switch (type) {
      case 'newProducers':
        this.handleNewProducers(data)
        break
      case 'consumerClosed':
        this.handleConsumerClosed(data)
        break
      case 'producerClosed':
        this.handleProducerClosed(data)
        break
      case 'userJoined':
        this.emit(Events.PEER_JOINED, data)
        break
      case 'userLeft':
        this.emit(Events.PEER_LEFT, data)
        break
      default:
        console.log('未知WebSocket消息类型:', type)
    }
  }

  // 获取房间内已存在的生产者
  async getExistingProducers() {
    try {
      console.log('获取房间内已存在的生产者...')
      const response = await apiManager.getProducers(this.roomId)
      console.log('已存在的生产者:', response.producers)

      if (response.producers && response.producers.length > 0) {
        for (const producer of response.producers) {
          try {
            await this.consume(producer.producerId || producer.producer_id)
          } catch (error) {
            console.error('消费已存在生产者失败:', producer, error)
          }
        }
      }
    } catch (error) {
      console.error('获取已存在生产者失败:', error)
    }
  }

  // 处理新生产者
  async handleNewProducers(data) {
    console.log('收到新生产者:', data)
    for (const { producer_id } of data.producers || []) {
      try {
        await this.consume(producer_id)
      } catch (error) {
        console.error('消费新生产者失败:', producer_id, error)
      }
    }
  }

  // 处理消费者关闭
  handleConsumerClosed(data) {
    console.log('消费者关闭:', data.consumer_id)
    this.removeConsumer(data.consumer_id)
  }

  // 处理生产者关闭
  handleProducerClosed(data) {
    console.log('其他用户关闭生产者:', data)
    this.emit(Events.PRODUCER_CLOSED, data)
  }

  // 获取设备列表
  async getDevices() {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices()

      this.audioDevices = devices
        .filter(device => device.kind === 'audioinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `音频设备 ${device.deviceId.slice(0, 8)}`
        }))

      this.videoDevices = devices
        .filter(device => device.kind === 'videoinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `视频设备 ${device.deviceId.slice(0, 8)}`
        }))

      return {
        audio: this.audioDevices,
        video: this.videoDevices
      }
    } catch (error) {
      console.error('获取设备列表失败:', error)
      return { audio: [], video: [] }
    }
  }

  // 检查权限
  canProduce(mediaType) {
    if (!this.userPermission) return false

    switch (this.userPermission) {
      case 'view_only':
        return false
      case 'audio_only':
        return mediaType === MediaType.AUDIO
      case 'video_audio':
      case 'full_access':
        return true
      default:
        return false
    }
  }

  // 生产媒体流
  async produce(mediaType, deviceId = null) {
    if (!this.canProduce(mediaType)) {
      throw new Error('没有权限生产此类型媒体')
    }

    if (!this.producerTransport) {
      throw new Error('生产者传输未初始化')
    }

    try {
      let stream

      if (mediaType === MediaType.AUDIO) {
        stream = await this.getAudioStream(deviceId)
      } else if (mediaType === MediaType.VIDEO) {
        stream = await this.getVideoStream(deviceId)
      } else if (mediaType === MediaType.SCREEN) {
        stream = await this.getScreenStream()
      } else {
        throw new Error('不支持的媒体类型')
      }

      const track = stream.getTracks()[0]
      const producer = await this.producerTransport.produce({
        track,
        appData: { mediaType }
      })

      console.log(`生产者创建成功: mediaType=${mediaType}`)
      console.log('Producer对象:', producer)
      console.log('Producer.id:', producer.id)
      console.log('Producer属性:', Object.getOwnPropertyNames(producer))

      this.producers.set(mediaType, producer)

      producer.on('trackended', () => {
        console.log(`${mediaType} track ended, producerId=${producer.id}`)
        this.closeProducer(mediaType)
      })

      producer.on('transportclose', () => {
        console.log(`${mediaType} transport closed, producerId=${producer.id}`)
        this.producers.delete(mediaType)
      })

      this.emit(Events.PRODUCER_ADDED, { mediaType, producer, stream })

      return { producer, stream }
    } catch (error) {
      console.error(`生产 ${mediaType} 失败:`, error)
      throw error
    }
  }

  // 获取音频流
  async getAudioStream(deviceId = null) {
    const constraints = {
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      }
    }

    if (deviceId) {
      constraints.audio.deviceId = { exact: deviceId }
    }

    return await navigator.mediaDevices.getUserMedia(constraints)
  }

  // 获取视频流
  async getVideoStream(deviceId = null) {
    const constraints = {
      video: {
        width: { ideal: 1280 },
        height: { ideal: 720 },
        frameRate: { ideal: 30 },
        facingMode: this.currentFacingMode
      }
    }

    if (deviceId) {
      constraints.video.deviceId = { exact: deviceId }
      // 如果指定了设备ID，移除 facingMode 约束
      delete constraints.video.facingMode
    }

    return await navigator.mediaDevices.getUserMedia(constraints)
  }

  // 获取屏幕共享流
  async getScreenStream() {
    return await navigator.mediaDevices.getDisplayMedia({
      video: {
        width: { ideal: 1920 },
        height: { ideal: 1080 },
        frameRate: { ideal: 15 }
      },
      audio: true
    })
  }

  // 切换摄像头（前置/后置）
  async switchCamera() {
    const videoProducer = this.producers.get(MediaType.VIDEO)
    if (!videoProducer) {
      throw new Error('视频未开启')
    }

    // 防止重复调用
    if (this._switchingCamera) {
      console.warn('摄像头正在切换中，请稍候...')
      return
    }

    this._switchingCamera = true
    let oldTrack = null

    try {
      console.log('开始切换摄像头，当前方向:', this.currentFacingMode)

      // 获取当前轨道，用于后续清理
      oldTrack = videoProducer.track
      console.log('当前视频轨道ID:', oldTrack?.id)

      // 切换摄像头方向
      this.currentFacingMode = this.currentFacingMode === 'user' ? 'environment' : 'user'
      console.log('切换到新方向:', this.currentFacingMode)

      // 重新获取视频流
      const newStream = await this.getVideoStream()
      const newTrack = newStream.getVideoTracks()[0]

      console.log('获取新视频流成功，新轨道ID:', newTrack.id)

      // 替换轨道
      await videoProducer.replaceTrack({ track: newTrack })
      console.log('视频轨道替换成功')

      // 停止旧轨道以释放资源
      if (oldTrack) {
        console.log('停止旧视频轨道:', oldTrack.id)
        oldTrack.stop()
      }

      // 触发事件，通知 UI 更新
      this.emit(Events.PRODUCER_ADDED, {
        mediaType: MediaType.VIDEO,
        producer: videoProducer,
        stream: newStream
      })

      console.log('摄像头切换完成')
      return newStream
    } catch (error) {
      console.error('切换摄像头失败:', error)
      // 如果切换失败，恢复原来的方向
      this.currentFacingMode = this.currentFacingMode === 'user' ? 'environment' : 'user'
      throw error
    } finally {
      this._switchingCamera = false
    }
  }

  // 关闭生产者
  async closeProducer(mediaType) {
    console.log(`尝试关闭生产者: mediaType=${mediaType}`)
    console.log(`当前生产者列表:`, Array.from(this.producers.keys()))

    const producer = this.producers.get(mediaType)
    if (!producer) {
      console.warn(`没有找到 ${mediaType} 类型的生产者`)
      return
    }

    try {
      // 详细调试producer对象
      console.log('=== Producer对象调试 ===')
      console.log('Producer:', producer)
      console.log('Producer类型:', typeof producer)
      console.log('Producer构造函数:', producer.constructor.name)
      console.log('Producer属性:', Object.getOwnPropertyNames(producer))
      console.log('Producer原型属性:', Object.getOwnPropertyNames(Object.getPrototypeOf(producer)))

      // 尝试各种可能的ID属性
      const possibleIds = [
        producer.id,
        producer._id,
        producer.producerId,
        producer._producerId,
        producer.uuid,
        producer._uuid
      ]

      console.log('可能的ID值:', possibleIds)

      // 找到第一个有效的ID
      let producerId = possibleIds.find(id => id && id !== 'undefined')

      // 如果从producer对象找不到ID，尝试从备用映射中获取
      if (!producerId) {
        producerId = this.producerIds.get(mediaType)
        console.log(`从备用映射获取生产者ID: ${producerId}`)
      }

      console.log(`最终使用的生产者ID: ${producerId}`)

      // 验证生产者ID
      if (!producerId || producerId === 'undefined') {
        console.error(`无法找到有效的生产者ID`)
        console.error(`Producer完整信息:`, JSON.stringify(producer, null, 2))
        console.error(`备用ID映射:`, Array.from(this.producerIds.entries()))
        throw new Error(`无法找到有效的生产者ID`)
      }

      // 如果是视频生产者，停止相关的轨道
      if (mediaType === MediaType.VIDEO && producer.track) {
        console.log('停止视频轨道:', producer.track.id)
        producer.track.stop()
      }

      // 先通知后端关闭生产者
      await apiManager.closeProducer(this.roomId, producerId)

      // 然后关闭本地生产者
      producer.close()
      this.producers.delete(mediaType)

      // 清理备用ID映射
      this.producerIds.delete(mediaType)

      console.log(`生产者 ${mediaType} 关闭成功`)
      this.emit(Events.PRODUCER_REMOVED, { mediaType })
    } catch (error) {
      console.error(`关闭 ${mediaType} 生产者失败:`, error)
      throw error
    }
  }

  // 消费远程流
  async consume(producerId) {
    if (!this.consumerTransport) {
      throw new Error('消费者传输未初始化')
    }

    try {
      const { rtpCapabilities } = this.device
      const response = await apiManager.createConsumer(
        this.roomId,
        this.consumerTransport.id,
        producerId,
        rtpCapabilities
      )

      console.log('创建消费者响应:', response)

      // 从响应中提取消费者数据
      const consumerData = response.consumer || response
      console.log('消费者数据:', consumerData)

      // 验证必要的字段
      if (!consumerData.consumerId) {
        throw new Error('响应中缺少 consumerId 字段')
      }
      if (!consumerData.kind) {
        throw new Error('响应中缺少 kind 字段')
      }
      if (!consumerData.rtpParameters) {
        throw new Error('响应中缺少 rtpParameters 字段')
      }

      const consumer = await this.consumerTransport.consume({
        id: consumerData.consumerId,
        producerId: producerId, // 使用原始的producerId
        kind: consumerData.kind,
        rtpParameters: consumerData.rtpParameters
      })

      this.consumers.set(consumer.id, consumer)

      consumer.on('transportclose', () => {
        console.log('Consumer transport closed')
        this.consumers.delete(consumer.id)
      })

      // 恢复消费者
      await apiManager.resumeConsumer(this.roomId, consumer.id)

      console.log('消费者已恢复，等待track准备就绪...')

      // 等待track准备就绪
      if (consumer.track && consumer.track.readyState !== 'live') {
        await new Promise((resolve) => {
          const checkTrack = () => {
            if (consumer.track.readyState === 'live') {
              console.log('消费者track已准备就绪')
              resolve()
            } else {
              console.log('等待消费者track准备就绪，当前状态:', consumer.track.readyState)
              setTimeout(checkTrack, 100)
            }
          }
          checkTrack()
        })
      }

      console.log('触发CONSUMER_ADDED事件:', {
        consumerId: consumer.id,
        kind: consumer.kind,
        trackState: consumer.track?.readyState
      })

      this.emit(Events.CONSUMER_ADDED, { consumer })

      return consumer
    } catch (error) {
      console.error('消费远程流失败:', error)
      throw error
    }
  }

  // 移除消费者
  async removeConsumer(consumerId) {
    const consumer = this.consumers.get(consumerId)
    if (consumer) {
      try {
        consumer.close()
        this.consumers.delete(consumerId)

        // 通知后端关闭消费者
        await apiManager.closeConsumer(this.roomId, consumerId)

        this.emit(Events.CONSUMER_REMOVED, { consumerId })
      } catch (error) {
        console.error('移除消费者失败:', error)
      }
    }
  }

  // 退出房间
  async exit() {
    try {
      // 关闭所有生产者
      for (const [mediaType] of this.producers) {
        await this.closeProducer(mediaType)
      }

      // 关闭所有消费者
      for (const [consumerId] of this.consumers) {
        await this.removeConsumer(consumerId)
      }

      console.log('已退出房间')
    } catch (error) {
      console.error('退出房间时出错:', error)
    }
  }

  // 断开连接
  disconnect() {
    try {
      if (this.ws) {
        this.ws.close()
        this.ws = null
      }

      if (this.producerTransport) {
        this.producerTransport.close()
        this.producerTransport = null
      }

      if (this.consumerTransport) {
        this.consumerTransport.close()
        this.consumerTransport = null
      }

      this.producers.clear()
      this.consumers.clear()

      this.isConnected = false
      this.emit(Events.ROOM_LEFT)

    } catch (error) {
      console.error('断开连接时出错:', error)
    }
  }
}

// 导出单例
const webrtcJavaClient = new WebRTCJavaClient()

export { webrtcJavaClient, MediaType, Events }
