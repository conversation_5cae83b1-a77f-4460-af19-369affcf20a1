{"version": 3, "file": "enhancedEvents.d.ts", "sourceRoot": "", "sources": ["../src/enhancedEvents.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,KAAK,QAAQ,EAAE,MAAM,oBAAoB,CAAC;AAKjE,KAAK,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;AAEpC,qBAAa,oBAAoB,CAChC,CAAC,SAAS,MAAM,GAAG,MAAM,CACxB,SAAQ,YAAY;;IAMZ,IAAI,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,MAAM,EACvC,SAAS,EAAE,CAAC,EACZ,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,GACX,OAAO;IAIV;;OAEG;IACH,QAAQ,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO;IAoBjE,EAAE,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,MAAM,EACrC,SAAS,EAAE,CAAC,EACZ,QAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAC/B,IAAI;IAME,GAAG,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,MAAM,EACtC,SAAS,EAAE,CAAC,EACZ,QAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAC/B,IAAI;IAME,WAAW,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,MAAM,EAC9C,SAAS,EAAE,CAAC,EACZ,QAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAC/B,IAAI;IAME,eAAe,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,MAAM,EAClD,SAAS,EAAE,CAAC,EACZ,QAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAC/B,IAAI;IAME,IAAI,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,MAAM,EACvC,SAAS,EAAE,CAAC,EACZ,QAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAC/B,IAAI;IAME,mBAAmB,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,MAAM,EACtD,SAAS,EAAE,CAAC,EACZ,QAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAC/B,IAAI;IAME,cAAc,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,MAAM,EACjD,SAAS,EAAE,CAAC,EACZ,QAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAC/B,IAAI;IAME,kBAAkB,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,MAAM,EAAE,SAAS,CAAC,EAAE,CAAC,GAAG,IAAI;IAMnE,aAAa,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,CAAC,GAAG,MAAM;IAI/D,SAAS,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,CAAC,GAAG,QAAQ,EAAE;IAI/D,YAAY,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,CAAC,GAAG,QAAQ,EAAE;CAG3E"}