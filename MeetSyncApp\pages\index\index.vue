<template>
  <view class="container">
    <text class="title">Hello UniApp!</text>
    <text class="desc">这是一个最简单的 uniapp 页面</text>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: 'Hello'
    }
  },
  onLoad() {
    console.log('页面加载成功')
  }
}
</script>

<style>
.container {
  padding: 20px;
  text-align: center;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title {
  font-size: 36px;
  color: white;
  font-weight: bold;
  margin-bottom: 20px;
}

.desc {
  font-size: 18px;
  color: white;
  opacity: 0.9;
}
</style>
