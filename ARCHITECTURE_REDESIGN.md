# MeetSync 架构重新设计

## 新架构概述

### 设计目标
1. **职责分离**: Java端负责业务逻辑，Node.js端专门处理WebRTC媒体流
2. **WebSocket通信**: Java和Node.js通过WebSocket进行实时通信
3. **统一认证**: 用户通过Java端登录，获得统一的身份认证
4. **媒体代理**: Java端作为前端和Node.js端的媒体请求代理

### 架构图
```
前端 (React/Vue)
    ↕ HTTP/WebSocket
Java服务端 (Spring Boot)
    ↕ WebSocket
Node.js服务端 (Mediasoup)
```

## 组件职责

### Java服务端 (MeetSyncJavaServer)
**端口**: 8080
**职责**:
- 用户认证和授权 (JWT)
- 用户管理 (注册、登录、权限)
- 房间管理 (创建、删除、权限控制)
- 数据库操作 (MySQL)
- API文档 (Swagger)
- 与Node.js端的WebSocket通信
- 媒体请求代理

**主要API**:
- `/api/auth/*` - 认证相关
- `/api/rooms/*` - 房间管理
- `/api/media/*` - 媒体请求代理 (新增)

### Node.js服务端 (MeetSyncMediaServer)
**端口**: 3016
**职责**:
- Mediasoup媒体服务器管理
- WebRTC传输处理
- 生产者/消费者管理
- 与Java端的WebSocket通信
- 纯媒体流处理，不涉及业务逻辑

**主要功能**:
- 创建/管理Mediasoup Worker
- 处理WebRTC传输
- 管理音视频生产者和消费者
- 响应Java端的媒体请求

## 通信协议

### Java ↔ Node.js WebSocket协议

#### 1. 连接建立
```json
// Java -> Node.js (连接时)
{
  "type": "auth",
  "data": {
    "serverToken": "java-server-secret-token"
  }
}

// Node.js -> Java (响应)
{
  "type": "auth_response",
  "success": true,
  "data": {
    "nodeId": "node-server-001"
  }
}
```

#### 2. 房间管理
```json
// Java -> Node.js (创建房间)
{
  "type": "create_room",
  "requestId": "req_123",
  "data": {
    "roomId": "room_001",
    "userId": 123,
    "userRole": "admin"
  }
}

// Node.js -> Java (响应)
{
  "type": "create_room_response",
  "requestId": "req_123",
  "success": true,
  "data": {
    "roomId": "room_001",
    "rtpCapabilities": {...}
  }
}
```

#### 3. 媒体传输
```json
// Java -> Node.js (创建传输)
{
  "type": "create_transport",
  "requestId": "req_124",
  "data": {
    "roomId": "room_001",
    "userId": 123,
    "direction": "send" // or "recv"
  }
}

// Node.js -> Java (响应)
{
  "type": "create_transport_response",
  "requestId": "req_124",
  "success": true,
  "data": {
    "transportId": "transport_001",
    "iceParameters": {...},
    "iceCandidates": [...],
    "dtlsParameters": {...}
  }
}
```

#### 4. 生产者管理
```json
// Java -> Node.js (创建生产者)
{
  "type": "create_producer",
  "requestId": "req_125",
  "data": {
    "roomId": "room_001",
    "userId": 123,
    "transportId": "transport_001",
    "kind": "video", // or "audio"
    "rtpParameters": {...}
  }
}

// Node.js -> Java (响应)
{
  "type": "create_producer_response",
  "requestId": "req_125",
  "success": true,
  "data": {
    "producerId": "producer_001",
    "kind": "video"
  }
}
```

### 前端 ↔ Java HTTP/WebSocket协议

#### 1. 用户认证 (HTTP)
```json
// POST /api/auth/login
{
  "username": "user123",
  "password": "password"
}

// Response
{
  "token": "jwt_token",
  "user": {...},
  "message": "登录成功"
}
```

#### 2. 房间操作 (HTTP)
```json
// POST /api/rooms
{
  "roomId": "room_001",
  "name": "测试房间",
  "isPublic": true
}

// Response
{
  "room": {...},
  "message": "房间创建成功"
}
```

#### 3. 媒体操作 (WebSocket)
```json
// 前端 -> Java (加入房间)
{
  "type": "join_room",
  "data": {
    "roomId": "room_001",
    "token": "jwt_token"
  }
}

// Java -> 前端 (房间信息)
{
  "type": "room_joined",
  "data": {
    "roomId": "room_001",
    "rtpCapabilities": {...},
    "participants": [...]
  }
}
```

## 实现状态

### ✅ 已完成: Node.js端重构
1. ✅ 移除用户认证和数据库相关代码
2. ✅ 创建与Java端的WebSocket连接 (`JavaServerConnector`)
3. ✅ 实现媒体请求处理器 (`MediaRoom`, `MediaServer`)
4. ✅ 保留纯Mediasoup功能
5. ✅ 更新package.json，移除不需要的依赖
6. ✅ 创建新的启动入口 (`media-server.js`)

### ✅ 已完成: Java端扩展
1. ✅ 添加WebSocket服务端接收Node.js连接 (`MediaServerWebSocketHandler`)
2. ✅ 创建媒体代理API (`MediaController`)
3. ✅ 实现请求转发机制 (`MediaProxyService`)
4. ✅ 添加媒体状态管理和通知处理
5. ✅ 更新Security配置允许WebSocket端点
6. ✅ 添加Swagger文档

### 🔄 进行中: 测试和验证
1. ✅ 创建启动脚本 (`start-both-servers.bat`)
2. ✅ 创建测试脚本 (`test-architecture.bat`)
3. 🔄 端到端功能测试
4. 🔄 WebSocket通信验证

### 📋 待完成: 前端适配
1. 修改前端API调用路径
2. 统一使用Java端API
3. 保持WebRTC客户端逻辑不变
4. 更新前端配置

## 优势

1. **清晰的职责分离**: 各服务专注自己的核心功能
2. **更好的可维护性**: 业务逻辑和媒体处理分离
3. **更强的扩展性**: 可以独立扩展各个服务
4. **统一的API入口**: 前端只需要对接Java端
5. **更好的安全性**: 认证统一在Java端处理

## 风险和挑战

1. **网络延迟**: Java和Node.js间的通信可能增加延迟
2. **复杂性增加**: 需要维护两个服务间的通信协议
3. **状态同步**: 需要确保两端状态一致性
4. **错误处理**: 需要处理跨服务的错误传播

## 配置示例

### Java端配置
```yaml
# application.yml
meetsync:
  media-server:
    url: ws://localhost:3016/java-ws
    auth-token: ${MEDIA_SERVER_TOKEN:your-secret-token}
    reconnect-interval: 5000
    timeout: 30000
```

### Node.js端配置
```javascript
// config.js
module.exports = {
  javaServer: {
    port: 3017, // WebSocket端口
    authToken: process.env.JAVA_SERVER_TOKEN || 'your-secret-token'
  },
  mediasoup: {
    // 现有配置保持不变
  }
}
```

这个新架构将提供更好的可维护性和扩展性，同时保持系统的高性能。

## 快速启动

### 1. 启动双服务
```bash
# 启动Java和Node.js服务
.\start-both-servers.bat
```

### 2. 验证架构
```bash
# 测试新架构
.\test-architecture.bat
```

### 3. 访问服务
- **Java API**: http://localhost:8080/api
- **Swagger文档**: http://localhost:8080/api/swagger-ui.html
- **媒体服务器健康检查**: http://localhost:3018/health

### 4. API使用示例

#### 用户注册
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"password123"}'
```

#### 用户登录
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"password123"}'
```

#### 创建房间
```bash
curl -X POST http://localhost:8080/api/rooms \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"roomId":"test-room","name":"测试房间","isPublic":true}'
```

#### 获取RTP能力
```bash
curl -X GET http://localhost:8080/api/media/rooms/test-room/rtp-capabilities \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 创建WebRTC传输
```bash
curl -X POST http://localhost:8080/api/media/rooms/test-room/transports?direction=send \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 文件结构

### Node.js媒体服务器
```
MeetSyncServer/
├── src/
│   ├── media-server.js          # 主入口
│   ├── config.js                # 配置文件
│   ├── connectors/
│   │   └── JavaServerConnector.js  # Java服务器连接器
│   └── media/
│       └── MediaRoom.js         # 媒体房间管理
├── package.json                 # 依赖配置
└── .env                        # 环境变量
```

### Java服务器
```
MeetSyncJavaServer/
├── src/main/java/org/meetsync/
│   ├── config/
│   │   └── WebSocketConfig.java     # WebSocket配置
│   ├── controller/
│   │   └── MediaController.java     # 媒体API控制器
│   ├── service/
│   │   └── MediaProxyService.java   # 媒体代理服务
│   └── websocket/
│       └── MediaServerWebSocketHandler.java  # WebSocket处理器
└── src/main/resources/
    └── application.yml              # 应用配置
```
