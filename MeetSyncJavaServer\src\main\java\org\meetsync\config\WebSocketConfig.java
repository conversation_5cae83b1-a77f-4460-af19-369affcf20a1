package org.meetsync.config;

import org.meetsync.websocket.MediaServerWebSocketHandler;
import org.meetsync.websocket.ClientWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private MediaServerWebSocketHandler mediaServerWebSocketHandler;

    @Autowired
    private ClientWebSocketHandler clientWebSocketHandler;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // Node.js媒体服务器连接端点（保持向后兼容）
        registry.addHandler(mediaServerWebSocketHandler, "/media-server-ws")
                .setAllowedOrigins("*"); // 在生产环境中应该限制来源

        // 前端客户端连接端点
        registry.addHandler(clientWebSocketHandler, "/media-ws")
                .setAllowedOrigins("*"); // 在生产环境中应该限制来源
    }
}
