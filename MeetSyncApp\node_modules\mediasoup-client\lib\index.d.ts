import debug from 'debug';
/**
 * Expose all types.
 */
export * as types from './types';
/**
 * Expose mediasoup-client version.
 */
export declare const version = "3.11.0";
/**
 * Expose Device class and device detector helpers.
 */
export { Device, detectDevice, detectDeviceAsync } from './Device';
/**
 * Expose parseScalabilityMode() function.
 */
export { parse as parseScalabilityMode } from './scalabilityModes';
/**
 * Expose all ORTC functions.
 */
export * as ortc from './ortc';
/**
 * Expose FakeHandler.
 */
export { FakeHandler } from './handlers/FakeHandler';
/**
 * Expose test/fakeParameters utils.
 */
export * as testFakeParameters from './test/fakeParameters';
/**
 * Expose the debug module.
 */
export { debug };
//# sourceMappingURL=index.d.ts.map