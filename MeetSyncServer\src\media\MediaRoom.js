/**
 * 媒体房间类
 * 管理单个房间的 Mediasoup 路由器和传输
 */

const config = require('../config')

class MediaRoom {
  constructor(roomId, worker, javaConnector = null) {
    this.roomId = roomId
    this.worker = worker
    this.router = null
    this.users = new Map() // userId -> UserMedia
    this.createdAt = new Date()
    this.javaConnector = javaConnector
  }

  async initialize() {
    // 创建路由器
    this.router = await this.worker.createRouter({
      mediaCodecs: config.mediasoup.router.mediaCodecs
    })

    console.log(`📺 媒体房间初始化完成: ${this.roomId}`)
  }

  async close() {
    console.log(`🗑️ 关闭媒体房间: ${this.roomId}`)
    
    // 关闭所有用户的媒体
    for (const [userId, userMedia] of this.users) {
      await userMedia.close()
    }
    this.users.clear()

    // 关闭路由器
    if (this.router) {
      this.router.close()
    }
  }

  // 创建传输
  async createTransport(userId, direction) {
    const userMedia = this.getOrCreateUserMedia(userId)
    
    const transport = await this.router.createWebRtcTransport({
      listenIps: config.mediasoup.webRtcTransport.listenIps,
      enableUdp: true,
      enableTcp: true,
      preferUdp: true,
      initialAvailableOutgoingBitrate: config.mediasoup.webRtcTransport.initialAvailableOutgoingBitrate
    })

    transport.on('dtlsstatechange', (dtlsState) => {
      if (dtlsState === 'closed') {
        console.log(`🔌 传输关闭 [${transport.id}] 用户: ${userId}`)
      }
    })

    transport.on('close', () => {
      console.log(`🗑️ 传输销毁 [${transport.id}] 用户: ${userId}`)
    })

    // 保存传输
    if (direction === 'send') {
      userMedia.sendTransport = transport
    } else {
      userMedia.recvTransport = transport
    }

    console.log(`🚛 创建传输 [${direction}] 用户: ${userId}, 传输ID: ${transport.id}`)
    
    return transport
  }

  // 连接传输
  async connectTransport(userId, transportId, dtlsParameters) {
    const userMedia = this.getUserMedia(userId)
    if (!userMedia) {
      throw new Error(`User ${userId} not found in room`)
    }

    const transport = userMedia.getTransport(transportId)
    if (!transport) {
      throw new Error(`Transport ${transportId} not found`)
    }

    await transport.connect({ dtlsParameters })
    console.log(`🔗 传输连接成功 [${transportId}] 用户: ${userId}`)
  }

  // 创建生产者
  async createProducer(userId, transportId, kind, rtpParameters) {
    const userMedia = this.getUserMedia(userId)
    if (!userMedia) {
      throw new Error(`User ${userId} not found in room`)
    }

    const transport = userMedia.getTransport(transportId)
    if (!transport) {
      throw new Error(`Transport ${transportId} not found`)
    }

    const producer = await transport.produce({
      kind,
      rtpParameters
    })

    producer.on('transportclose', () => {
      console.log(`🗑️ 生产者关闭 [${producer.id}] 用户: ${userId}`)
    })

    // 保存生产者
    userMedia.addProducer(producer)

    console.log(`🎬 创建生产者 [${kind}] 用户: ${userId}, 生产者ID: ${producer.id}`)

    // 通知Java服务器有新生产者创建，让其他用户知道
    if (this.javaConnector) {
      const newProducerInfo = {
        producer_id: producer.id,
        userId: userId,
        kind: producer.kind,
        paused: producer.paused
      }

      console.log(`📢 通知其他用户新生产者: ${producer.id}`)
      this.javaConnector.notifyNewProducers(this.roomId, [newProducerInfo])
    }

    return producer
  }

  // 创建消费者
  async createConsumer(userId, transportId, producerId, rtpCapabilities) {
    const userMedia = this.getUserMedia(userId)
    if (!userMedia) {
      console.warn(`⚠️ 用户 ${userId} 不存在于房间中，无法创建消费者`)
      return null
    }

    const transport = userMedia.getTransport(transportId)
    if (!transport) {
      console.warn(`⚠️ 传输 ${transportId} 不存在，无法创建消费者`)
      return null
    }

    // 检查是否可以消费
    try {
      if (!this.router.canConsume({ producerId, rtpCapabilities })) {
        console.warn(`⚠️ 无法消费生产者 ${producerId}，可能生产者已不存在或RTP能力不兼容`)
        return null
      }
    } catch (error) {
      console.warn(`⚠️ 检查消费能力时出错: ${error.message}，生产者 ${producerId} 可能已不存在`)
      return null
    }

    try {
      const consumer = await transport.consume({
        producerId,
        rtpCapabilities,
        paused: true // 开始时暂停
      })

      consumer.on('transportclose', () => {
        console.log(`🗑️ 消费者关闭 [${consumer.id}] 用户: ${userId}`)
        userMedia.removeConsumer(consumer.id)
      })

      consumer.on('producerclose', () => {
        console.log(`🎬 生产者关闭，消费者自动关闭 [${consumer.id}] 用户: ${userId}`)
        userMedia.removeConsumer(consumer.id)
      })

      // 保存消费者
      userMedia.addConsumer(consumer)

      console.log(`🎭 创建消费者 [${consumer.kind}] 用户: ${userId}, 消费者ID: ${consumer.id}`)

      return consumer
    } catch (error) {
      console.error(`创建消费者失败: ${error.message}，生产者 ${producerId} 可能已不存在`)
      return null
    }
  }

  // 关闭生产者
  async closeProducer(userId, producerId) {
    const userMedia = this.getUserMedia(userId)
    if (!userMedia) {
      console.warn(`⚠️ 用户 ${userId} 不存在于房间中，无法关闭生产者 ${producerId}`)
      // 返回成功，避免抛出错误
      return { success: true, message: 'User not found, producer already cleaned' }
    }

    const producer = userMedia.getProducer(producerId)
    if (!producer) {
      console.warn(`⚠️ 生产者 ${producerId} 不存在，可能已被清理`)
      // 返回成功，避免抛出错误
      return { success: true, message: 'Producer not found, already cleaned' }
    }

    const mediaType = producer.kind

    try {
      producer.close()
      userMedia.removeProducer(producerId)

      console.log(`🗑️ 关闭生产者 [${producerId}] 用户: ${userId}, 类型: ${mediaType}`)

      // 通知Java服务器生产者状态变化
      if (this.javaConnector) {
        this.javaConnector.notifyProducerStatusChange(this.roomId, userId, producerId, 'closed', mediaType, 'user_closed')
      }

      return { success: true, message: 'Producer closed successfully' }
    } catch (error) {
      console.error(`关闭生产者失败 [${producerId}]:`, error)
      // 即使关闭失败，也清理本地状态
      userMedia.removeProducer(producerId)
      return { success: true, message: 'Producer cleaned after error' }
    }
  }

  // 关闭消费者
  async closeConsumer(userId, consumerId) {
    const userMedia = this.getUserMedia(userId)
    if (!userMedia) {
      console.warn(`⚠️ 用户 ${userId} 不存在于房间中，无法关闭消费者 ${consumerId}`)
      return { success: true, message: 'User not found, consumer already cleaned' }
    }

    const consumer = userMedia.getConsumer(consumerId)
    if (!consumer) {
      console.warn(`⚠️ 消费者 ${consumerId} 不存在，可能已被清理`)
      return { success: true, message: 'Consumer not found, already cleaned' }
    }

    try {
      consumer.close()
      userMedia.removeConsumer(consumerId)

      console.log(`🗑️ 关闭消费者 [${consumerId}] 用户: ${userId}`)

      // 通知Java服务器消费者状态变化
      if (this.javaConnector) {
        this.javaConnector.notifyConsumerStatusChange(this.roomId, userId, consumerId, 'closed')
      }

      return { success: true, message: 'Consumer closed successfully' }
    } catch (error) {
      console.error(`关闭消费者失败 [${consumerId}]:`, error)
      // 即使关闭失败，也清理本地状态
      userMedia.removeConsumer(consumerId)
      return { success: true, message: 'Consumer cleaned after error' }
    }
  }

  // 恢复消费者
  async resumeConsumer(userId, consumerId) {
    const userMedia = this.getUserMedia(userId)
    if (!userMedia) {
      console.warn(`⚠️ 用户 ${userId} 不存在于房间中，无法恢复消费者 ${consumerId}`)
      return { success: false, message: 'User not found' }
    }

    const consumer = userMedia.getConsumer(consumerId)
    if (!consumer) {
      console.warn(`⚠️ 消费者 ${consumerId} 不存在，可能已被清理`)
      return { success: false, message: 'Consumer not found' }
    }

    try {
      await consumer.resume()
      console.log(`▶️ 恢复消费者 [${consumerId}] 用户: ${userId}`)
      return { success: true, message: 'Consumer resumed successfully' }
    } catch (error) {
      console.error(`恢复消费者失败 [${consumerId}]:`, error)
      return { success: false, message: 'Failed to resume consumer' }
    }
  }

  // 获取房间内的生产者列表（供其他用户消费）
  getProducersForUser(excludeUserId) {
    const producers = []

    for (const [userId, userMedia] of this.users) {
      if (userId !== excludeUserId) {
        for (const [producerId, producer] of userMedia.producers) {
          producers.push({
            producerId,
            userId,
            kind: producer.kind,
            paused: producer.paused
          })
        }
      }
    }

    return producers
  }

  // 清理用户的所有生产者（用户断开连接时调用）
  async cleanupUserProducers(userId) {
    const userMedia = this.getUserMedia(userId)
    if (!userMedia) {
      console.log(`👤 用户 ${userId} 不存在，无需清理生产者`)
      return 0
    }

    const producerIds = Array.from(userMedia.producers.keys())
    let cleanedCount = 0

    console.log(`🧹 开始清理用户 ${userId} 的生产者，共 ${producerIds.length} 个`)

    for (const producerId of producerIds) {
      try {
        const producer = userMedia.getProducer(producerId)
        if (producer) {
          const mediaType = producer.kind

          // 关闭生产者
          try {
            producer.close()
          } catch (closeError) {
            console.warn(`⚠️ 关闭生产者时出错 [${producerId}]:`, closeError.message)
          }

          userMedia.removeProducer(producerId)
          cleanedCount++

          console.log(`🗑️ 清理生产者 [${producerId}] 用户: ${userId}, 类型: ${mediaType}`)

          // 通知Java服务器生产者状态变化
          if (this.javaConnector) {
            try {
              this.javaConnector.notifyProducerStatusChange(
                this.roomId,
                userId,
                producerId,
                'closed',
                mediaType,
                'user_disconnected'
              )
            } catch (notifyError) {
              console.warn(`⚠️ 通知Java服务器失败 [${producerId}]:`, notifyError.message)
            }
          }
        } else {
          // 生产者不存在，但仍然从映射中移除
          userMedia.removeProducer(producerId)
          console.log(`🗑️ 生产者 [${producerId}] 已不存在，从映射中移除`)
        }
      } catch (error) {
        console.error(`清理生产者失败 [${producerId}]:`, error)
        // 即使出错，也尝试从映射中移除
        userMedia.removeProducer(producerId)
      }
    }

    console.log(`🧹 用户 ${userId} 生产者清理完成，共清理 ${cleanedCount} 个`)
    return cleanedCount
  }

  // 获取或创建用户媒体对象
  getOrCreateUserMedia(userId) {
    if (!this.users.has(userId)) {
      console.log(`👤 创建新用户媒体对象: ${userId}`)
      this.users.set(userId, new UserMedia(userId))
    } else {
      // 用户已存在，检查是否需要清理旧的传输（可能是多设备登录）
      const existingUserMedia = this.users.get(userId)
      if (existingUserMedia.hasTransports()) {
        console.warn(`⚠️ 用户 ${userId} 已有传输对象，可能是多设备登录，清理旧传输`)
        existingUserMedia.clearTransports()
      }
    }
    return this.users.get(userId)
  }

  getUserMedia(userId) {
    return this.users.get(userId)
  }

  // 移除用户
  async removeUser(userId) {
    const userMedia = this.users.get(userId)
    if (userMedia) {
      await userMedia.close()
      this.users.delete(userId)
      console.log(`👋 用户离开媒体房间: ${userId}`)
    }
  }

  // 获取房间统计信息
  getStats() {
    return {
      roomId: this.roomId,
      userCount: this.users.size,
      createdAt: this.createdAt,
      uptime: Date.now() - this.createdAt.getTime()
    }
  }
}

// 用户媒体类
class UserMedia {
  constructor(userId) {
    this.userId = userId
    this.sendTransport = null
    this.recvTransport = null
    this.producers = new Map() // producerId -> producer
    this.consumers = new Map() // consumerId -> consumer
  }

  getTransport(transportId) {
    if (this.sendTransport && this.sendTransport.id === transportId) {
      return this.sendTransport
    }
    if (this.recvTransport && this.recvTransport.id === transportId) {
      return this.recvTransport
    }
    return null
  }

  addProducer(producer) {
    this.producers.set(producer.id, producer)
  }

  getProducer(producerId) {
    return this.producers.get(producerId)
  }

  removeProducer(producerId) {
    this.producers.delete(producerId)
  }

  addConsumer(consumer) {
    this.consumers.set(consumer.id, consumer)
  }

  getConsumer(consumerId) {
    return this.consumers.get(consumerId)
  }

  removeConsumer(consumerId) {
    this.consumers.delete(consumerId)
  }

  async close() {
    // 关闭所有生产者
    for (const producer of this.producers.values()) {
      try {
        producer.close()
      } catch (error) {
        console.warn('关闭生产者时出错:', error.message)
      }
    }
    this.producers.clear()

    // 关闭所有消费者
    for (const consumer of this.consumers.values()) {
      try {
        consumer.close()
      } catch (error) {
        console.warn('关闭消费者时出错:', error.message)
      }
    }
    this.consumers.clear()

    // 关闭传输（只在用户完全离开时调用）
    if (this.sendTransport) {
      try {
        this.sendTransport.close()
        this.sendTransport = null
      } catch (error) {
        console.warn('关闭发送传输时出错:', error.message)
      }
    }
    if (this.recvTransport) {
      try {
        this.recvTransport.close()
        this.recvTransport = null
      } catch (error) {
        console.warn('关闭接收传输时出错:', error.message)
      }
    }

    console.log(`🧹 用户媒体对象已清理: ${this.userId}`)
  }

  // 检查是否有传输对象
  hasTransports() {
    return this.sendTransport !== null || this.recvTransport !== null
  }

  // 清理传输对象（用于多设备登录时的冲突处理）
  clearTransports() {
    console.log(`🔄 清理用户 ${this.userId} 的传输对象`)

    if (this.sendTransport) {
      try {
        this.sendTransport.close()
      } catch (error) {
        console.warn('关闭发送传输时出错:', error.message)
      }
      this.sendTransport = null
    }

    if (this.recvTransport) {
      try {
        this.recvTransport.close()
      } catch (error) {
        console.warn('关闭接收传输时出错:', error.message)
      }
      this.recvTransport = null
    }

    console.log(`✅ 用户 ${this.userId} 的传输对象已清理`)
  }
}

module.exports = MediaRoom
