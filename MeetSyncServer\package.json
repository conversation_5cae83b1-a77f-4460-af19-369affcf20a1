{"name": "meetsync-media-server", "version": "1.0.0", "description": "MeetSync Media Server - WebRTC媒体流处理服务", "main": "src/media-server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node src/media-server.js", "dev": "nodemon src/media-server.js", "lint": "npx prettier --write ."}, "author": "", "license": "ISC", "dependencies": {"dotenv": "^16.5.0", "express": "^4.19.2", "mediasoup": "^3.14.1", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.1.10", "prettier": "3.2.5"}}