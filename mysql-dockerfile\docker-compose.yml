# MeetSync 服务端 Docker Compose 配置
version: '3.8'

services:
  # MySQL 数据库服务 - 配置为公共服务
  mysql:
    build:
      context: ./
      dockerfile: Dockerfile
    container_name: mysql-server
    restart: unless-stopped
    ports:
      - "3306:3306"  # 暴露给主机和其他容器
    env_file:
      - .env
    volumes:
      - mysql-data:/var/lib/mysql
      - mysql-logs:/var/log/mysql
      - ./init-scripts:/docker-entrypoint-initdb.d:ro  # 初始化脚本
    networks:
      - default-network  # 使用默认网络
    command:
      - --default-authentication-plugin=mysql_native_password
      - --character-set-server=utf8mb4
      - --collation-server=utf8mb4_unicode_ci
      - --bind-address=0.0.0.0  # 允许从任何 IP 连接
    cap_add:
      - SYS_NICE
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

# 数据卷定义
volumes:
  mysql-data:
    driver: local
    name: mysql-data  # 通用名称
  mysql-logs:
    driver: local
    name: mysql-logs  # 通用名称


# 网络定义
networks:
  default-network:
    driver: bridge
    name: default-network  # 默认公共网络
