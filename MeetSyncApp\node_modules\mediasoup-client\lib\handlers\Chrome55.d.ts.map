{"version": 3, "file": "Chrome55.d.ts", "sourceRoot": "", "sources": ["../../src/handlers/Chrome55.ts"], "names": [], "mappings": "AAOA,OAAO,EACN,KAAK,cAAc,EACnB,gBAAgB,EAChB,KAAK,iBAAiB,EACtB,KAAK,kBAAkB,EACvB,KAAK,iBAAiB,EACtB,KAAK,qBAAqB,EAC1B,KAAK,oBAAoB,EACzB,KAAK,6BAA6B,EAClC,KAAK,4BAA4B,EACjC,KAAK,gCAAgC,EACrC,KAAK,+BAA+B,EACpC,MAAM,oBAAoB,CAAC;AAE5B,OAAO,KAAK,EAAE,aAAa,EAAY,MAAM,cAAc,CAAC;AAC5D,OAAO,KAAK,EAAE,eAAe,EAAiB,MAAM,kBAAkB,CAAC;AACvE,OAAO,KAAK,EAAE,gBAAgB,EAAwB,MAAM,mBAAmB,CAAC;AAOhF,qBAAa,QAAS,SAAQ,gBAAgB;IAE7C,OAAO,CAAC,UAAU,CAAC,CAAkB;IAErC,OAAO,CAAC,UAAU,CAAC,CAAY;IAE/B,OAAO,CAAC,2BAA2B,CAAC,CAAmC;IAGvE,OAAO,CAAC,iCAAiC,CAAC,CAAmC;IAG7E,OAAO,CAAC,oBAAoB,CAAC,CAAW;IAExC,OAAO,CAAC,GAAG,CAAM;IAEjB,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAqB;IAEjD,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAC1B;IAEX,OAAO,CAAC,gBAAgB,CAAK;IAG7B,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAMtB;IAEd,OAAO,CAAC,2BAA2B,CAAS;IAE5C,OAAO,CAAC,qBAAqB,CAAK;IAElC,OAAO,CAAC,eAAe,CAAS;IAEhC;;OAEG;IACH,MAAM,CAAC,aAAa,IAAI,cAAc;;IAQtC,IAAI,IAAI,IAAI,MAAM,CAEjB;IAED,KAAK,IAAI,IAAI;IAaP,wBAAwB,IAAI,OAAO,CAAC,eAAe,CAAC;IAoCpD,yBAAyB,IAAI,OAAO,CAAC,gBAAgB,CAAC;IAQ5D,GAAG,CAAC,EACH,SAAS,EACT,aAAa,EACb,aAAa,EACb,cAAc,EACd,cAAc,EACd,UAAU,EACV,kBAAkB,EAClB,kBAAkB,EAClB,sBAAsB,EACtB,uBAAuB,GACvB,EAAE,iBAAiB,GAAG,IAAI;IAuGrB,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAU3D,UAAU,CAAC,aAAa,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC;IAiDvD,iBAAiB,IAAI,OAAO,CAAC,cAAc,CAAC;IAI5C,IAAI,CAAC,EACV,KAAK,EACL,SAAS,EACT,YAAY,EACZ,KAAK,GACL,EAAE,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAsI5C,WAAW,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAsD3C,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAK5C,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAI7C,YAAY,CAEjB,OAAO,EAAE,MAAM,EAEf,KAAK,EAAE,gBAAgB,GAAG,IAAI,GAC5B,OAAO,CAAC,IAAI,CAAC;IAIV,kBAAkB,CAEvB,OAAO,EAAE,MAAM,EAEf,YAAY,EAAE,MAAM,GAClB,OAAO,CAAC,IAAI,CAAC;IAKV,wBAAwB,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAKrE,cAAc,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC;IAIxD,eAAe,CAAC,EACrB,OAAO,EACP,iBAAiB,EACjB,cAAc,EACd,KAAK,EACL,QAAQ,GACR,EAAE,6BAA6B,GAAG,OAAO,CAAC,4BAA4B,CAAC;IAoElE,OAAO,CACZ,WAAW,EAAE,qBAAqB,EAAE,GAClC,OAAO,CAAC,oBAAoB,EAAE,CAAC;IAuF5B,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAqChD,cAAc,CAEnB,QAAQ,EAAE,MAAM,EAAE,GAChB,OAAO,CAAC,IAAI,CAAC;IAIV,eAAe,CAEpB,QAAQ,EAAE,MAAM,EAAE,GAChB,OAAO,CAAC,IAAI,CAAC;IAKV,gBAAgB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC;IAI1D,kBAAkB,CAAC,EACxB,oBAAoB,EACpB,KAAK,EACL,QAAQ,GACR,EAAE,gCAAgC,GAAG,OAAO,CAAC,+BAA+B,CAAC;YA0DhE,cAAc;IAgC5B,OAAO,CAAC,mBAAmB;IAQ3B,OAAO,CAAC,mBAAmB;CAO3B"}