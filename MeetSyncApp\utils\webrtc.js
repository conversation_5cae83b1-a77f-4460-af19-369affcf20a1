/**
 * WebRTC 客户端工具类
 * 基于 mediasoup-client 的 uniapp 适配版本
 */
import io from 'socket.io-client'
import * as mediasoupClient from 'mediasoup-client'
import authManager from './auth.js'

// 媒体类型枚举
const MediaType = {
  AUDIO: 'audio',
  VIDEO: 'video',
  SCREEN: 'screen'
}

// 事件类型
const Events = {
  ROOM_JOINED: 'roomJoined',
  ROOM_LEFT: 'roomLeft',
  PEER_JOINED: 'peerJoined',
  PEER_LEFT: 'peerLeft',
  PRODUCER_ADDED: 'producerAdded',
  PRODUCER_REMOVED: 'producerRemoved',
  PRODUCER_CLOSED: 'producerClosed',
  CONSUMER_ADDED: 'consumerAdded',
  CONSUMER_REMOVED: 'consumerRemoved',
  PARTICIPANT_COUNT_UPDATED: 'participantCountUpdated'
}

class WebRTCClient {
  constructor() {
    this.socket = null
    this.device = null
    this.producerTransport = null
    this.consumerTransport = null
    
    this.roomId = null
    this.isConnected = false
    
    // 生产者和消费者管理
    this.producers = new Map()
    this.consumers = new Map()
    
    // 事件监听器
    this.eventListeners = new Map()
    
    // 用户信息
    this.user = null
    this.userPermission = null
    
    // 设备信息
    this.audioDevices = []
    this.videoDevices = []
    this.selectedAudioDevice = null
    this.selectedVideoDevice = null
    
    // 当前摄像头方向（前置/后置）
    this.currentFacingMode = 'user' // 'user' 前置, 'environment' 后置

    // 切换状态控制
    this._switchingCamera = false

    this.initEventListeners()
  }

  initEventListeners() {
    Object.values(Events).forEach(event => {
      this.eventListeners.set(event, [])
    })
  }

  // 事件监听
  on(event, callback) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).push(callback)
    }
  }

  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event)
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  // 移除所有事件监听器
  removeAllListeners() {
    this.eventListeners.forEach((listeners) => {
      listeners.length = 0 // 清空数组
    })
    console.log('已清除所有事件监听器')
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`Event listener error for ${event}:`, error)
        }
      })
    }
  }

  // 连接到房间
  async connect(roomId, password = null) {
    try {
      this.user = authManager.getCurrentUser()
      if (!this.user) {
        throw new Error('用户未登录')
      }

      this.roomId = roomId

      // 检查是否是重连（页面刷新后）
      const isReconnecting = this.isConnected === false && (this.producers.size > 0 || this.consumers.size > 0)
      if (isReconnecting) {
        console.log('检测到重连，清理旧状态...')
        // 清理旧的生产者和消费者状态，但保留引用用于后续恢复
        this.producers.clear()
        this.consumers.clear()
      }

      // 建立 Socket.IO 连接
      await this.connectSocket()

      // 创建房间
      await this.createRoom(roomId)

      // 加入房间
      await this.joinRoom(roomId, password)

      // 初始化 mediasoup Device
      await this.initDevice()

      // 创建传输通道
      await this.createTransports()

      // 消费房间中现有的生产者
      await this.consumeExistingProducers()

      this.isConnected = true
      this.emit(Events.ROOM_JOINED, { roomId })

      return true
    } catch (error) {
      console.error('连接房间失败:', error)
      throw error
    }
  }

  // 建立 Socket.IO 连接
  async connectSocket() {
    const token = authManager.getAuthToken()
    const baseUrl = getApp().globalData?.baseUrl || 'https://your-domain.com'

    return new Promise((resolve, reject) => {
      this.socket = io(baseUrl, {
        auth: {
          token: token
        },
        transports: ['websocket', 'polling'],
        // SSL 兼容性配置
        rejectUnauthorized: false,
        secure: true,
        // 连接配置
        timeout: 30000,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        // 强制使用 polling 作为备选
        upgrade: true,
        rememberUpgrade: false
      })

      this.socket.on('connect', () => {
        console.log('Socket.IO 连接成功')
        this.setupSocketListeners()
        resolve()
      })

      this.socket.on('connect_error', (error) => {
        console.error('Socket.IO 连接失败:', error)
        reject(new Error('网络连接失败'))
      })

      this.socket.on('disconnect', (reason) => {
        console.log('Socket.IO 断开连接:', reason)
        this.isConnected = false
      })
    })
  }

  // 设置 Socket 事件监听
  setupSocketListeners() {
    // 新生产者
    this.socket.on('newProducers', async (data) => {
      console.log('收到新生产者:', data)
      for (const { producer_id } of data) {
        await this.consume(producer_id)
      }
    })

    // 消费者关闭
    this.socket.on('consumerClosed', ({ consumer_id }) => {
      console.log('消费者关闭:', consumer_id)
      this.removeConsumer(consumer_id)
    })

    // 生产者关闭（其他用户关闭了音视频）
    this.socket.on('producerClosed', ({ producer_id, socket_id, username, mediaType }) => {
      console.log('其他用户关闭生产者:', { producer_id, socket_id, username, mediaType })

      // 触发事件通知 UI 更新用户状态
      this.emit(Events.PRODUCER_CLOSED, {
        producer_id,
        socket_id,
        username,
        mediaType
      })
    })

    // 用户加入
    this.socket.on('userJoined', (data) => {
      console.log('用户加入:', data)
      this.emit(Events.PEER_JOINED, data)
      if (data.participantCount !== undefined) {
        this.emit(Events.PARTICIPANT_COUNT_UPDATED, data.participantCount)
      }
    })

    // 用户离开
    this.socket.on('userLeft', (data) => {
      console.log('用户离开:', data)
      this.emit(Events.PEER_LEFT, data)
      if (data.participantCount !== undefined) {
        this.emit(Events.PARTICIPANT_COUNT_UPDATED, data.participantCount)
      }
    })
  }

  // Socket 请求封装
  socketRequest(type, data = {}) {
    return new Promise((resolve, reject) => {
      this.socket.emit(type, data, (response) => {
        if (response.error) {
          reject(new Error(response.error))
        } else {
          resolve(response)
        }
      })
    })
  }

  // 创建房间
  async createRoom(roomId) {
    try {
      await this.socketRequest('createRoom', { room_id: roomId })
    } catch (error) {
      console.log('创建房间失败 (可能已存在):', error.message)
    }
  }

  // 加入房间
  async joinRoom(roomId, password = null) {
    const joinData = { room_id: roomId }
    if (password) {
      joinData.password = password
    }

    const response = await this.socketRequest('join', joinData)
    console.log('加入房间成功:', response)

    this.userPermission = response.userPermission

    // 更新参与者数量
    if (response.peers) {
      try {
        const peersData = JSON.parse(response.peers)
        this.emit(Events.PARTICIPANT_COUNT_UPDATED, peersData.length)
      } catch (error) {
        console.error('解析参与者数据失败:', error)
      }
    }

    return response
  }

  // 获取房间中现有的生产者并消费
  async consumeExistingProducers() {
    try {
      console.log('获取房间中现有的生产者...')
      const response = await this.socketRequest('getProducers')

      if (response.producers && response.producers.length > 0) {
        console.log('发现现有生产者:', response.producers.length, '个')

        for (const producerInfo of response.producers) {
          try {
            console.log('开始消费生产者:', producerInfo.producer_id)
            const consumer = await this.consume(producerInfo.producer_id)
            console.log('成功消费生产者:', producerInfo.producer_id, '类型:', consumer.kind)
          } catch (error) {
            console.error('消费生产者失败:', producerInfo.producer_id, error)
          }
        }
      } else {
        console.log('房间中暂无其他生产者')
      }
    } catch (error) {
      console.error('获取现有生产者失败:', error)
    }
  }

  // 初始化 mediasoup Device
  async initDevice() {
    const routerRtpCapabilities = await this.socketRequest('getRouterRtpCapabilities')
    
    this.device = new mediasoupClient.Device()
    await this.device.load({ routerRtpCapabilities })
    
    console.log('mediasoup Device 初始化成功')
  }

  // 创建传输通道
  async createTransports() {
    // 创建发送传输
    const producerTransportInfo = await this.socketRequest('createWebRtcTransport', {
      forceTcp: false,
      rtpCapabilities: this.device.rtpCapabilities
    })

    this.producerTransport = this.device.createSendTransport(producerTransportInfo)
    
    this.producerTransport.on('connect', async ({ dtlsParameters }, callback, errback) => {
      try {
        await this.socketRequest('connectTransport', {
          transport_id: producerTransportInfo.id,
          dtlsParameters
        })
        callback()
      } catch (error) {
        errback(error)
      }
    })

    this.producerTransport.on('produce', async (parameters, callback, errback) => {
      try {
        const { producer_id } = await this.socketRequest('produce', {
          producerTransportId: producerTransportInfo.id,
          kind: parameters.kind,
          rtpParameters: parameters.rtpParameters,
          appData: parameters.appData
        })
        callback({ id: producer_id })
      } catch (error) {
        errback(error)
      }
    })

    // 创建接收传输
    const consumerTransportInfo = await this.socketRequest('createWebRtcTransport', {
      forceTcp: false,
      rtpCapabilities: this.device.rtpCapabilities
    })

    this.consumerTransport = this.device.createRecvTransport(consumerTransportInfo)
    
    this.consumerTransport.on('connect', async ({ dtlsParameters }, callback, errback) => {
      try {
        await this.socketRequest('connectTransport', {
          transport_id: consumerTransportInfo.id,
          dtlsParameters
        })
        callback()
      } catch (error) {
        errback(error)
      }
    })

    console.log('传输通道创建成功')
  }

  // 获取设备列表
  async getDevices() {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices()
      
      this.audioDevices = devices
        .filter(device => device.kind === 'audioinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `音频设备 ${device.deviceId.slice(0, 8)}`
        }))
        
      this.videoDevices = devices
        .filter(device => device.kind === 'videoinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `视频设备 ${device.deviceId.slice(0, 8)}`
        }))
        
      return {
        audio: this.audioDevices,
        video: this.videoDevices
      }
    } catch (error) {
      console.error('获取设备列表失败:', error)
      return { audio: [], video: [] }
    }
  }

  // 检查权限
  canProduce(mediaType) {
    if (!this.userPermission) return false
    
    switch (this.userPermission) {
      case 'view_only':
        return false
      case 'audio_only':
        return mediaType === MediaType.AUDIO
      case 'video_audio':
      case 'full_access':
        return true
      default:
        return false
    }
  }

  // 生产媒体流
  async produce(mediaType, deviceId = null) {
    if (!this.canProduce(mediaType)) {
      throw new Error('没有权限生产此类型媒体')
    }

    if (!this.producerTransport) {
      throw new Error('生产者传输未初始化')
    }

    try {
      let stream

      if (mediaType === MediaType.AUDIO) {
        stream = await this.getAudioStream(deviceId)
      } else if (mediaType === MediaType.VIDEO) {
        stream = await this.getVideoStream(deviceId)
      } else if (mediaType === MediaType.SCREEN) {
        stream = await this.getScreenStream()
      } else {
        throw new Error('不支持的媒体类型')
      }

      const track = stream.getTracks()[0]
      const producer = await this.producerTransport.produce({
        track,
        appData: { mediaType }
      })

      this.producers.set(mediaType, producer)

      producer.on('trackended', () => {
        console.log(`${mediaType} track ended`)
        this.closeProducer(mediaType)
      })

      producer.on('transportclose', () => {
        console.log(`${mediaType} transport closed`)
        this.producers.delete(mediaType)
      })

      this.emit(Events.PRODUCER_ADDED, { mediaType, producer, stream })

      return { producer, stream }
    } catch (error) {
      console.error(`生产 ${mediaType} 失败:`, error)
      throw error
    }
  }

  // 获取音频流
  async getAudioStream(deviceId = null) {
    const constraints = {
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      }
    }

    if (deviceId) {
      constraints.audio.deviceId = { exact: deviceId }
    }

    return await navigator.mediaDevices.getUserMedia(constraints)
  }

  // 获取视频流
  async getVideoStream(deviceId = null) {
    const constraints = {
      video: {
        width: { ideal: 1280 },
        height: { ideal: 720 },
        frameRate: { ideal: 30 },
        facingMode: this.currentFacingMode
      }
    }

    if (deviceId) {
      constraints.video.deviceId = { exact: deviceId }
      // 如果指定了设备ID，移除 facingMode 约束
      delete constraints.video.facingMode
    }

    return await navigator.mediaDevices.getUserMedia(constraints)
  }

  // 获取屏幕共享流
  async getScreenStream() {
    return await navigator.mediaDevices.getDisplayMedia({
      video: {
        width: { ideal: 1920 },
        height: { ideal: 1080 },
        frameRate: { ideal: 15 }
      },
      audio: true
    })
  }

  // 切换摄像头（前置/后置）
  async switchCamera() {
    const videoProducer = this.producers.get(MediaType.VIDEO)
    if (!videoProducer) {
      throw new Error('视频未开启')
    }

    // 防止重复调用
    if (this._switchingCamera) {
      console.warn('摄像头正在切换中，请稍候...')
      return
    }

    this._switchingCamera = true
    let oldTrack = null

    try {
      console.log('开始切换摄像头，当前方向:', this.currentFacingMode)

      // 获取当前轨道，用于后续清理
      oldTrack = videoProducer.track
      console.log('当前视频轨道ID:', oldTrack?.id)

      // 切换摄像头方向
      this.currentFacingMode = this.currentFacingMode === 'user' ? 'environment' : 'user'
      console.log('切换到新方向:', this.currentFacingMode)

      // 重新获取视频流
      const newStream = await this.getVideoStream()
      const newTrack = newStream.getVideoTracks()[0]

      console.log('获取新视频流成功，新轨道ID:', newTrack.id)

      // 替换轨道
      await videoProducer.replaceTrack({ track: newTrack })
      console.log('视频轨道替换成功')

      // 停止旧轨道以释放资源
      if (oldTrack) {
        console.log('停止旧视频轨道:', oldTrack.id)
        oldTrack.stop()
      }

      // 触发事件，通知 UI 更新
      this.emit(Events.PRODUCER_ADDED, {
        mediaType: MediaType.VIDEO,
        producer: videoProducer,
        stream: newStream
      })

      console.log('摄像头切换完成')
      return newStream
    } catch (error) {
      console.error('切换摄像头失败:', error)
      // 如果切换失败，恢复原来的方向
      this.currentFacingMode = this.currentFacingMode === 'user' ? 'environment' : 'user'
      throw error
    } finally {
      this._switchingCamera = false
    }
  }

  // 关闭生产者
  async closeProducer(mediaType) {
    const producer = this.producers.get(mediaType)
    if (!producer) return

    try {
      // 如果是视频生产者，停止相关的轨道
      if (mediaType === MediaType.VIDEO && producer.track) {
        console.log('停止视频轨道:', producer.track.id)
        producer.track.stop()
      }

      producer.close()
      this.producers.delete(mediaType)

      await this.socketRequest('closeProducer', {
        producer_id: producer.id
      })

      this.emit(Events.PRODUCER_REMOVED, { mediaType })
    } catch (error) {
      console.error(`关闭 ${mediaType} 生产者失败:`, error)
    }
  }

  // 消费远程流
  async consume(producerId) {
    if (!this.consumerTransport) {
      throw new Error('消费者传输未初始化')
    }

    try {
      const { rtpCapabilities } = this.device
      const data = await this.socketRequest('consume', {
        consumerTransportId: this.consumerTransport.id,
        producerId,
        rtpCapabilities
      })

      const consumer = await this.consumerTransport.consume({
        id: data.id,
        producerId: data.producerId,
        kind: data.kind,
        rtpParameters: data.rtpParameters
      })

      this.consumers.set(consumer.id, consumer)

      consumer.on('transportclose', () => {
        console.log('Consumer transport closed')
        this.consumers.delete(consumer.id)
      })

      // 恢复消费者
      await this.socketRequest('resume', {
        consumerId: consumer.id
      })

      this.emit(Events.CONSUMER_ADDED, { consumer })

      return consumer
    } catch (error) {
      console.error('消费远程流失败:', error)
      throw error
    }
  }

  // 移除消费者
  removeConsumer(consumerId) {
    const consumer = this.consumers.get(consumerId)
    if (consumer) {
      consumer.close()
      this.consumers.delete(consumerId)
      this.emit(Events.CONSUMER_REMOVED, { consumerId })
    }
  }

  // 退出房间
  async exit() {
    try {
      if (this.socket) {
        // 发送退出房间请求并等待响应
        await this.socketRequest('exitRoom', {})
        console.log('已退出房间')
      }
    } catch (error) {
      console.error('退出房间时出错:', error)
    }
  }

  // 断开连接
  disconnect() {
    try {
      if (this.socket) {
        this.socket.emit('exitRoom')
        this.socket.disconnect()
        this.socket = null
      }

      if (this.producerTransport) {
        this.producerTransport.close()
        this.producerTransport = null
      }

      if (this.consumerTransport) {
        this.consumerTransport.close()
        this.consumerTransport = null
      }

      this.producers.clear()
      this.consumers.clear()

      this.isConnected = false
      this.emit(Events.ROOM_LEFT)

    } catch (error) {
      console.error('断开连接时出错:', error)
    }
  }
}

// 导出单例
const webrtcClient = new WebRTCClient()

export { webrtcClient, MediaType, Events }
