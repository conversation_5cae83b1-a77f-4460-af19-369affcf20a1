package org.meetsync.websocket;

import ch.qos.logback.core.joran.util.beans.BeanUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.meetsync.service.MediaProxyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.TimeUnit;

@Component
public class MediaServerWebSocketHandler implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(MediaServerWebSocketHandler.class);

    @Autowired
    @Lazy
    private MediaProxyService mediaProxyService;
    
    @Value("${meetsync.media-server.auth-token:media-server-secret-token}")
    private String expectedAuthToken;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private WebSocketSession mediaServerSession = null;
    private final ConcurrentHashMap<String, CompletableFuture<JsonNode>> pendingRequests = new ConcurrentHashMap<>();
    private final ReentrantLock sendLock = new ReentrantLock();
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        logger.info("媒体服务器连接建立: {}", session.getId());
        
        // 验证认证令牌
        String authHeader = session.getHandshakeHeaders().getFirst("Authorization");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            logger.warn("媒体服务器连接缺少认证令牌");
            session.close(CloseStatus.NOT_ACCEPTABLE.withReason("Missing auth token"));
            return;
        }
        
        String token = authHeader.substring(7);
        if (!expectedAuthToken.equals(token)) {
            logger.warn("媒体服务器连接认证令牌无效");
            session.close(CloseStatus.NOT_ACCEPTABLE.withReason("Invalid auth token"));
            return;
        }
        
        this.mediaServerSession = session;

        mediaProxyService.setMediaServerConnected(true);
        
        logger.info("✅ 媒体服务器认证成功并连接");
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        if (!(message instanceof TextMessage)) {
            return;
        }
        
        String payload = ((TextMessage) message).getPayload();
        logger.debug("收到媒体服务器消息: {}", payload);
        
        try {
            JsonNode messageNode = objectMapper.readTree(payload);
            String type = messageNode.get("type").asText();
            
            switch (type) {
                case "response":
                    handleResponse(messageNode);
                    break;
                case "notification":
                    handleNotification(messageNode);
                    break;
                case "heartbeat":
                    handleHeartbeat(session);
                    break;
                case "pong":
                    // 心跳响应，无需处理
                    break;
                default:
                    logger.warn("未知消息类型: {}", type);
            }
            
        } catch (Exception e) {
            logger.error("处理媒体服务器消息失败", e);
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        logger.error("媒体服务器连接传输错误", exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        logger.info("媒体服务器连接关闭: {} - {}", closeStatus.getCode(), closeStatus.getReason());
        
        if (this.mediaServerSession == session) {
            this.mediaServerSession = null;
            mediaProxyService.setMediaServerConnected(false);
            
            // 取消所有待处理的请求
            for (CompletableFuture<JsonNode> future : pendingRequests.values()) {
                future.completeExceptionally(new RuntimeException("媒体服务器连接已断开"));
            }
            pendingRequests.clear();
        }
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
    
    /**
     * 向媒体服务器发送请求
     */
    public CompletableFuture<JsonNode> sendRequest(String method, JsonNode data) {
        if (mediaServerSession == null || !mediaServerSession.isOpen()) {
            return CompletableFuture.failedFuture(new RuntimeException("媒体服务器未连接"));
        }
        
        String requestId = generateRequestId();
        CompletableFuture<JsonNode> future = new CompletableFuture<>();
        
        // 设置超时
        future.orTimeout(30, TimeUnit.SECONDS);
        
        pendingRequests.put(requestId, future);
        
        try {
            JsonNode request = objectMapper.createObjectNode()
                    .put("type", "request")
                    .put("requestId", requestId)
                    .put("method", method)
                    .set("data", data);

            // 使用同步锁防止并发写入
            sendLock.lock();
            try {
                if (mediaServerSession != null && mediaServerSession.isOpen()) {
                    mediaServerSession.sendMessage(new TextMessage(objectMapper.writeValueAsString(request)));
                    logger.debug("发送请求到媒体服务器: {} [{}]", method, requestId);
                } else {
                    throw new IOException("媒体服务器连接已断开");
                }
            } finally {
                sendLock.unlock();
            }

        } catch (IOException e) {
            pendingRequests.remove(requestId);
            future.completeExceptionally(e);
        }
        
        return future;
    }
    
    /**
     * 发送通知到媒体服务器
     */
    public void sendNotification(String event, JsonNode data) {
        if (mediaServerSession == null || !mediaServerSession.isOpen()) {
            logger.warn("无法发送通知，媒体服务器未连接");
            return;
        }
        
        try {
            JsonNode notification = objectMapper.createObjectNode()
                    .put("type", "notification")
                    .put("event", event)
                    .put("timestamp", System.currentTimeMillis())
                    .set("data", data);

            // 使用同步锁防止并发写入
            sendLock.lock();
            try {
                if (mediaServerSession != null && mediaServerSession.isOpen()) {
                    mediaServerSession.sendMessage(new TextMessage(objectMapper.writeValueAsString(notification)));
                    logger.debug("发送通知到媒体服务器: {}", event);
                } else {
                    logger.warn("无法发送通知，媒体服务器连接已断开");
                }
            } finally {
                sendLock.unlock();
            }

        } catch (IOException e) {
            logger.error("发送通知失败", e);
        }
    }
    
    private void handleResponse(JsonNode messageNode) {
        String requestId = messageNode.get("requestId").asText();
        CompletableFuture<JsonNode> future = pendingRequests.remove(requestId);
        
        if (future != null) {
            boolean success = messageNode.get("success").asBoolean();
            if (success) {
                JsonNode data = messageNode.get("data");
                future.complete(data);
            } else {
                String error = messageNode.has("error") ? messageNode.get("error").asText() : "Unknown error";
                future.completeExceptionally(new RuntimeException(error));
            }
        }
    }
    
    private void handleNotification(JsonNode messageNode) {
        String event = messageNode.get("event").asText();
        JsonNode data = messageNode.get("data");
        
        logger.info("收到媒体服务器通知: {}", event);
        
        // 处理不同类型的通知
        switch (event) {
            case "producer_status_change":
                mediaProxyService.handleProducerStatusChange(data);
                break;
            case "consumer_status_change":
                mediaProxyService.handleConsumerStatusChange(data);
                break;
            case "new_producers":
                mediaProxyService.handleNewProducers(data);
                break;
            case "user_left":
                mediaProxyService.handleUserLeft(data);
                break;
            default:
                logger.warn("未知通知事件: {}", event);
        }
    }
    
    private void handleHeartbeat(WebSocketSession session) throws IOException {
        // 响应心跳
        JsonNode pong = objectMapper.createObjectNode()
                .put("type", "pong")
                .put("timestamp", System.currentTimeMillis());

        // 使用同步锁防止并发写入
        sendLock.lock();
        try {
            if (session.isOpen()) {
                session.sendMessage(new TextMessage(objectMapper.writeValueAsString(pong)));
            }
        } finally {
            sendLock.unlock();
        }
    }
    
    private String generateRequestId() {
        return "req_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString((int) (Math.random() * 0x10000));
    }
    
    /**
     * 检查媒体服务器是否连接
     */
    public boolean isMediaServerConnected() {
        return mediaServerSession != null && mediaServerSession.isOpen();
    }
    
    /**
     * 获取连接状态信息
     */
    public String getConnectionStatus() {
        if (mediaServerSession == null) {
            return "未连接";
        } else if (mediaServerSession.isOpen()) {
            return "已连接";
        } else {
            return "连接已断开";
        }
    }
}
