package org.meetsync.repository;

import org.meetsync.entity.RoomActivityLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface RoomActivityLogRepository extends JpaRepository<RoomActivityLog, Long> {

    /**
     * Find logs by room ID
     */
    @Query("SELECT ral FROM RoomActivityLog ral WHERE ral.room.id = :roomId ORDER BY ral.createdAt DESC")
    List<RoomActivityLog> findByRoomIdOrderByCreatedAtDesc(@Param("roomId") Long roomId);

    /**
     * Find logs by room ID string
     */
    @Query("SELECT ral FROM RoomActivityLog ral WHERE ral.room.roomId = :roomId ORDER BY ral.createdAt DESC")
    List<RoomActivityLog> findByRoomRoomIdOrderByCreatedAtDesc(@Param("roomId") String roomId);

    /**
     * Find logs by user ID
     */
    @Query("SELECT ral FROM RoomActivityLog ral WHERE ral.user.id = :userId ORDER BY ral.createdAt DESC")
    List<RoomActivityLog> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId);

    /**
     * Find logs by action type
     */
    List<RoomActivityLog> findByActionTypeOrderByCreatedAtDesc(String actionType);

    /**
     * Find logs by room and action type
     */
    @Query("SELECT ral FROM RoomActivityLog ral WHERE ral.room.roomId = :roomId AND ral.actionType = :actionType ORDER BY ral.createdAt DESC")
    List<RoomActivityLog> findByRoomRoomIdAndActionTypeOrderByCreatedAtDesc(@Param("roomId") String roomId, @Param("actionType") String actionType);

    /**
     * Find logs by user and action type
     */
    @Query("SELECT ral FROM RoomActivityLog ral WHERE ral.user.id = :userId AND ral.actionType = :actionType ORDER BY ral.createdAt DESC")
    List<RoomActivityLog> findByUserIdAndActionTypeOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("actionType") String actionType);

    /**
     * Find logs created after a specific date
     */
    List<RoomActivityLog> findByCreatedAtAfterOrderByCreatedAtDesc(LocalDateTime date);

    /**
     * Find logs for a room created after a specific date
     */
    @Query("SELECT ral FROM RoomActivityLog ral WHERE ral.room.roomId = :roomId AND ral.createdAt >= :fromDate ORDER BY ral.createdAt DESC")
    List<RoomActivityLog> findByRoomRoomIdAndCreatedAtAfterOrderByCreatedAtDesc(@Param("roomId") String roomId, @Param("fromDate") LocalDateTime fromDate);

    /**
     * Find logs for a user created after a specific date
     */
    @Query("SELECT ral FROM RoomActivityLog ral WHERE ral.user.id = :userId AND ral.createdAt >= :fromDate ORDER BY ral.createdAt DESC")
    List<RoomActivityLog> findByUserIdAndCreatedAtAfterOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("fromDate") LocalDateTime fromDate);

    /**
     * Find recent logs (last N records)
     */
    @Query("SELECT ral FROM RoomActivityLog ral ORDER BY ral.createdAt DESC")
    List<RoomActivityLog> findRecentLogs();

    /**
     * Find recent logs for a room (last N records)
     */
    @Query("SELECT ral FROM RoomActivityLog ral WHERE ral.room.roomId = :roomId ORDER BY ral.createdAt DESC")
    List<RoomActivityLog> findRecentLogsByRoomRoomId(@Param("roomId") String roomId);

    /**
     * Count logs by room
     */
    @Query("SELECT COUNT(ral) FROM RoomActivityLog ral WHERE ral.room.id = :roomId")
    long countByRoomId(@Param("roomId") Long roomId);

    /**
     * Count logs by user
     */
    @Query("SELECT COUNT(ral) FROM RoomActivityLog ral WHERE ral.user.id = :userId")
    long countByUserId(@Param("userId") Long userId);

    /**
     * Count logs by action type
     */
    long countByActionType(String actionType);

    /**
     * Find logs by IP address
     */
    List<RoomActivityLog> findByIpAddressOrderByCreatedAtDesc(String ipAddress);

    /**
     * Delete old logs (older than specified date)
     */
    @Query("DELETE FROM RoomActivityLog ral WHERE ral.createdAt < :beforeDate")
    void deleteLogsOlderThan(@Param("beforeDate") LocalDateTime beforeDate);

    /**
     * Find logs between dates
     */
    @Query("SELECT ral FROM RoomActivityLog ral WHERE ral.createdAt BETWEEN :startDate AND :endDate ORDER BY ral.createdAt DESC")
    List<RoomActivityLog> findLogsBetweenDates(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find logs for room between dates
     */
    @Query("SELECT ral FROM RoomActivityLog ral WHERE ral.room.roomId = :roomId AND ral.createdAt BETWEEN :startDate AND :endDate ORDER BY ral.createdAt DESC")
    List<RoomActivityLog> findRoomLogsBetweenDates(@Param("roomId") String roomId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
}
