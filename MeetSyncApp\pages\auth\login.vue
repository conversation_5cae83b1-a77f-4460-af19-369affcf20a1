<template>
  <view class="login-container page-container">
    <!-- 头部 Logo -->
    <view class="header">
      <view class="logo">
        <text class="logo-icon">🎲</text>
        <text class="logo-text">MeetSync</text>
      </view>
      <text class="subtitle">视频会议移动端</text>
    </view>


    <!-- 登录表单 -->
    <view class="form-container">
      <!-- 标签页切换 -->
      <view class="tabs">
        <view
          class="tab-item"
          :class="{ active: activeTab === 'login' }"
          @click="switchTab('login')"
        >
          用户登录
        </view>
        <view
          class="tab-item"
          :class="{ active: activeTab === 'register' }"
          @click="switchTab('register')"
        >
          用户注册
        </view>

      </view>

      <!-- 用户登录表单 -->
      <view v-if="activeTab === 'login'" class="form">
        <view class="form-group">
          <input
            class="form-input"
            type="text"
            placeholder="用户名"
            v-model="loginForm.username"
            :disabled="loading"
          />
        </view>
        <view class="form-group">
          <input
            class="form-input"
            type="password"
            placeholder="密码"
            v-model="loginForm.password"
            :disabled="loading"
          />
        </view>
        <button
          class="btn btn-primary btn-block"
          :disabled="loading || !canLogin"
          @click="handleLogin"
        >
          {{ loading ? '登录中...' : '登录' }}
        </button>
      </view>

      <!-- 用户注册表单 -->
      <view v-if="activeTab === 'register'" class="form">
        <view class="form-group">
          <input
            class="form-input"
            type="text"
            placeholder="用户名"
            v-model="registerForm.username"
            :disabled="loading"
          />
        </view>
        <view class="form-group">
          <input
            class="form-input"
            type="text"
            placeholder="邀请码"
            v-model="registerForm.inviteCode"
            :disabled="loading"
            maxlength="20"
          />
        </view>
        <view class="form-group">
          <input
            class="form-input"
            type="password"
            placeholder="密码"
            v-model="registerForm.password"
            :disabled="loading"
          />
        </view>
        <view class="form-group">
          <input
            class="form-input"
            type="password"
            placeholder="确认密码"
            v-model="registerForm.confirmPassword"
            :disabled="loading"
          />
        </view>
        <button
          class="btn btn-success btn-block"
          :disabled="loading || !canRegister"
          @click="handleRegister"
        >
          {{ loading ? '注册中...' : '注册' }}
        </button>
      </view>



      <!-- 错误提示 -->
      <view v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer">
      <text class="footer-text">基于 MediaSoup WebRTC 技术</text>
    </view>
  </view>
</template>

<script>
import authManager from '../../utils/auth.js'

export default {
  name: 'Login',
  data() {
    return {
      activeTab: 'login',
      loading: false,
      errorMessage: '',
      loginForm: {
        username: '',
        password: ''
      },
      registerForm: {
        username: '',
        inviteCode: '',
        password: '',
        confirmPassword: ''
      },

    }
  },
  computed: {
    canLogin() {
      return this.loginForm.username.trim() && this.loginForm.password.trim()
    },
    canRegister() {
      return this.registerForm.username.trim() &&
             this.registerForm.inviteCode.trim() &&
             this.registerForm.password.trim() &&
             this.registerForm.confirmPassword.trim() &&
             this.registerForm.password === this.registerForm.confirmPassword
    },

  },
  onLoad() {
    console.log("🔑 登录页面加载成功");
    console.log("📄 当前页面路径:", getCurrentPages()[getCurrentPages().length - 1].route);

    // 检查是否已登录
    if (authManager.isLoggedIn()) {
      console.log("用户已登录，跳转到仪表板");
      this.redirectToDashboard()
    } else {
      console.log("用户未登录，显示登录界面");
    }
  },
  onShow() {
    console.log("🔑 登录页面显示");
  },
  onReady() {
    console.log("🔑 登录页面渲染完成");
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab
      this.clearError()
    },
    
    async handleLogin() {
      if (!this.canLogin) return
      
      this.loading = true
      this.clearError()
      
      try {
        await authManager.login(this.loginForm.username, this.loginForm.password)
        
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        setTimeout(() => {
          this.redirectToDashboard()
        }, 1000)
        
      } catch (error) {
        this.showError(this.translateError(error.message))
      } finally {
        this.loading = false
      }
    },

    async handleRegister() {
      if (!this.canRegister) return

      this.loading = true
      this.clearError()

      try {
        // 导入API管理器
        const apiManager = (await import('../../utils/api.js')).default

        const response = await apiManager.register({
          username: this.registerForm.username,
          inviteCode: this.registerForm.inviteCode,
          password: this.registerForm.password,
          confirmPassword: this.registerForm.confirmPassword
        })

        uni.showToast({
          title: '注册成功',
          icon: 'success'
        })

        // 注册成功后自动登录
        if (response.token) {
          const authManager = (await import('../../utils/auth.js')).default
          authManager.setAuth(response.token, response.user)

          setTimeout(() => {
            this.redirectToDashboard()
          }, 1000)
        } else {
          // 如果没有返回token，切换到登录页面
          this.switchTab('login')
          this.loginForm.username = this.registerForm.username
        }

      } catch (error) {
        this.showError(this.translateError(error.message))
      } finally {
        this.loading = false
      }
    },


    
    redirectToDashboard() {
      uni.reLaunch({
        url: '/pages/dashboard/index'
      })
    },
    
    showError(message) {
      this.errorMessage = message
      setTimeout(() => {
        this.clearError()
      }, 5000)
    },
    
    clearError() {
      this.errorMessage = ''
    },
    
    translateError(error) {
      const errorMap = {
        'Invalid credentials': '用户名或密码错误',
        'User not found': '用户不存在',
        'Account disabled': '账户已被禁用',
        'Network Error': '网络连接失败',
        'Request failed': '请求失败，请稍后重试'
      }
      
      return errorMap[error] || error || '登录失败，请稍后重试'
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  background: linear-gradient(135deg, #1A1A1A 0%, #2D2D2D 50%, #1A1A1A 100%);
  padding: 40rpx;
  box-sizing: border-box;
}

.header {
  text-align: center;
  margin-top: 120rpx;
  margin-bottom: 80rpx;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.logo-icon {
  font-size: 80rpx;
  margin-right: 20rpx;
}

.logo-text {
  font-size: 60rpx;
  font-weight: bold;
  color: #FFD700;
}

.subtitle {
  color: #B8860B;
  font-size: 28rpx;
}

.form-container {
  background: #2D2D2D;
  border: 2rpx solid #FFD700;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(255, 215, 0, 0.2);
}

.tabs {
  display: flex;
  margin-bottom: 40rpx;
  background: #1A1A1A;
  border-radius: 12rpx;
  padding: 8rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #B8860B;
  transition: all 0.3s;
}

.tab-item.active {
  background: #FFD700;
  color: #1A1A1A;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-input {
  height: auto;
  width: 100%;
  padding: 25rpx;
  border: 2rpx solid #444444;
  border-radius: 12rpx;
  font-size: 32rpx;
  box-sizing: border-box;
  background: #1A1A1A;
  color: #E5E5E5;
}

.form-input:focus {
  border-color: #FFD700;
}

.form-input:disabled {
  background: #2D2D2D !important;
  color: #666666 !important;
  border-color: #333333 !important;
  cursor: not-allowed;
  opacity: 0.8;
}

.btn {
  width: 100%;
  padding: 25rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s;
}

.btn:disabled {
  background: #2D2D2D !important;
  color: #666666 !important;
  border: 1rpx solid #444444 !important;
  cursor: not-allowed;
  opacity: 0.8;
}

.btn-primary {
  background: #FFD700;
  color: #1A1A1A;
}

.btn-primary:disabled {
  background: #2D2D2D !important;
  color: #8B7355 !important;
  border: 1rpx solid #444444 !important;
}

.btn-secondary {
  background: #B8860B;
  color: white;
}

.btn-secondary:disabled {
  background: #2D2D2D !important;
  color: #666666 !important;
  border: 1rpx solid #444444 !important;
}

.btn-success {
  background: #32CD32;
  color: white;
}

.btn-success:disabled {
  background: #2D2D2D !important;
  color: #666666 !important;
  border: 1rpx solid #444444 !important;
}

.error-message {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #2D1B1B;
  color: #FF6B6B;
  border: 1rpx solid #FF6B6B;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
}

.footer {
  margin-top: auto;
  text-align: center;
}

.footer-text {
  color: #B8860B;
  font-size: 24rpx;
}
</style>
