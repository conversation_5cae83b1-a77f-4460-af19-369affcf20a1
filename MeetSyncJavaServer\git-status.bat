@echo off
echo ========================================
echo Git 仓库状态检查
echo ========================================
echo.

echo 检查Git是否已安装...
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Git未安装或未添加到PATH
    pause
    exit /b 1
)

echo 检查是否为Git仓库...
if not exist ".git" (
    echo [错误] 当前目录不是Git仓库
    echo 请先运行: .\init-git.bat
    pause
    exit /b 1
)

echo [成功] Git仓库检查通过
echo.

echo ========================================
echo 仓库基本信息
echo ========================================
echo 当前分支:
git branch --show-current
echo.

echo 所有分支:
git branch -a
echo.

echo 远程仓库:
git remote -v
echo.

echo ========================================
echo 工作区状态
echo ========================================
git status
echo.

echo ========================================
echo 最近提交历史
echo ========================================
git log --oneline -10
echo.

echo ========================================
echo 文件变更统计
echo ========================================
echo 未跟踪的文件:
git ls-files --others --exclude-standard | wc -l

echo 已修改的文件:
git diff --name-only | wc -l

echo 暂存区文件:
git diff --cached --name-only | wc -l
echo.

echo ========================================
echo Git配置信息
echo ========================================
echo 用户名: 
git config user.name

echo 邮箱:
git config user.email
echo.

echo ========================================
echo 仓库统计
echo ========================================
echo 总提交数:
git rev-list --all --count

echo 贡献者数量:
git shortlog -sn | wc -l

echo 仓库大小:
du -sh .git 2>nul || echo "无法计算仓库大小"
echo.

echo 是否查看详细的文件差异? (y/n)
set /p show_diff=
if /i "%show_diff%"=="y" (
    echo.
    echo ========================================
    echo 文件差异详情
    echo ========================================
    git diff
    echo.
    git diff --cached
)

echo.
echo 按任意键退出...
pause
