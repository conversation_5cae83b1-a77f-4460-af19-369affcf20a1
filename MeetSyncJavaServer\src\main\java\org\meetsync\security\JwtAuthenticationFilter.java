package org.meetsync.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.meetsync.entity.User;
import org.meetsync.entity.UserRole;
import org.meetsync.service.UserService;
import org.meetsync.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;
import java.util.Optional;

@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private ApplicationContext applicationContext;

    private UserService userService;

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
                                    @NonNull FilterChain filterChain) throws ServletException, IOException {
        
        try {
            String jwt = getJwtFromRequest(request);

            if (StringUtils.hasText(jwt)) {
                // 处理游客token
                if ("guest_token".equals(jwt)) {
                    handleGuestToken(request);
                    filterChain.doFilter(request, response);
                    return;
                }

                // 处理正常JWT token
                if (jwtUtil.validateToken(jwt)) {
                Long userId = jwtUtil.getUserIdFromToken(jwt);
                String username = jwtUtil.getUsernameFromToken(jwt);
                String role = jwtUtil.getUserRoleFromToken(jwt);
                
                // Load user details (lazy loading to avoid circular dependency)
                if (userService == null) {
                    userService = applicationContext.getBean(UserService.class);
                }
                Optional<User> userOpt = userService.findUserById(userId);
                
                if (userOpt.isPresent()) {
                    User user = userOpt.get();
                    
                    // Verify that the token data matches the user
                    if (user.getUsername().equals(username) && user.getRole().getValue().equals(role)) {
                        // Create authentication token
                        UsernamePasswordAuthenticationToken authentication = 
                            new UsernamePasswordAuthenticationToken(
                                user, 
                                null, 
                                Collections.singletonList(new SimpleGrantedAuthority("ROLE_" + role.toUpperCase()))
                            );
                        
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                        
                        logger.debug("Set authentication for user: {}", username);
                    } else {
                        logger.warn("Token data mismatch for user: {}", username);
                    }
                } else {
                    logger.warn("User not found for token: {}", username);
                }
                }
            }
        } catch (Exception ex) {
            logger.error("Could not set user authentication in security context", ex);
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 处理游客token
     */
    private void handleGuestToken(HttpServletRequest request) {
        // 创建游客用户对象
        User guestUser = new User();
        guestUser.setId(-1L); // 游客用户使用特殊ID
        guestUser.setUsername("guest_user");
        guestUser.setRole(UserRole.GUEST);

        // 创建认证token
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(
                guestUser,
                null,
                Collections.singletonList(new SimpleGrantedAuthority("ROLE_GUEST"))
            );

        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        SecurityContextHolder.getContext().setAuthentication(authentication);

        logger.debug("Set guest authentication");
    }

    /**
     * Extract JWT token from request header
     */
    private String getJwtFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        
        return null;
    }

    /**
     * Check if the request should be filtered
     */
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String path = request.getRequestURI();

        // 由于context-path是/api，实际路径会包含/api前缀
        // Skip JWT filter for public endpoints
        return path.equals("/api/auth/login") ||
               path.equals("/api/auth/register") ||
               path.equals("/api/health") ||
               path.equals("/api/ping") ||
               path.equals("/api/") ||
               path.startsWith("/api/public/") ||
               path.startsWith("/api/test/") ||  // 测试接口
               path.startsWith("/api/v3/api-docs") ||  // Swagger API文档
               path.startsWith("/api/swagger-ui") ||   // Swagger UI
               path.equals("/api/swagger-ui.html") ||  // Swagger UI首页
               path.startsWith("/api/swagger-resources") ||  // Swagger资源
               path.startsWith("/api/webjars");      // Swagger静态资源
    }
}
