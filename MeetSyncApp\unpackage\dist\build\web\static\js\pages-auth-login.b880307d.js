(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-auth-login"],{1865:function(e,t,r){var n=r("c86c");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.login-container[data-v-4829066a]{min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);display:flex;flex-direction:column;padding:%?40?%}.header[data-v-4829066a]{text-align:center;margin-top:%?120?%;margin-bottom:%?80?%}.logo[data-v-4829066a]{display:flex;align-items:center;justify-content:center;margin-bottom:%?20?%}.logo-icon[data-v-4829066a]{font-size:%?80?%;margin-right:%?20?%}.logo-text[data-v-4829066a]{font-size:%?60?%;font-weight:700;color:#fff}.subtitle[data-v-4829066a]{color:hsla(0,0%,100%,.8);font-size:%?28?%}.form-container[data-v-4829066a]{background:#fff;border-radius:%?20?%;padding:%?40?%;margin-bottom:%?40?%;box-shadow:0 %?10?% %?30?% rgba(0,0,0,.1)}.tabs[data-v-4829066a]{display:flex;margin-bottom:%?40?%;background:#f8f9fa;border-radius:%?12?%;padding:%?8?%}.tab-item[data-v-4829066a]{flex:1;text-align:center;padding:%?20?%;border-radius:%?8?%;font-size:%?28?%;color:#666;transition:all .3s}.tab-item.active[data-v-4829066a]{background:#007bff;color:#fff}.form-group[data-v-4829066a]{margin-bottom:%?30?%}.form-input[data-v-4829066a]{height:auto;width:100%;padding:%?25?%;border:%?2?% solid #e9ecef;border-radius:%?12?%;font-size:%?32?%;box-sizing:border-box}.form-input[data-v-4829066a]:focus{border-color:#007bff}.btn[data-v-4829066a]{width:100%;padding:%?25?%;border:none;border-radius:%?12?%;font-size:%?32?%;font-weight:700;transition:all .3s}.btn[data-v-4829066a]:disabled{opacity:.6}.btn-primary[data-v-4829066a]{background:#007bff;color:#fff}.btn-secondary[data-v-4829066a]{background:#6c757d;color:#fff}.btn-success[data-v-4829066a]{background:#28a745;color:#fff}.error-message[data-v-4829066a]{margin-top:%?20?%;padding:%?20?%;background:#f8d7da;color:#721c24;border-radius:%?8?%;font-size:%?28?%;text-align:center}.footer[data-v-4829066a]{margin-top:auto;text-align:center}.footer-text[data-v-4829066a]{color:hsla(0,0%,100%,.6);font-size:%?24?%}',""]),e.exports=t},"1ea2":function(e,t,r){"use strict";var n=r("af9e"),i=r("1c06"),a=r("ada5"),o=r("5d6e"),s=Object.isExtensible,u=n((function(){s(1)}));e.exports=u||o?function(e){return!!i(e)&&((!o||"ArrayBuffer"!==a(e))&&(!s||s(e)))}:s},"2ef2":function(e,t,r){var n=r("1865");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=r("967d").default;i("7d74b346",n,!0,{sourceMap:!1,shadowMode:!1})},"34d8":function(e,t,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(r("9004")),a=n(r("2634")),o=n(r("2fdc"));r("0c26"),r("bf0f");var s=n(r("3822")),u={name:"Login",data:function(){return{activeTab:"login",loading:!1,errorMessage:"",loginForm:{username:"",password:""},registerForm:{username:"",email:"",password:"",confirmPassword:""},guestForm:{name:""}}},computed:{canLogin:function(){return this.loginForm.username.trim()&&this.loginForm.password.trim()},canRegister:function(){return this.registerForm.username.trim()&&this.registerForm.email.trim()&&this.registerForm.password.trim()&&this.registerForm.confirmPassword.trim()&&this.registerForm.password===this.registerForm.confirmPassword},canGuestLogin:function(){return this.guestForm.name.trim().length>=2}},onLoad:function(){console.log("🔑 登录页面加载成功"),console.log("📄 当前页面路径:",getCurrentPages()[getCurrentPages().length-1].route),s.default.isLoggedIn()?(console.log("用户已登录，跳转到仪表板"),this.redirectToDashboard()):console.log("用户未登录，显示登录界面")},onShow:function(){console.log("🔑 登录页面显示")},onReady:function(){console.log("🔑 登录页面渲染完成")},methods:{switchTab:function(e){this.activeTab=e,this.clearError()},handleLogin:function(){var e=this;return(0,o.default)((0,a.default)().mark((function t(){return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.canLogin){t.next=2;break}return t.abrupt("return");case 2:return e.loading=!0,e.clearError(),t.prev=4,t.next=7,s.default.login(e.loginForm.username,e.loginForm.password);case 7:uni.showToast({title:"登录成功",icon:"success"}),setTimeout((function(){e.redirectToDashboard()}),1e3),t.next=14;break;case 11:t.prev=11,t.t0=t["catch"](4),e.showError(e.translateError(t.t0.message));case 14:return t.prev=14,e.loading=!1,t.finish(14);case 17:case"end":return t.stop()}}),t,null,[[4,11,14,17]])})))()},handleRegister:function(){var e=this;return(0,o.default)((0,a.default)().mark((function t(){var n,o,s;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.canRegister){t.next=2;break}return t.abrupt("return");case 2:return e.loading=!0,e.clearError(),t.prev=4,t.next=7,Promise.resolve().then((function(){return(0,i.default)(r("b013"))}));case 7:return n=t.sent.default,t.next=10,n.register({username:e.registerForm.username,email:e.registerForm.email,password:e.registerForm.password,confirmPassword:e.registerForm.confirmPassword});case 10:if(o=t.sent,uni.showToast({title:"注册成功",icon:"success"}),!o.token){t.next=20;break}return t.next=15,Promise.resolve().then((function(){return(0,i.default)(r("3822"))}));case 15:s=t.sent.default,s.setAuth(o.token,o.user),setTimeout((function(){e.redirectToDashboard()}),1e3),t.next=22;break;case 20:e.switchTab("login"),e.loginForm.username=e.registerForm.username;case 22:t.next=27;break;case 24:t.prev=24,t.t0=t["catch"](4),e.showError(e.translateError(t.t0.message));case 27:return t.prev=27,e.loading=!1,t.finish(27);case 30:case"end":return t.stop()}}),t,null,[[4,24,27,30]])})))()},handleGuestLogin:function(){var e=this;if(this.canGuestLogin){this.loading=!0,this.clearError();try{s.default.guestLogin(this.guestForm.name),uni.showToast({title:"进入成功",icon:"success"}),setTimeout((function(){e.redirectToDashboard()}),1e3)}catch(t){this.showError(t.message)}finally{this.loading=!1}}},redirectToDashboard:function(){uni.reLaunch({url:"/pages/dashboard/index"})},showError:function(e){var t=this;this.errorMessage=e,setTimeout((function(){t.clearError()}),5e3)},clearError:function(){this.errorMessage=""},translateError:function(e){return{"Invalid credentials":"用户名或密码错误","User not found":"用户不存在","Account disabled":"账户已被禁用","Network Error":"网络连接失败","Request failed":"请求失败，请稍后重试"}[e]||e||"登录失败，请稍后重试"}}};t.default=u},5075:function(e,t,r){"use strict";var n=r("ae5c"),i=r("71e9"),a=r("e7e3"),o=r("52df"),s=r("81a7"),u=r("1fc1"),c=r("1297"),f=r("d67c"),l=r("5112"),d=r("7e91"),g=TypeError,v=function(e,t){this.stopped=e,this.result=t},b=v.prototype;e.exports=function(e,t,r){var p,h,m,w,x,y,k,F=r&&r.that,C=!(!r||!r.AS_ENTRIES),E=!(!r||!r.IS_RECORD),_=!(!r||!r.IS_ITERATOR),T=!(!r||!r.INTERRUPTED),O=n(t,F),z=function(e){return p&&d(p,"normal",e),new v(!0,e)},P=function(e){return C?(a(e),T?O(e[0],e[1],z):O(e[0],e[1])):T?O(e,z):O(e)};if(E)p=e.iterator;else if(_)p=e;else{if(h=l(e),!h)throw new g(o(e)+" is not iterable");if(s(h)){for(m=0,w=u(e);w>m;m++)if(x=P(e[m]),x&&c(b,x))return x;return new v(!1)}p=f(e,h)}y=E?e.next:p.next;while(!(k=i(y,p)).done){try{x=P(k.value)}catch(j){d(p,"throw",j)}if("object"==typeof x&&x&&c(b,x))return x}return new v(!1)}},5210:function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"c",(function(){return i})),r.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-uni-view",{staticClass:"login-container"},[r("v-uni-view",{staticClass:"header"},[r("v-uni-view",{staticClass:"logo"},[r("v-uni-text",{staticClass:"logo-icon"},[e._v("📹")]),r("v-uni-text",{staticClass:"logo-text"},[e._v("MeetSync")])],1),r("v-uni-text",{staticClass:"subtitle"},[e._v("视频会议移动端")])],1),r("v-uni-view",{staticClass:"form-container"},[r("v-uni-view",{staticClass:"tabs"},[r("v-uni-view",{staticClass:"tab-item",class:{active:"login"===e.activeTab},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.switchTab("login")}}},[e._v("用户登录")]),r("v-uni-view",{staticClass:"tab-item",class:{active:"register"===e.activeTab},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.switchTab("register")}}},[e._v("用户注册")]),r("v-uni-view",{staticClass:"tab-item",class:{active:"guest"===e.activeTab},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.switchTab("guest")}}},[e._v("游客进入")])],1),"login"===e.activeTab?r("v-uni-view",{staticClass:"form"},[r("v-uni-view",{staticClass:"form-group"},[r("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"用户名",disabled:e.loading},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}})],1),r("v-uni-view",{staticClass:"form-group"},[r("v-uni-input",{staticClass:"form-input",attrs:{type:"password",placeholder:"密码",disabled:e.loading},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}})],1),r("v-uni-button",{staticClass:"btn btn-primary btn-block",attrs:{disabled:e.loading||!e.canLogin},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleLogin.apply(void 0,arguments)}}},[e._v(e._s(e.loading?"登录中...":"登录"))])],1):e._e(),"register"===e.activeTab?r("v-uni-view",{staticClass:"form"},[r("v-uni-view",{staticClass:"form-group"},[r("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"用户名",disabled:e.loading},model:{value:e.registerForm.username,callback:function(t){e.$set(e.registerForm,"username",t)},expression:"registerForm.username"}})],1),r("v-uni-view",{staticClass:"form-group"},[r("v-uni-input",{staticClass:"form-input",attrs:{type:"email",placeholder:"邮箱",disabled:e.loading},model:{value:e.registerForm.email,callback:function(t){e.$set(e.registerForm,"email",t)},expression:"registerForm.email"}})],1),r("v-uni-view",{staticClass:"form-group"},[r("v-uni-input",{staticClass:"form-input",attrs:{type:"password",placeholder:"密码",disabled:e.loading},model:{value:e.registerForm.password,callback:function(t){e.$set(e.registerForm,"password",t)},expression:"registerForm.password"}})],1),r("v-uni-view",{staticClass:"form-group"},[r("v-uni-input",{staticClass:"form-input",attrs:{type:"password",placeholder:"确认密码",disabled:e.loading},model:{value:e.registerForm.confirmPassword,callback:function(t){e.$set(e.registerForm,"confirmPassword",t)},expression:"registerForm.confirmPassword"}})],1),r("v-uni-button",{staticClass:"btn btn-success btn-block",attrs:{disabled:e.loading||!e.canRegister},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleRegister.apply(void 0,arguments)}}},[e._v(e._s(e.loading?"注册中...":"注册"))])],1):e._e(),"guest"===e.activeTab?r("v-uni-view",{staticClass:"form"},[r("v-uni-view",{staticClass:"form-group"},[r("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入您的显示名称",disabled:e.loading,maxlength:"20"},model:{value:e.guestForm.name,callback:function(t){e.$set(e.guestForm,"name",t)},expression:"guestForm.name"}})],1),r("v-uni-button",{staticClass:"btn btn-secondary btn-block",attrs:{disabled:e.loading||!e.canGuestLogin},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleGuestLogin.apply(void 0,arguments)}}},[e._v(e._s(e.loading?"进入中...":"以游客身份进入"))])],1):e._e(),e.errorMessage?r("v-uni-view",{staticClass:"error-message"},[e._v(e._s(e.errorMessage))]):e._e()],1),r("v-uni-view",{staticClass:"footer"},[r("v-uni-text",{staticClass:"footer-text"},[e._v("基于 MediaSoup WebRTC 技术")])],1)],1)},i=[]},"5d6e":function(e,t,r){"use strict";var n=r("af9e");e.exports=n((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}}))},7658:function(e,t,r){"use strict";var n=r("8bdb"),i=r("85c1"),a=r("bb80"),o=r("8466"),s=r("81a9"),u=r("d0b1"),c=r("5075"),f=r("b720"),l=r("474f"),d=r("1eb8"),g=r("1c06"),v=r("af9e"),b=r("29ba"),p=r("181d"),h=r("dcda");e.exports=function(e,t,r){var m=-1!==e.indexOf("Map"),w=-1!==e.indexOf("Weak"),x=m?"set":"add",y=i[e],k=y&&y.prototype,F=y,C={},E=function(e){var t=a(k[e]);s(k,e,"add"===e?function(e){return t(this,0===e?0:e),this}:"delete"===e?function(e){return!(w&&!g(e))&&t(this,0===e?0:e)}:"get"===e?function(e){return w&&!g(e)?void 0:t(this,0===e?0:e)}:"has"===e?function(e){return!(w&&!g(e))&&t(this,0===e?0:e)}:function(e,r){return t(this,0===e?0:e,r),this})},_=o(e,!l(y)||!(w||k.forEach&&!v((function(){(new y).entries().next()}))));if(_)F=r.getConstructor(t,e,m,x),u.enable();else if(o(e,!0)){var T=new F,O=T[x](w?{}:-0,1)!==T,z=v((function(){T.has(1)})),P=b((function(e){new y(e)})),j=!w&&v((function(){var e=new y,t=5;while(t--)e[x](t,t);return!e.has(-0)}));P||(F=t((function(e,t){f(e,k);var r=h(new y,e,F);return d(t)||c(t,r[x],{that:r,AS_ENTRIES:m}),r})),F.prototype=k,k.constructor=F),(z||j)&&(E("delete"),E("has"),m&&E("get")),(j||O)&&E(x),w&&k.clear&&delete k.clear}return C[e]=F,n({global:!0,constructor:!0,forced:F!==y},C),p(F,e),w||r.setStrong(F,e,m),F}},9004:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==(0,n.default)(e)&&"function"!==typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var a={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&Object.prototype.hasOwnProperty.call(e,s)){var u=o?Object.getOwnPropertyDescriptor(e,s):null;u&&(u.get||u.set)?Object.defineProperty(a,s,u):a[s]=e[s]}a["default"]=e,r&&r.set(e,a);return a},r("bf0f"),r("18f7"),r("d0af"),r("de6c"),r("6a54"),r("9a2c");var n=function(e){return e&&e.__esModule?e:{default:e}}(r("fcf3"));function i(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}},9031:function(e,t,r){"use strict";r.r(t);var n=r("5210"),i=r("b741");for(var a in i)["default"].indexOf(a)<0&&function(e){r.d(t,e,(function(){return i[e]}))}(a);r("9662");var o=r("828b"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"4829066a",null,!1,n["a"],void 0);t["default"]=s.exports},9662:function(e,t,r){"use strict";var n=r("2ef2"),i=r.n(n);i.a},b3e2:function(e,t,r){"use strict";var n,i=r("c238"),a=r("85c1"),o=r("bb80"),s=r("a74c"),u=r("d0b1"),c=r("7658"),f=r("d871c"),l=r("1c06"),d=r("235c").enforce,g=r("af9e"),v=r("a20b"),b=Object,p=Array.isArray,h=b.isExtensible,m=b.isFrozen,w=b.isSealed,x=b.freeze,y=b.seal,k=!a.ActiveXObject&&"ActiveXObject"in a,F=function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}},C=c("WeakMap",F,f),E=C.prototype,_=o(E.set);if(v)if(k){n=f.getConstructor(F,"WeakMap",!0),u.enable();var T=o(E["delete"]),O=o(E.has),z=o(E.get);s(E,{delete:function(e){if(l(e)&&!h(e)){var t=d(this);return t.frozen||(t.frozen=new n),T(this,e)||t.frozen["delete"](e)}return T(this,e)},has:function(e){if(l(e)&&!h(e)){var t=d(this);return t.frozen||(t.frozen=new n),O(this,e)||t.frozen.has(e)}return O(this,e)},get:function(e){if(l(e)&&!h(e)){var t=d(this);return t.frozen||(t.frozen=new n),O(this,e)?z(this,e):t.frozen.get(e)}return z(this,e)},set:function(e,t){if(l(e)&&!h(e)){var r=d(this);r.frozen||(r.frozen=new n),O(this,e)?_(this,e,t):r.frozen.set(e,t)}else _(this,e,t);return this}})}else(function(){return i&&g((function(){var e=x([]);return _(new C,e,1),!m(e)}))})()&&s(E,{set:function(e,t){var r;return p(e)&&(m(e)?r=x:w(e)&&(r=y)),_(this,e,t),r&&r(e),this}})},b741:function(e,t,r){"use strict";r.r(t);var n=r("34d8"),i=r.n(n);for(var a in n)["default"].indexOf(a)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(a);t["default"]=i.a},c238:function(e,t,r){"use strict";var n=r("af9e");e.exports=!n((function(){return Object.isExtensible(Object.preventExtensions({}))}))},d0af:function(e,t,r){"use strict";r("b3e2")},d0b1:function(e,t,r){"use strict";var n=r("8bdb"),i=r("bb80"),a=r("11bf"),o=r("1c06"),s=r("338c"),u=r("d6b1").f,c=r("80bb"),f=r("8449"),l=r("1ea2"),d=r("d7b4"),g=r("c238"),v=!1,b=d("meta"),p=0,h=function(e){u(e,b,{value:{objectID:"O"+p++,weakData:{}}})},m=e.exports={enable:function(){m.enable=function(){},v=!0;var e=c.f,t=i([].splice),r={};r[b]=1,e(r).length&&(c.f=function(r){for(var n=e(r),i=0,a=n.length;i<a;i++)if(n[i]===b){t(n,i,1);break}return n},n({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:f.f}))},fastKey:function(e,t){if(!o(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!s(e,b)){if(!l(e))return"F";if(!t)return"E";h(e)}return e[b].objectID},getWeakData:function(e,t){if(!s(e,b)){if(!l(e))return!0;if(!t)return!1;h(e)}return e[b].weakData},onFreeze:function(e){return g&&v&&l(e)&&!s(e,b)&&h(e),e}};a[b]=!0},d871c:function(e,t,r){"use strict";var n=r("bb80"),i=r("a74c"),a=r("d0b1").getWeakData,o=r("b720"),s=r("e7e3"),u=r("1eb8"),c=r("1c06"),f=r("5075"),l=r("4d16"),d=r("338c"),g=r("235c"),v=g.set,b=g.getterFor,p=l.find,h=l.findIndex,m=n([].splice),w=0,x=function(e){return e.frozen||(e.frozen=new y)},y=function(){this.entries=[]},k=function(e,t){return p(e.entries,(function(e){return e[0]===t}))};y.prototype={get:function(e){var t=k(this,e);if(t)return t[1]},has:function(e){return!!k(this,e)},set:function(e,t){var r=k(this,e);r?r[1]=t:this.entries.push([e,t])},delete:function(e){var t=h(this.entries,(function(t){return t[0]===e}));return~t&&m(this.entries,t,1),!!~t}},e.exports={getConstructor:function(e,t,r,n){var l=e((function(e,i){o(e,g),v(e,{type:t,id:w++,frozen:void 0}),u(i)||f(i,e[n],{that:e,AS_ENTRIES:r})})),g=l.prototype,p=b(t),h=function(e,t,r){var n=p(e),i=a(s(t),!0);return!0===i?x(n).set(t,r):i[n.id]=r,e};return i(g,{delete:function(e){var t=p(this);if(!c(e))return!1;var r=a(e);return!0===r?x(t)["delete"](e):r&&d(r,t.id)&&delete r[t.id]},has:function(e){var t=p(this);if(!c(e))return!1;var r=a(e);return!0===r?x(t).has(e):r&&d(r,t.id)}}),i(g,r?{get:function(e){var t=p(this);if(c(e)){var r=a(e);return!0===r?x(t).get(e):r?r[t.id]:void 0}},set:function(e,t){return h(this,e,t)}}:{add:function(e){return h(this,e,!0)}}),l}}}}]);