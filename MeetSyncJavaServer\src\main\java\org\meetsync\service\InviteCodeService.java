package org.meetsync.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.meetsync.entity.InviteCode;
import org.meetsync.entity.User;
import org.meetsync.repository.InviteCodeRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 邀请码服务类
 * 
 * <AUTHOR> Team
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class InviteCodeService {
    
    private final InviteCodeRepository inviteCodeRepository;
    private static final String INVITE_CODE_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final int INVITE_CODE_LENGTH = 8;
    private static final SecureRandom random = new SecureRandom();
    
    /**
     * 生成邀请码
     * 
     * @param inviterId 邀请人ID
     * @return 生成的邀请码实体
     */
    @Transactional
    public InviteCode generateInviteCode(Long inviterId) {
        String code = generateUniqueCode();
        
        InviteCode inviteCode = InviteCode.builder()
                .inviteCode(code)
                .inviterId(inviterId)
                .isUsed(false)
                .inviteTime(LocalDateTime.now())
                .build();
        
        InviteCode saved = inviteCodeRepository.save(inviteCode);
        log.info("生成邀请码成功: {} by user: {}", code, inviterId);
        return saved;
    }
    
    /**
     * 验证邀请码
     * 
     * @param inviteCode 邀请码
     * @return 是否有效
     */
    public boolean validateInviteCode(String inviteCode) {
        if (inviteCode == null || inviteCode.trim().isEmpty()) {
            return false;
        }
        
        Optional<InviteCode> codeEntity = inviteCodeRepository.findAvailableByInviteCode(inviteCode.trim().toUpperCase());
        boolean isValid = codeEntity.isPresent();
        
        log.info("邀请码验证: {} - {}", inviteCode, isValid ? "有效" : "无效");
        return isValid;
    }
    
    /**
     * 使用邀请码
     * 
     * @param inviteCode 邀请码
     * @param userId 使用人ID
     * @return 是否使用成功
     */
    @Transactional
    public boolean useInviteCode(String inviteCode, Long userId) {
        if (inviteCode == null || inviteCode.trim().isEmpty() || userId == null) {
            return false;
        }
        
        Optional<InviteCode> codeEntityOpt = inviteCodeRepository.findAvailableByInviteCode(inviteCode.trim().toUpperCase());
        
        if (codeEntityOpt.isPresent()) {
            InviteCode codeEntity = codeEntityOpt.get();
            codeEntity.markAsUsed(userId);
            inviteCodeRepository.save(codeEntity);
            
            log.info("邀请码使用成功: {} by user: {}", inviteCode, userId);
            return true;
        }
        
        log.warn("邀请码使用失败: {} by user: {} - 邀请码不存在或已使用", inviteCode, userId);
        return false;
    }
    
    /**
     * 获取邀请码详情
     * 
     * @param inviteCode 邀请码
     * @return 邀请码实体
     */
    public Optional<InviteCode> getInviteCodeDetails(String inviteCode) {
        if (inviteCode == null || inviteCode.trim().isEmpty()) {
            return Optional.empty();
        }
        
        return inviteCodeRepository.findByInviteCode(inviteCode.trim().toUpperCase());
    }
    
    /**
     * 获取用户创建的邀请码列表
     * 
     * @param inviterId 邀请人ID
     * @return 邀请码列表
     */
    public List<InviteCode> getUserInviteCodes(Long inviterId) {
        return inviteCodeRepository.findByInviterIdOrderByInviteTimeDesc(inviterId);
    }
    
    /**
     * 获取用户创建的可用邀请码列表
     * 
     * @param inviterId 邀请人ID
     * @return 可用邀请码列表
     */
    public List<InviteCode> getUserAvailableInviteCodes(Long inviterId) {
        return inviteCodeRepository.findByInviterIdAndIsUsedOrderByInviteTimeDesc(inviterId, false);
    }
    
    /**
     * 统计用户的邀请码使用情况
     * 
     * @param inviterId 邀请人ID
     * @return 统计信息数组 [总数, 已使用数, 未使用数]
     */
    public long[] getUserInviteCodeStats(Long inviterId) {
        long total = inviteCodeRepository.countByInviterId(inviterId);
        long used = inviteCodeRepository.countByInviterIdAndIsUsed(inviterId, true);
        long unused = total - used;
        
        return new long[]{total, used, unused};
    }
    
    /**
     * 生成唯一的邀请码
     * 
     * @return 唯一邀请码
     */
    private String generateUniqueCode() {
        String code;
        int attempts = 0;
        int maxAttempts = 100;
        
        do {
            code = generateRandomCode();
            attempts++;
            
            if (attempts > maxAttempts) {
                throw new RuntimeException("生成唯一邀请码失败，请稍后重试");
            }
        } while (inviteCodeRepository.existsByInviteCode(code));
        
        return code;
    }
    
    /**
     * 生成随机邀请码
     * 
     * @return 随机邀请码
     */
    private String generateRandomCode() {
        StringBuilder code = new StringBuilder(INVITE_CODE_LENGTH);
        
        for (int i = 0; i < INVITE_CODE_LENGTH; i++) {
            int index = random.nextInt(INVITE_CODE_CHARS.length());
            code.append(INVITE_CODE_CHARS.charAt(index));
        }
        
        return code.toString();
    }
    
    /**
     * 清理过期的未使用邀请码（可选功能）
     * 
     * @param expireDays 过期天数
     * @return 清理的数量
     */
    @Transactional
    public int cleanupExpiredInviteCodes(int expireDays) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(expireDays);
        List<InviteCode> expiredCodes = inviteCodeRepository.findExpiredUnusedInviteCodes(expireTime);
        
        if (!expiredCodes.isEmpty()) {
            inviteCodeRepository.deleteAll(expiredCodes);
            log.info("清理过期邀请码: {} 个", expiredCodes.size());
        }
        
        return expiredCodes.size();
    }
    
    /**
     * 获取系统邀请码统计
     * 
     * @return 统计信息数组 [总数, 已使用数, 未使用数]
     */
    public long[] getSystemInviteCodeStats() {
        long total = inviteCodeRepository.countTotalInviteCodes();
        long used = inviteCodeRepository.countUsedInviteCodes();
        long unused = total - used;
        
        return new long[]{total, used, unused};
    }
}
