{"title": "isStandalonePWA", "name": "is-standalone-pwa", "version": "0.1.1", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "description": "Detect if PWA is running in standalone mode", "type": "commonjs", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "exports": {".": {"require": "./dist/cjs/index.js", "import": "./dist/esm/index.js"}}, "files": ["dist"], "directories": {"dist": "dist", "src": "src", "test": "test"}, "scripts": {"build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --module commonjs --outDir ./dist/cjs --target es2019", "build:esm": "tsc --module esnext --moduleResolution bundler --outDir ./dist/esm --target es2019 && echo '{\"type\":\"module\"}' > ./dist/esm/package.json", "test": "mocha ./test"}, "repository": {"type": "git", "url": "git+https://github.com/faisalman/is-standalone-pwa.git"}, "keywords": ["ua-parser-js", "is-standalone-pwa", "pwa"], "license": "MIT", "bugs": {"url": "https://github.com/faisalman/is-standalone-pwa/issues"}, "homepage": "https://github.com/faisalman/is-standalone-pwa#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/faisalman"}, {"type": "opencollective", "url": "https://opencollective.com/ua-parser-js"}, {"type": "paypal", "url": "https://paypal.me/faisalman"}], "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2"}}