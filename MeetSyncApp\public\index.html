<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-touch-fullscreen" content="yes">
    
    <title>MeetSync - 视频会议</title>
    <meta name="description" content="基于 MediaSoup 的移动端视频会议应用">
    <meta name="keywords" content="视频会议,WebRTC,MediaSoup,移动端">
    
    <!-- 基础图标 - 使用 emoji 作为临时图标 -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📹</text></svg>">
    
    <style>
        /* 加载动画 */
        .uni-loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        
        .uni-loading-logo {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        
        .uni-loading-text {
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 2rem;
        }
        
        .uni-loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        /* 隐藏加载动画 */
        .uni-loading.hidden {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        /* 基础样式重置 */
        * {
            box-sizing: border-box;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-color: #f8f9fa;
        }
        
        #app {
            height: 100vh;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <!-- 应用容器 -->
    <div id="app">
        <!-- 加载动画 -->
        <div class="uni-loading" id="loading">
            <div class="uni-loading-logo">📹</div>
            <div class="uni-loading-text">MeetSync</div>
            <div class="uni-loading-spinner"></div>
        </div>
    </div>
    
    <script>
        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('Global error:', event.error);
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);
        });
        
        // 隐藏加载动画
        function hideLoading() {
            const loading = document.getElementById('loading');
            if (loading) {
                loading.classList.add('hidden');
                setTimeout(() => {
                    loading.style.display = 'none';
                }, 300);
            }
        }
        
        // 当应用加载完成时隐藏加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 设置超时检查
            const loadTimeout = setTimeout(() => {
                console.warn('应用加载超时');
            }, 30000);
            
            // 当应用加载完成时清除超时
            window.addEventListener('load', () => {
                clearTimeout(loadTimeout);
                hideLoading();
            });
        });
        
        // PWA 支持已暂时禁用
        console.log('📱 MeetSync 应用已启动（PWA 功能已禁用）');
    </script>
</body>
</html>
