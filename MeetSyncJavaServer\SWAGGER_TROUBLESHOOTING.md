# Swagger 问题排查指南

## 常见问题

### 1. 404错误 - 无法访问Swagger UI

**问题**: 访问 `http://localhost:8080/swagger-ui.html` 返回404

**原因**: 应用设置了 `context-path: /api`，所有路径都需要加上 `/api` 前缀

**解决方案**: 使用正确的路径
```
✓ 正确: http://localhost:8080/api/swagger-ui.html
✗ 错误: http://localhost:8080/swagger-ui.html
```

### 2. API文档无法加载

**问题**: Swagger UI打开但API文档无法加载

**检查步骤**:
1. 确认API文档端点可访问：
   ```
   curl http://localhost:8080/api/v3/api-docs
   ```

2. 检查控制台错误信息

3. 验证Security配置是否允许访问Swagger端点

### 3. JWT认证无法使用

**问题**: 在Swagger中无法进行JWT认证

**解决步骤**:
1. 先通过 `/api/auth/login` 获取JWT令牌
2. 点击Swagger UI右上角的 **Authorize** 按钮
3. 输入JWT令牌（不需要'Bearer '前缀）
4. 点击 **Authorize** 确认

### 4. 接口无法测试

**问题**: 点击"Try it out"后无法发送请求

**检查项目**:
- 服务是否正常运行
- 网络连接是否正常
- 浏览器控制台是否有错误

## 验证步骤

### 1. 基础验证
```bash
# 检查服务状态
curl http://localhost:8080/api/health

# 检查API文档
curl http://localhost:8080/api/v3/api-docs

# 检查测试接口
curl http://localhost:8080/api/test/swagger
```

### 2. 使用测试脚本
```bash
.\test-swagger-access.bat
```

### 3. 手动验证
1. 启动应用：`.\start-java-server.bat`
2. 等待启动完成（看到"Started MeetSyncApplication"）
3. 访问：http://localhost:8080/api/swagger-ui.html

## 正确的访问路径

由于应用配置了 `server.servlet.context-path: /api`，所有路径都需要加上 `/api` 前缀：

| 功能 | 正确路径 | 错误路径 |
|------|----------|----------|
| Swagger UI | http://localhost:8080/api/swagger-ui.html | http://localhost:8080/swagger-ui.html |
| API文档 | http://localhost:8080/api/v3/api-docs | http://localhost:8080/v3/api-docs |
| 健康检查 | http://localhost:8080/api/health | http://localhost:8080/health |
| 用户登录 | http://localhost:8080/api/auth/login | http://localhost:8080/auth/login |

## 配置检查

### 1. application.yml配置
```yaml
server:
  servlet:
    context-path: /api  # 这个设置影响所有路径

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
```

### 2. Security配置
确保以下路径被允许访问：
```java
.requestMatchers("/v3/api-docs/**", "/swagger-ui/**", "/swagger-ui.html").permitAll()
```

### 3. 依赖检查
确保pom.xml中包含：
```xml
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    <version>2.2.0</version>
</dependency>
```

## 开发建议

### 1. 移除context-path（可选）
如果不需要 `/api` 前缀，可以注释掉：
```yaml
server:
  servlet:
    # context-path: /api
```

### 2. 使用相对路径
在代码中使用相对路径，避免硬编码：
```java
@RequestMapping("/auth")  // 而不是 "/api/auth"
```

### 3. 环境区分
可以为不同环境设置不同的context-path：
```yaml
---
spring:
  config:
    activate:
      on-profile: prod
server:
  servlet:
    context-path: /api/v1
```

## 日志调试

启用Swagger相关日志：
```yaml
logging:
  level:
    org.springdoc: DEBUG
    org.springframework.web: DEBUG
```

## 浏览器调试

1. 打开浏览器开发者工具（F12）
2. 查看Network标签页
3. 访问Swagger UI
4. 检查失败的请求和响应

## 联系支持

如果问题仍然存在：
1. 检查应用日志
2. 确认Java版本（需要17+）
3. 验证依赖版本兼容性
4. 参考官方文档：https://springdoc.org/
