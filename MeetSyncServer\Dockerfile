# MeetSync 服务器 Dockerfile
FROM node:18-bullseye

# 设置工作目录
WORKDIR /app

# 安装系统依赖和工具
RUN apt-get update && apt-get install -y \
    build-essential \
    python3 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制 package 文件并安装依赖
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# 复制应用源码
COPY src/ ./src/
COPY ssl/ ./ssl/

# 创建日志目录
RUN mkdir -p /app/logs && chown -R node:node /app

# 暴露端口
EXPOSE 3016
EXPOSE 10000-10100/udp

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3016
ENV SKIP_DB_INIT=true

# 添加健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:3016/health || exit 1

# 切换到非root用户
USER node

# 启动应用（跳过数据库初始化）
CMD ["node", "src/app.js"]
