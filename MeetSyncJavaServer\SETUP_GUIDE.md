# Java开发环境设置指南

由于系统中没有检测到Java和Maven，需要先安装Java开发环境。

## 1. 安装Java 17

### 方法一：使用Oracle JDK
1. 访问 [Oracle JDK下载页面](https://www.oracle.com/java/technologies/javase/jdk17-archive-downloads.html)
2. 下载适合Windows的JDK 17安装包
3. 运行安装程序并按照提示完成安装

### 方法二：使用OpenJDK（推荐）
1. 访问 [Adoptium](https://adoptium.net/temurin/releases/?version=17)
2. 选择Java 17 LTS版本
3. 下载Windows x64的MSI安装包
4. 运行安装程序

### 方法三：使用包管理器
如果安装了Chocolatey：
```powershell
choco install openjdk17
```

如果安装了Scoop：
```powershell
scoop install openjdk17
```

## 2. 安装Maven

### 方法一：手动安装
1. 访问 [Maven下载页面](https://maven.apache.org/download.cgi)
2. 下载Binary zip archive
3. 解压到合适的目录（如 `C:\Program Files\Apache\maven`）
4. 添加到系统PATH环境变量

### 方法二：使用包管理器
如果安装了Chocolatey：
```powershell
choco install maven
```

如果安装了Scoop：
```powershell
scoop install maven
```

## 3. 配置环境变量

### 配置JAVA_HOME
1. 右键"此电脑" -> "属性" -> "高级系统设置" -> "环境变量"
2. 在系统变量中新建：
   - 变量名：`JAVA_HOME`
   - 变量值：Java安装路径（如 `C:\Program Files\Eclipse Adoptium\jdk-17.0.x-hotspot`）

### 配置MAVEN_HOME
1. 在系统变量中新建：
   - 变量名：`MAVEN_HOME`
   - 变量值：Maven安装路径（如 `C:\Program Files\Apache\maven`）

### 更新PATH
在系统变量PATH中添加：
- `%JAVA_HOME%\bin`
- `%MAVEN_HOME%\bin`

## 4. 验证安装

重新打开命令提示符或PowerShell，运行以下命令验证：

```powershell
java -version
mvn -version
```

应该看到类似输出：
```
java version "17.0.x" 2023-xx-xx LTS
Java(TM) SE Runtime Environment (build 17.0.x+xx-LTS-xxx)
Java HotSpot(TM) 64-Bit Server VM (build 17.0.x+xx-LTS-xxx, mixed mode, sharing)

Apache Maven 3.9.x (xxxxxxx)
Maven home: C:\Program Files\Apache\maven
Java version: 17.0.x, vendor: Eclipse Adoptium, runtime: C:\Program Files\Eclipse Adoptium\jdk-17.0.x-hotspot
```

## 5. 运行Java服务

安装完成后，可以运行以下命令启动Java服务：

```powershell
# 进入Java服务目录
cd MeetSyncJavaServer

# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run
```

## 6. 替代方案：使用IDE

如果不想手动安装，可以使用集成开发环境：

### IntelliJ IDEA
1. 下载并安装 [IntelliJ IDEA Community Edition](https://www.jetbrains.com/idea/download/)
2. 打开项目时，IDE会自动检测并提示安装所需的JDK
3. 使用IDE内置的Maven支持运行项目

### Eclipse
1. 下载并安装 [Eclipse IDE for Java Developers](https://www.eclipse.org/downloads/)
2. 导入Maven项目
3. 配置JDK并运行项目

### Visual Studio Code
1. 安装 [Visual Studio Code](https://code.visualstudio.com/)
2. 安装Java扩展包：Extension Pack for Java
3. 打开项目文件夹，扩展会自动配置Java环境

## 7. Docker方案（可选）

如果不想在本地安装Java环境，可以使用Docker：

```dockerfile
# 创建Dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app
COPY . .

RUN ./mvnw clean package -DskipTests

EXPOSE 8080

CMD ["java", "-jar", "target/MeetSyncJavaServer-1.0-SNAPSHOT.jar"]
```

```powershell
# 构建和运行
docker build -t meetsync-java .
docker run -p 8080:8080 meetsync-java
```

## 故障排除

### 常见问题

1. **"java不是内部或外部命令"**
   - 检查JAVA_HOME环境变量是否正确设置
   - 检查PATH中是否包含%JAVA_HOME%\bin

2. **"mvn不是内部或外部命令"**
   - 检查MAVEN_HOME环境变量是否正确设置
   - 检查PATH中是否包含%MAVEN_HOME%\bin

3. **权限问题**
   - 以管理员身份运行命令提示符
   - 检查安装目录的权限设置

4. **版本冲突**
   - 确保Java版本为17或更高
   - 确保Maven版本为3.6或更高

### 验证环境变量
```powershell
echo $env:JAVA_HOME
echo $env:MAVEN_HOME
echo $env:PATH
```

完成环境设置后，就可以正常编译和运行MeetSync Java服务了。
