(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-auth-login~pages-dashboard-index~pages-room-index"],{"20f5":function(e,t,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("c223"),r("bf0f"),r("7a76"),r("c9b5"),r("4626"),r("5ac7"),r("aa9c");var a=n(r("2634")),o=n(r("2fdc")),u=n(r("9b1b")),i=n(r("80b1")),s=n(r("efe5")),c=n(r("fd93")),l=function(){function e(){(0,i.default)(this,e),this.config=c.default.server,this.retryCount=3,this.retryDelay=1e3}return(0,s.default)(e,[{key:"getRequestConfig",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,u.default)({timeout:3e4,sslVerify:this.config.sslVerify||!1,enableHttp2:this.config.enableHttp2||!1,enableQuic:this.config.enableQuic||!1,enableCache:!1,enableHttpDNS:!1,retry:!0,retryTimes:this.retryCount},e);return"development"===c.default.env&&(t.sslVerify=!1,t.enableHttp2=!1,t.enableQuic=!1),t}},{key:"requestWithRetry",value:function(){var e=(0,o.default)((0,a.default)().mark((function e(t){var r,n,o,u,i=arguments;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:r=i.length>1&&void 0!==i[1]?i[1]:this.retryCount,n=this.getRequestConfig(t),o=0;case 3:if(!(o<r)){e.next=24;break}return e.prev=4,console.log("[Network] 尝试请求 (".concat(o+1,"/").concat(r,"):"),n.url),e.next=8,this.makeRequest(n);case 8:return u=e.sent,console.log("[Network] 请求成功:",n.url),e.abrupt("return",u);case 13:if(e.prev=13,e.t0=e["catch"](4),console.error("[Network] 请求失败 (".concat(o+1,"/").concat(r,"):"),e.t0.message),this.isSSLError(e.t0)&&o<r-1&&(console.log("[Network] 检测到 SSL 错误，尝试降级配置"),n.sslVerify=!1,n.enableHttp2=!1,n.enableQuic=!1),o!==r-1){e.next=19;break}throw this.createNetworkError(e.t0);case 19:return e.next=21,this.delay(this.retryDelay*(o+1));case 21:o++,e.next=3;break;case 24:case"end":return e.stop()}}),e,this,[[4,13]])})));return function(t){return e.apply(this,arguments)}}()},{key:"makeRequest",value:function(e){return new Promise((function(t,r){uni.request((0,u.default)((0,u.default)({},e),{},{success:function(e){var n;e.statusCode>=200&&e.statusCode<300?t(e.data):r(new Error("HTTP ".concat(e.statusCode,": ").concat((null===(n=e.data)||void 0===n?void 0:n.error)||"请求失败")))},fail:function(e){r(new Error(e.errMsg||"网络请求失败"))}}))}))}},{key:"isSSLError",value:function(e){var t=e.message||e.errMsg||"";return["SSL","TLS","certificate","handshake","protocol version","cipher suite","CERT_","ERR_SSL_","ERR_CERT_","SSL_ERROR_","SEC_ERROR_"].some((function(e){return t.toLowerCase().includes(e.toLowerCase())}))}},{key:"createNetworkError",value:function(e){var t=e.message||e.errMsg||"网络请求失败";return this.isSSLError(e)?new Error("SSL连接失败，请检查服务器证书配置或联系管理员"):t.includes("timeout")?new Error("网络请求超时，请检查网络连接"):t.includes("Network Error")?new Error("网络连接失败，请检查网络设置"):new Error("网络请求失败: ".concat(t))}},{key:"delay",value:function(e){return new Promise((function(t){return setTimeout(t,e)}))}},{key:"checkNetworkStatus",value:function(){var e=(0,o.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e){uni.getNetworkType({success:function(t){var r=t.networkType,n="none"!==r;e({isConnected:n,networkType:r,isWifi:"wifi"===r,isMobile:["2g","3g","4g","5g"].includes(r)})},fail:function(){e({isConnected:!1,networkType:"unknown",isWifi:!1,isMobile:!1})}})})));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()},{key:"testServerConnection",value:function(){var e=(0,o.default)((0,a.default)().mark((function e(){var t,r,n=arguments;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=n.length>0&&void 0!==n[0]?n[0]:this.config.baseUrl,e.prev=1,console.log("[Network] 测试服务器连接: ".concat(t)),r={url:"".concat(t,"/api/health"),method:"GET",timeout:1e4,sslVerify:!1,enableHttp2:!1,enableQuic:!1},e.next=6,this.makeRequest(r);case 6:return console.log("[Network] 服务器连接正常"),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e["catch"](1),console.error("[Network] 服务器连接失败:",e.t0.message),e.abrupt("return",!1);case 14:case"end":return e.stop()}}),e,this,[[1,10]])})));return function(){return e.apply(this,arguments)}}()},{key:"getRecommendedConfig",value:function(){return{timeout:3e4,sslVerify:!1,enableHttp2:!1,enableQuic:!1,enableCache:!1,enableHttpDNS:!1,retry:!0,retryTimes:3,retryDelay:1e3,header:{"Content-Type":"application/json","User-Agent":"MeetSync-UniApp/1.0.0",Accept:"application/json","Cache-Control":"no-cache"}}}},{key:"diagnoseNetworkIssue",value:function(){var e=(0,o.default)((0,a.default)().mark((function e(t){var r,n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r={error:t.message,suggestions:[],canRetry:!1},e.next=3,this.checkNetworkStatus();case 3:if(n=e.sent,n.isConnected){e.next=7;break}return r.suggestions.push("请检查网络连接"),e.abrupt("return",r);case 7:return this.isSSLError(t)&&(r.suggestions.push("检测到 SSL/TLS 连接问题","1. 确认服务器证书配置正确","2. 检查服务器是否支持 TLS 1.2 或更高版本","3. 开发环境可以尝试关闭 SSL 验证","4. 联系服务器管理员检查加密套件配置"),r.canRetry=!0),t.message.includes("timeout")&&(r.suggestions.push("网络请求超时","1. 检查网络连接速度","2. 尝试增加请求超时时间","3. 检查服务器响应速度"),r.canRetry=!0),(t.message.includes("500")||t.message.includes("502")||t.message.includes("503"))&&(r.suggestions.push("服务器内部错误","1. 检查服务器是否正常运行","2. 查看服务器日志","3. 稍后重试"),r.canRetry=!0),e.abrupt("return",r);case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),e}(),f=new l,h=f;t.default=h},2634:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
t.default=function(){return e};var e={},r=Object.prototype,a=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},u="function"==typeof Symbol?Symbol:{},i=u.iterator||"@@iterator",s=u.asyncIterator||"@@asyncIterator",c=u.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(N){l=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var a=t&&t.prototype instanceof d?t:d,u=Object.create(a.prototype),i=new T(n||[]);return o(u,"_invoke",{value:S(e,r,i)}),u}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(N){return{type:"throw",arg:N}}}e.wrap=f;var p={};function d(){}function v(){}function y(){}var g={};l(g,i,(function(){return this}));var w=Object.getPrototypeOf,m=w&&w(w(C([])));m&&m!==r&&a.call(m,i)&&(g=m);var b=y.prototype=d.prototype=Object.create(g);function k(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){var r;o(this,"_invoke",{value:function(o,u){function i(){return new t((function(r,i){(function r(o,u,i,s){var c=h(e[o],e,u);if("throw"!==c.type){var l=c.arg,f=l.value;return f&&"object"==(0,n.default)(f)&&a.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,i,s)}),(function(e){r("throw",e,i,s)})):t.resolve(f).then((function(e){l.value=e,i(l)}),(function(e){return r("throw",e,i,s)}))}s(c.arg)})(o,u,r,i)}))}return r=r?r.then(i,i):i()}})}function S(e,t,r){var n="suspendedStart";return function(a,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===a)throw o;return R()}for(r.method=a,r.arg=o;;){var u=r.delegate;if(u){var i=E(u,r);if(i){if(i===p)continue;return i}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=h(e,t,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===p)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function E(e,t){var r=t.method,n=e.iterator[r];if(void 0===n)return t.delegate=null,"throw"===r&&e.iterator["return"]&&(t.method="return",t.arg=void 0,E(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),p;var a=h(n,e.iterator,t.arg);if("throw"===a.type)return t.method="throw",t.arg=a.arg,t.delegate=null,p;var o=a.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,p):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,p)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function _(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function C(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,n=function t(){for(;++r<e.length;)if(a.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return n.next=n}}return{next:R}}function R(){return{value:void 0,done:!0}}return v.prototype=y,o(b,"constructor",{value:y,configurable:!0}),o(y,"constructor",{value:v,configurable:!0}),v.displayName=l(y,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,l(e,c,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},k(x.prototype),l(x.prototype,s,(function(){return this})),e.AsyncIterator=x,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var u=new x(f(t,r,n,a),o);return e.isGeneratorFunction(r)?u:u.next().then((function(e){return e.done?e.value:u.next()}))},k(b),l(b,c,"Generator"),l(b,i,(function(){return this})),l(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=C,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!e)for(var t in this)"t"===t.charAt(0)&&a.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,n){return u.type="throw",u.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],u=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var i=a.call(o,"catchLoc"),s=a.call(o,"finallyLoc");if(i&&s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(i){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var u=o?o.completion:{};return u.type=e,u.arg=t,o?(this.method="next",this.next=o.finallyLoc,p):this.complete(u)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),_(r),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;_(r)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:C(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),p}},e},r("6a54"),r("01a2"),r("e39c"),r("bf0f"),r("844d"),r("18f7"),r("de6c"),r("3872e"),r("4e9b"),r("114e"),r("c240"),r("926e"),r("7a76"),r("c9b5"),r("aa9c"),r("2797"),r("8a8d"),r("dc69"),r("f7a5");var n=function(e){return e&&e.__esModule?e:{default:e}}(r("fcf3"))},"2fdc":function(e,t,r){"use strict";function n(e,t,r,n,a,o,u){try{var i=e[o](u),s=i.value}catch(c){return void r(c)}i.done?t(s):Promise.resolve(s).then(n,a)}r("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(){var t=this,r=arguments;return new Promise((function(a,o){var u=e.apply(t,r);function i(e){n(u,a,o,i,s,"next",e)}function s(e){n(u,a,o,i,s,"throw",e)}i(void 0)}))}},r("bf0f")},3822:function(e,t,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("7a76"),r("c9b5"),r("0c26"),r("bf0f");var a=n(r("9b1b")),o=n(r("2634")),u=n(r("2fdc")),i=n(r("80b1")),s=n(r("efe5")),c=function(){function e(){(0,i.default)(this,e),this.token=uni.getStorageSync("auth_token")||null,this.user=uni.getStorageSync("user")||null}return(0,s.default)(e,[{key:"getCurrentUser",value:function(){return this.user}},{key:"getAuthToken",value:function(){return this.token}},{key:"isLoggedIn",value:function(){return!(!this.token||!this.user)}},{key:"setAuth",value:function(e,t){this.token=e,this.user=t,uni.setStorageSync("auth_token",e),uni.setStorageSync("user",t)}},{key:"clearAuth",value:function(){this.token=null,this.user=null,uni.removeStorageSync("auth_token"),uni.removeStorageSync("user")}},{key:"login",value:function(){var e=(0,u.default)((0,o.default)().mark((function e(t,r){var n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.apiCall("/api/auth/login","POST",{username:t,password:r});case 3:if(n=e.sent,!n.token){e.next=7;break}return this.setAuth(n.token,n.user),e.abrupt("return",n);case 7:throw new Error("登录失败");case 10:throw e.prev=10,e.t0=e["catch"](0),e.t0;case 13:case"end":return e.stop()}}),e,this,[[0,10]])})));return function(t,r){return e.apply(this,arguments)}}()},{key:"guestLogin",value:function(e){if(!e||e.trim().length<2)throw new Error("请输入有效的显示名称");var t={id:"guest_"+Date.now(),username:e.trim(),role:"guest",isGuest:!0};return this.setAuth("guest_token",t),t}},{key:"logout",value:function(){this.clearAuth(),uni.reLaunch({url:"/pages/auth/login"})}},{key:"apiCall",value:function(){var e=(0,u.default)((0,o.default)().mark((function e(t){var r,n,u,i,s,c=arguments;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=c.length>1&&void 0!==c[1]?c[1]:"GET",n=c.length>2&&void 0!==c[2]?c[2]:null,u=getApp(),i=u.globalData&&u.globalData.baseUrl||"http://localhost:3016",s={url:i+t,method:r,header:{"Content-Type":"application/json"},timeout:3e4,sslVerify:!1,enableHttp2:!1,enableQuic:!1,enableCache:!1,enableHttpDNS:!1},n&&(s.data=n),this.token&&"guest_token"!==this.token&&(s.header.Authorization="Bearer ".concat(this.token)),e.abrupt("return",new Promise((function(e,t){uni.request((0,a.default)((0,a.default)({},s),{},{success:function(r){200===r.statusCode?e(r.data):t(new Error(r.data&&r.data.error||"请求失败"))},fail:function(e){t(new Error("网络请求失败"))}}))})));case 8:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),e}(),l=new c,f=l;t.default=f},b013:function(e,t,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("4626"),r("5ac7"),r("7a76"),r("c9b5");var a=n(r("2634")),o=n(r("9b1b")),u=n(r("2fdc")),i=n(r("80b1")),s=n(r("efe5")),c=n(r("3822")),l=n(r("20f5")),f=function(){function e(){(0,i.default)(this,e);var t=getApp();this.baseUrl=t.globalData&&t.globalData.baseUrl||"http://localhost:3016"}return(0,s.default)(e,[{key:"request",value:function(){var e=(0,u.default)((0,a.default)().mark((function e(t){var r,n,u,i,s,f,h,p=arguments;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=p.length>1&&void 0!==p[1]?p[1]:"GET",n=p.length>2&&void 0!==p[2]?p[2]:null,u=p.length>3&&void 0!==p[3]?p[3]:{},i=c.default.getAuthToken(),s={url:this.baseUrl+t,method:r,header:(0,o.default)({"Content-Type":"application/json"},u.header),timeout:u.timeout||3e4,sslVerify:!1,enableHttp2:!1,enableQuic:!1,enableCache:!1,enableHttpDNS:!1},n&&(s.data=n),i&&"guest_token"!==i&&(s.header.Authorization="Bearer ".concat(i)),e.prev=7,e.next=10,l.default.requestWithRetry(s);case 10:return f=e.sent,e.abrupt("return",f);case 14:if(e.prev=14,e.t0=e["catch"](7),!e.t0.message.includes("401")&&!e.t0.message.includes("认证失败")){e.next=20;break}throw c.default.clearAuth(),uni.reLaunch({url:"/pages/auth/login"}),new Error("认证失败，请重新登录");case 20:return e.next=22,l.default.diagnoseNetworkIssue(e.t0);case 22:throw h=e.sent,console.error("API请求失败:",h),e.t0;case 25:case"end":return e.stop()}}),e,this,[[7,14]])})));return function(t){return e.apply(this,arguments)}}()},{key:"get",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.request(e,"GET",null,t)}},{key:"post",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.request(e,"POST",t,r)}},{key:"put",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.request(e,"PUT",t,r)}},{key:"delete",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.request(e,"DELETE",null,t)}},{key:"getPublicRooms",value:function(){var e=(0,u.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/api/rooms/public"));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getMyRooms",value:function(){var e=(0,u.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/api/rooms/my-rooms"));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getRoomDetails",value:function(){var e=(0,u.default)((0,a.default)().mark((function e(t){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/api/rooms/".concat(t)));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"createRoom",value:function(){var e=(0,u.default)((0,a.default)().mark((function e(t){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/api/rooms",t));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"updateRoom",value:function(){var e=(0,u.default)((0,a.default)().mark((function e(t,r){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.put("/api/rooms/".concat(t),r));case 1:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()},{key:"deleteRoom",value:function(){var e=(0,u.default)((0,a.default)().mark((function e(t){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.delete("/api/rooms/".concat(t)));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"login",value:function(){var e=(0,u.default)((0,a.default)().mark((function e(t,r){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/api/auth/login",{username:t,password:r}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()},{key:"register",value:function(){var e=(0,u.default)((0,a.default)().mark((function e(t){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.post("/api/auth/register",t));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"verifyToken",value:function(){var e=(0,u.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.get("/api/auth/verify"));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),h=new f,p=h;t.default=p}}]);