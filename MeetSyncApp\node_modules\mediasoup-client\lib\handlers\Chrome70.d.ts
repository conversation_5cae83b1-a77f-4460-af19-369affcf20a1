import { type HandlerFactory, HandlerInterface, type HandlerRunOptions, type HandlerSendOptions, type HandlerSendResult, type HandlerReceiveOptions, type HandlerReceiveResult, type HandlerSendDataChannelOptions, type HandlerSendDataChannelResult, type HandlerReceiveDataChannelOptions, type HandlerReceiveDataChannelResult } from './HandlerInterface';
import type { IceParameters } from '../Transport';
import type { RtpCapabilities } from '../RtpParameters';
import type { SctpCapabilities } from '../SctpParameters';
export declare class Chrome70 extends HandlerInterface {
    private _direction?;
    private _remoteSdp?;
    private _sendingRtpParametersByKind?;
    private _sendingRemoteRtpParametersByKind?;
    private _forcedLocalDtlsRole?;
    private _pc;
    private readonly _mapMidTransceiver;
    private readonly _sendStream;
    private _hasDataChannelMediaSection;
    private _nextSendSctpStreamId;
    private _transportReady;
    /**
     * Creates a factory function.
     */
    static createFactory(): HandlerFactory;
    constructor();
    get name(): string;
    close(): void;
    getNativeRtpCapabilities(): Promise<RtpCapabilities>;
    getNativeSctpCapabilities(): Promise<SctpCapabilities>;
    run({ direction, iceParameters, iceCandidates, dtlsParameters, sctpParameters, iceServers, iceTransportPolicy, additionalSettings, proprietaryConstraints, extendedRtpCapabilities, }: HandlerRunOptions): void;
    updateIceServers(iceServers: RTCIceServer[]): Promise<void>;
    restartIce(iceParameters: IceParameters): Promise<void>;
    getTransportStats(): Promise<RTCStatsReport>;
    send({ track, encodings, codecOptions, codec, }: HandlerSendOptions): Promise<HandlerSendResult>;
    stopSending(localId: string): Promise<void>;
    pauseSending(localId: string): Promise<void>;
    resumeSending(localId: string): Promise<void>;
    replaceTrack(localId: string, track: MediaStreamTrack | null): Promise<void>;
    setMaxSpatialLayer(localId: string, spatialLayer: number): Promise<void>;
    setRtpEncodingParameters(localId: string, params: any): Promise<void>;
    getSenderStats(localId: string): Promise<RTCStatsReport>;
    sendDataChannel({ ordered, maxPacketLifeTime, maxRetransmits, label, protocol, }: HandlerSendDataChannelOptions): Promise<HandlerSendDataChannelResult>;
    receive(optionsList: HandlerReceiveOptions[]): Promise<HandlerReceiveResult[]>;
    stopReceiving(localIds: string[]): Promise<void>;
    pauseReceiving(localIds: string[]): Promise<void>;
    resumeReceiving(localIds: string[]): Promise<void>;
    getReceiverStats(localId: string): Promise<RTCStatsReport>;
    receiveDataChannel({ sctpStreamParameters, label, protocol, }: HandlerReceiveDataChannelOptions): Promise<HandlerReceiveDataChannelResult>;
    private setupTransport;
    private assertSendDirection;
    private assertRecvDirection;
}
//# sourceMappingURL=Chrome70.d.ts.map