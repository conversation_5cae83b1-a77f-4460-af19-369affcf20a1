package org.meetsync.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 邀请码实体类
 * 
 * <AUTHOR> Team
 */
@Entity
@Table(name = "invite_codes")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InviteCode {
    
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 邀请码（唯一）
     */
    @Column(name = "invite_code", nullable = false, unique = true, length = 20)
    private String inviteCode;
    
    /**
     * 邀请人ID
     */
    @Column(name = "inviter_id", nullable = false)
    private Long inviterId;
    
    /**
     * 邀请人用户对象
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "inviter_id", insertable = false, updatable = false)
    private User inviter;
    
    /**
     * 邀请时间
     */
    @Column(name = "invite_time", nullable = false)
    private LocalDateTime inviteTime;
    
    /**
     * 是否已使用
     */
    @Column(name = "is_used", nullable = false)
    @Builder.Default
    private Boolean isUsed = false;
    
    /**
     * 使用时间
     */
    @Column(name = "use_time")
    private LocalDateTime useTime;
    
    /**
     * 使用人ID
     */
    @Column(name = "user_id")
    private Long userId;
    
    /**
     * 使用人用户对象
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 实体创建前的回调
     */
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        if (updatedAt == null) {
            updatedAt = now;
        }
        if (inviteTime == null) {
            inviteTime = now;
        }
    }
    
    /**
     * 实体更新前的回调
     */
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 标记邀请码为已使用
     * 
     * @param userId 使用人ID
     */
    public void markAsUsed(Long userId) {
        this.isUsed = true;
        this.userId = userId;
        this.useTime = LocalDateTime.now();
    }
    
    /**
     * 检查邀请码是否可用
     * 
     * @return 是否可用
     */
    public boolean isAvailable() {
        return !isUsed;
    }
}
