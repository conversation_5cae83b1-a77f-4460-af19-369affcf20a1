# Compiled class files
*.class

# Log files
*.log
logs/
*.log.*

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Virtual machine crash logs
hs_err_pid*
replay_pid*

### Maven ###
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.properties
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

### Gradle ###
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

### IntelliJ IDEA ###
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

### Eclipse ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

### Operating Systems ###
# Mac OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

### Spring Boot ###
*.pid
application-*.properties
application-*.yml
!application.yml
!application.properties

### Database ###
*.db
*.sqlite
*.sqlite3
*.h2.db
*.trace.db
*.lock.db

### Environment Variables ###
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

### Security ###
*.key
*.pem
*.p12
*.jks
*.keystore
*.truststore
*.crt
*.cert
*.ca-bundle

### Temporary Files ###
*.tmp
*.temp
*.bak
*.backup
*.swp
*.swo
*.orig
*.rej

### Test Results ###
TEST-*.xml
test-results/
coverage/
*.lcov

### Application Specific ###
uploads/
downloads/
temp/
cache/
backup/
dumps/
dev/
local/
scratch/

### JVM ###
*.hprof
*.dump
*.mdmp
*.jfr

### IDE Workspace ###
.metadata
.recommenders
.sonarlint

### Lombok ###
.apt_generated_tests

### Application Logs ###
meetsync-java-server.log*
app.log*

### Docker ###
.dockerignore
Dockerfile.dev
docker-compose.override.yml

### Node.js (if any frontend tools) ###
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

### Custom ###
# Add project-specific ignores here