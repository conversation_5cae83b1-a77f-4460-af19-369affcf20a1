package org.meetsync.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.core.util.Json;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.meetsync.entity.User;
import org.meetsync.service.MediaProxyService;
import org.meetsync.service.RoomService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.function.Supplier;

@RestController
@RequestMapping("/media")
@CrossOrigin(origins = "*", maxAge = 3600)
@Tag(name = "媒体管理", description = "WebRTC媒体流管理API - 代理到Node.js媒体服务器")
public class MediaController {

    private static final Logger logger = LoggerFactory.getLogger(MediaController.class);

    @Autowired
    private MediaProxyService mediaProxyService;

    @Autowired
    private RoomService roomService;

    @Operation(
            summary = "获取媒体服务器状态",
            description = "检查Node.js媒体服务器的连接状态",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "状态获取成功"),
            @ApiResponse(responseCode = "503", description = "媒体服务器未连接")
    })
    @GetMapping("/status")
    public ResponseEntity<?> getMediaServerStatus() {
        try {
            JsonNode status = mediaProxyService.getMediaServerStatus();
            boolean connected = mediaProxyService.isMediaServerConnected();
            
            Map<String, Object> response = new HashMap<>();
            response.put("mediaServer", status);
            response.put("message", connected ? "媒体服务器连接正常" : "媒体服务器未连接");
            
            return ResponseEntity.status(connected ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE)
                    .body(response);
                    
        } catch (Exception e) {
            logger.error("获取媒体服务器状态失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("获取媒体服务器状态失败"));
        }
    }

    @Operation(
            summary = "创建媒体房间",
            description = "在媒体服务器中创建新的房间",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "房间创建成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "409", description = "房间已存在"),
            @ApiResponse(responseCode = "503", description = "媒体服务器未连接")
    })
    @PostMapping("/rooms")
    public ResponseEntity<?> createMediaRoom(@RequestBody Map<String, String> request) {

        if (!mediaProxyService.isMediaServerConnected()) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(createErrorResponse("媒体服务器未连接"));
        }

        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(createErrorResponse("用户未认证"));
        }

        String roomId = request.get("roomId");
        if (roomId == null || roomId.trim().isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("房间ID不能为空"));
        }

        // 检查用户是否有权限访问房间（如果房间存在的话）
        if (roomService.roomExists(roomId)) {
            // 房间存在，检查用户权限
            if (!roomService.canUserAccessRoom(currentUser.getId(), roomId, null)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(createErrorResponse("无权限访问该房间"));
            }
        }
        // 如果房间不存在，允许创建媒体房间（用于临时房间或自动创建的房间）

        try {
            JsonNode data = mediaProxyService.createMediaRoom(roomId).get();
            Map<String, Object> response = new HashMap<>();
            response.put("roomId", roomId);
            if (data.has("rtpCapabilities")) {
                response.put("rtpCapabilities", data.get("rtpCapabilities"));
            }
            response.put("message", "媒体房间创建成功");
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (Exception ex) {
            logger.error("创建媒体房间失败: {}", roomId, ex);
            if (ex.getCause() != null && ex.getCause().getMessage().contains("already exists")) {
                // 房间已存在不是错误，返回成功状态
                logger.info("媒体房间已存在: {}", roomId);
                Map<String, Object> response = new HashMap<>();
                response.put("roomId", roomId);
                response.put("message", "媒体房间已存在");
                return ResponseEntity.ok().body(response);
            }
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("创建媒体房间失败: " + ex.getMessage()));
        }
    }


    @Operation(
            summary = "获取房间RTP能力",
            description = "获取指定房间的RTP能力配置",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "404", description = "房间不存在"),
            @ApiResponse(responseCode = "503", description = "媒体服务器未连接")
    })
    @GetMapping("/rooms/{roomId}/rtp-capabilities")
    public ResponseEntity<?> getRtpCapabilities(
            @Parameter(description = "房间ID", required = true)
            @PathVariable String roomId) {

        if (!mediaProxyService.isMediaServerConnected()) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(createErrorResponse("媒体服务器未连接"));
        }

        // 验证用户权限和房间存在性
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(createErrorResponse("用户未认证"));
        }

        // 检查用户是否有权限访问房间（如果房间存在的话）
        if (roomService.roomExists(roomId)) {
            // 房间存在，检查用户权限
            if (!roomService.canUserAccessRoom(currentUser.getId(), roomId, null)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(createErrorResponse("无权限访问该房间"));
            }
        }
        // 如果房间不存在，允许获取RTP能力（用于临时房间或自动创建的房间）

        try {
            JsonNode data = mediaProxyService.getRtpCapabilities(roomId).get();
            Map<String, Object> response = new HashMap<>();
            response.put("roomId", roomId);
            if (data.has("rtpCapabilities")) {
                response.put("rtpCapabilities", data.get("rtpCapabilities"));
            }
            response.put("message", "RTP能力获取成功");
            return ResponseEntity.ok(response);
        } catch (Exception ex) {
            logger.error("获取RTP能力失败: {}", roomId, ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("获取RTP能力失败: " + ex.getMessage()));
        }
    }

    @Operation(
            summary = "创建WebRTC传输",
            description = "为用户创建发送或接收传输",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @PostMapping("/rooms/{roomId}/transports")
    public ResponseEntity<?> createTransport(
            @Parameter(description = "房间ID", required = true)
            @PathVariable String roomId,
            @RequestBody Map<String, String> request) {

        if (!mediaProxyService.isMediaServerConnected()) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(createErrorResponse("媒体服务器未连接"));
        }

        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(createErrorResponse("用户未认证"));
        }

        String direction = request.get("direction");
        if (direction == null || direction.trim().isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("传输方向不能为空"));
        }

        // 检查用户是否有权限访问房间（如果房间存在的话）
        if (roomService.roomExists(roomId)) {
            // 房间存在，检查用户权限
            if (!roomService.canUserAccessRoom(currentUser.getId(), roomId, null)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(createErrorResponse("无权限访问该房间"));
            }
        }

        if (!"send".equals(direction) && !"recv".equals(direction)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("传输方向必须是 'send' 或 'recv'"));
        }

        try {
            JsonNode data = mediaProxyService.createTransport(roomId, currentUser.getId(), direction).get();
            logger.info("NodeJS返回的传输数据: {}", data.toString());

            Map<String, Object> response = new HashMap<>();
            response.put("roomId", roomId);
            response.put("userId", currentUser.getId());
            response.put("direction", direction);
            // 将JsonNode转换为Map
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> transportData = mapper.convertValue(data, Map.class);

            logger.info("转换后的传输数据: {}", transportData);
            response.put("transport", transportData);
            response.put("message", "传输创建成功");
            return ResponseEntity.ok(response);
        } catch (Exception ex) {
            logger.error("创建传输失败: {}", roomId, ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("创建传输失败: " + ex.getMessage()));
        }
    }

    @Operation(
            summary = "连接WebRTC传输",
            description = "使用DTLS参数连接传输",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @PostMapping("/rooms/{roomId}/transports/{transportId}/connect")
    public ResponseEntity<?> connectTransport(
            @Parameter(description = "房间ID", required = true)
            @PathVariable String roomId,
            @Parameter(description = "传输ID", required = true)
            @PathVariable String transportId,
            @RequestBody JsonNode dtlsParameters) {

        if (!mediaProxyService.isMediaServerConnected()) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(createErrorResponse("媒体服务器未连接"));
        }

        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(createErrorResponse("用户未认证"));
        }

        try {
            logger.info("连接传输请求: roomId={}, transportId={}, dtlsParameters={}", roomId, transportId, dtlsParameters.toString());
            JsonNode data = mediaProxyService.connectTransport(roomId, currentUser.getId(), transportId, dtlsParameters).get();
            Map<String, Object> response = new HashMap<>();
            response.put("roomId", roomId);
            response.put("transportId", transportId);
            response.put("connected", true);
            response.put("message", "传输连接成功");
            return ResponseEntity.ok(response);
        } catch (Exception ex) {
            logger.error("连接传输失败: {}", transportId, ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("连接传输失败: " + ex.getMessage()));
        }
    }

    @Operation(
            summary = "创建媒体生产者",
            description = "创建音频或视频生产者",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @PostMapping("/rooms/{roomId}/producers")
    public ResponseEntity<?> createProducer(
            @Parameter(description = "房间ID", required = true)
            @PathVariable String roomId,
            @RequestBody Map<String, Object> request) {

        if (!mediaProxyService.isMediaServerConnected()) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(createErrorResponse("媒体服务器未连接"));
        }

        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(createErrorResponse("用户未认证"));
        }

        String transportId = (String) request.get("transportId");
        String kind = (String) request.get("kind");
        Object rtpParameters = request.get("rtpParameters");

        if (transportId == null || transportId.trim().isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("传输ID不能为空"));
        }

        if (kind == null || kind.trim().isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("媒体类型不能为空"));
        }

        if (!"audio".equals(kind) && !"video".equals(kind)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("媒体类型必须是 'audio' 或 'video'"));
        }

        if (rtpParameters == null) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("RTP参数不能为空"));
        }

        try {
            logger.info("创建生产者请求: roomId={}, transportId={}, kind={}", roomId, transportId, kind);

            // 将rtpParameters转换为JsonNode
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rtpParametersNode = objectMapper.valueToTree(rtpParameters);

            JsonNode data = mediaProxyService.createProducer(roomId, currentUser.getId(), transportId, kind, rtpParametersNode).get();
            logger.info("媒体服务器返回的生产者数据: {}", data.toString());

            Map<String, Object> response = new HashMap<>();
            response.put("roomId", roomId);
            response.put("userId", currentUser.getId());
            // 将JsonNode转换为Map
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> producerData = mapper.convertValue(data, Map.class);
            response.put("producer", producerData);
            response.put("message", "生产者创建成功");

            logger.info("最终响应数据: {}", response);
            return ResponseEntity.ok(response);
        } catch (Exception ex) {
            logger.error("创建生产者失败: roomId={}, transportId={}, kind={}", roomId, transportId, kind, ex);

            String errorMessage = ex.getMessage();
            if (errorMessage != null && errorMessage.contains("Transport") && errorMessage.contains("not found")) {
                // 传输不存在，可能是多设备登录导致的会话冲突
                return ResponseEntity.status(HttpStatus.CONFLICT)
                        .body(createErrorResponse("传输对象不存在，可能是多设备登录导致的会话冲突，请重新连接"));
            }

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("创建生产者失败: " + errorMessage));
        }
    }

    @Operation(
            summary = "获取房间生产者列表",
            description = "获取房间内其他用户的生产者列表",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @GetMapping("/rooms/{roomId}/producers")
    public ResponseEntity<?> getProducers(
            @Parameter(description = "房间ID", required = true)
            @PathVariable String roomId) {

        if (!mediaProxyService.isMediaServerConnected()) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(createErrorResponse("媒体服务器未连接"));
        }

        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(createErrorResponse("用户未认证"));
        }

        try {
            JsonNode data = mediaProxyService.getProducers(roomId, currentUser.getId()).get();
            Map<String, Object> response = new HashMap<>();
            response.put("roomId", roomId);
            if (data.has("producers")) {
                response.put("producers", data.get("producers"));
            } else {
                response.put("producers", new ArrayList<>());
            }
            response.put("message", "生产者列表获取成功");
            return ResponseEntity.ok(response);
        } catch (Exception ex) {
            logger.error("获取生产者列表失败: {}", roomId, ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("获取生产者列表失败: " + ex.getMessage()));
        }
    }

    @Operation(
            summary = "创建消费者",
            description = "创建媒体消费者",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @PostMapping("/rooms/{roomId}/consumers")
    public ResponseEntity<?> createConsumer(
            @Parameter(description = "房间ID", required = true)
            @PathVariable String roomId,
            @RequestBody Map<String, Object> request) {

        if (!mediaProxyService.isMediaServerConnected()) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(createErrorResponse("媒体服务器未连接"));
        }

        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(createErrorResponse("用户未认证"));
        }

        String transportId = (String) request.get("transportId");
        String producerId = (String) request.get("producerId");
        Object rtpCapabilities = request.get("rtpCapabilities");

        if (transportId == null || transportId.trim().isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("传输ID不能为空"));
        }

        if (producerId == null || producerId.trim().isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("生产者ID不能为空"));
        }

        if (rtpCapabilities == null) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("RTP能力不能为空"));
        }

        try {
            logger.info("创建消费者请求: roomId={}, transportId={}, producerId={}", roomId, transportId, producerId);

            // 将rtpCapabilities转换为JsonNode
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rtpCapabilitiesNode = objectMapper.valueToTree(rtpCapabilities);

            JsonNode data = mediaProxyService.createConsumer(roomId, currentUser.getId(), transportId, producerId, rtpCapabilitiesNode).get();
            logger.info("媒体服务器返回的消费者数据: {}", data.toString());

            Map<String, Object> response = new HashMap<>();
            response.put("roomId", roomId);
            response.put("userId", currentUser.getId());
            // 将JsonNode转换为Map
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> consumerData = mapper.convertValue(data, Map.class);
            response.put("consumer", consumerData);
            response.put("message", "消费者创建成功");

            logger.info("最终响应数据: {}", response);
            return ResponseEntity.ok(response);
        } catch (Exception ex) {
            logger.error("创建消费者失败: roomId={}, producerId={}", roomId, producerId, ex);

            String errorMessage = ex.getMessage();
            if (errorMessage != null) {
                if (errorMessage.contains("Producer not found") || errorMessage.contains("incompatible")) {
                    return ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body(createErrorResponse("生产者不存在或已关闭，无法创建消费者"));
                } else if (errorMessage.contains("Room not found")) {
                    return ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body(createErrorResponse("房间不存在"));
                } else if (errorMessage.contains("Transport") && errorMessage.contains("not found")) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(createErrorResponse("传输通道不存在，请先创建传输"));
                }
            }

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("创建消费者失败: " + errorMessage));
        }
    }

    @Operation(
            summary = "关闭生产者",
            description = "关闭指定的生产者",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @DeleteMapping("/rooms/{roomId}/producers/{producerId}")
    public ResponseEntity<?> closeProducer(
            @Parameter(description = "房间ID", required = true)
            @PathVariable String roomId,
            @Parameter(description = "生产者ID", required = true)
            @PathVariable String producerId) {

        if (!mediaProxyService.isMediaServerConnected()) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(createErrorResponse("媒体服务器未连接"));
        }

        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(createErrorResponse("用户未认证"));
        }

        // 验证生产者ID
        if (producerId == null || producerId.trim().isEmpty() || "undefined".equals(producerId)) {
            logger.error("无效的生产者ID: {}", producerId);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(createErrorResponse("生产者ID无效"));
        }

        try {
            logger.info("关闭生产者请求: roomId={}, userId={}, producerId={}", roomId, currentUser.getId(), producerId);
            JsonNode data = mediaProxyService.closeProducer(roomId, currentUser.getId(), producerId).get();
            Map<String, Object> response = new HashMap<>();
            response.put("roomId", roomId);
            response.put("producerId", producerId);
            response.put("closed", true);
            response.put("message", "生产者关闭成功");
            return ResponseEntity.ok(response);
        } catch (Exception ex) {
            logger.error("关闭生产者失败: {}", producerId, ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("关闭生产者失败: " + ex.getMessage()));
        }
    }

    @Operation(
            summary = "关闭消费者",
            description = "关闭指定的消费者",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @DeleteMapping("/rooms/{roomId}/consumers/{consumerId}")
    public ResponseEntity<?> closeConsumer(
            @Parameter(description = "房间ID", required = true)
            @PathVariable String roomId,
            @Parameter(description = "消费者ID", required = true)
            @PathVariable String consumerId) {

        if (!mediaProxyService.isMediaServerConnected()) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(createErrorResponse("媒体服务器未连接"));
        }

        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(createErrorResponse("用户未认证"));
        }

        try {
            JsonNode data = mediaProxyService.closeConsumer(roomId, currentUser.getId(), consumerId).get();
            Map<String, Object> response = new HashMap<>();
            response.put("roomId", roomId);
            response.put("consumerId", consumerId);
            response.put("closed", true);
            response.put("message", "消费者关闭成功");
            return ResponseEntity.ok(response);
        } catch (Exception ex) {
            logger.error("关闭消费者失败: {}", consumerId, ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("关闭消费者失败: " + ex.getMessage()));
        }
    }

    @Operation(
            summary = "恢复消费者",
            description = "恢复指定的消费者",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @PostMapping("/rooms/{roomId}/consumers/{consumerId}/resume")
    public ResponseEntity<?> resumeConsumer(
            @Parameter(description = "房间ID", required = true)
            @PathVariable String roomId,
            @Parameter(description = "消费者ID", required = true)
            @PathVariable String consumerId) {

        if (!mediaProxyService.isMediaServerConnected()) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(createErrorResponse("媒体服务器未连接"));
        }

        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(createErrorResponse("用户未认证"));
        }

        try {
            JsonNode data = mediaProxyService.resumeConsumer(roomId, currentUser.getId(), consumerId).get();
            Map<String, Object> response = new HashMap<>();
            response.put("roomId", roomId);
            response.put("consumerId", consumerId);
            response.put("resumed", true);
            response.put("message", "消费者恢复成功");
            return ResponseEntity.ok(response);
        } catch (Exception ex) {
            logger.error("恢复消费者失败: {}", consumerId, ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("恢复消费者失败: " + ex.getMessage()));
        }
    }

    @Operation(
            summary = "清理用户生产者",
            description = "清理用户的所有生产者（用于页面刷新后的清理）",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @PostMapping("/rooms/{roomId}/cleanup-producers")
    public ResponseEntity<?> cleanupUserProducers(
            @Parameter(description = "房间ID", required = true)
            @PathVariable String roomId) {

        if (!mediaProxyService.isMediaServerConnected()) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(createErrorResponse("媒体服务器未连接"));
        }

        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(createErrorResponse("用户未认证"));
        }

        try {
            logger.info("清理用户生产者请求: roomId={}, userId={}", roomId, currentUser.getId());
            JsonNode data = mediaProxyService.cleanupUserProducers(roomId, currentUser.getId()).get();
            Map<String, Object> response = new HashMap<>();
            response.put("roomId", roomId);
            response.put("userId", currentUser.getId());
            response.put("cleaned", true);
            if (data.has("count")) {
                response.put("count", data.get("count").asInt());
            }
            response.put("message", "用户生产者清理成功");
            return ResponseEntity.ok(response);
        } catch (Exception ex) {
            logger.error("清理用户生产者失败: roomId={}, userId={}", roomId, currentUser.getId(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(createErrorResponse("清理用户生产者失败: " + ex.getMessage()));
        }
    }

    // 辅助方法

    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof User) {
            return (User) authentication.getPrincipal();
        }
        return null;
    }

    private Map<String, String> createErrorResponse(String message) {
        Map<String, String> error = new HashMap<>();
        error.put("error", message);
        return error;
    }




}
