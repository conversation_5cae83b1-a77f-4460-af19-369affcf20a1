{"name": "mediasoup-client", "version": "3.11.0", "description": "mediasoup client side TypeScript library", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>> (https://inakibaz.me)", "<PERSON> <<EMAIL>> (https://github.com/jmillan)"], "license": "ISC", "homepage": "https://mediasoup.org", "repository": {"type": "git", "url": "git+https://github.com/versatica/mediasoup-client.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mediasoup"}, "main": "lib/index.js", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./types": {"types": "./lib/types.d.ts", "default": "./lib/types.js"}, "./ortc": {"ortc": "./lib/ortc.d.ts", "default": "./lib/ortc.js"}, "./handlers/*": {"types": "./lib/handlers/*.d.ts", "default": "./lib/handlers/*.js"}, "./fakeParameters": {"types": "./lib/test/fakeParameters.d.ts", "default": "./lib/test/fakeParameters.js"}}, "files": ["LICENSE", "README.md", "npm-scripts.mjs", "lib"], "engines": {"node": ">=18"}, "keywords": ["webrtc", "ortc", "browser", "nodejs"], "scripts": {"prepare": "node npm-scripts.mjs prepare", "typescript:build": "node npm-scripts.mjs typescript:build", "typescript:watch": "node npm-scripts.mjs typescript:watch", "lint": "node npm-scripts.mjs lint", "format": "node npm-scripts.mjs format", "test": "node npm-scripts.mjs test", "coverage": "node npm-scripts.mjs coverage", "release:check": "node npm-scripts.mjs release:check", "release": "node npm-scripts.mjs release"}, "dependencies": {"@types/debug": "^4.1.12", "@types/npm-events-package": "npm:@types/events@^3.0.3", "awaitqueue": "^3.2.0", "debug": "^4.4.1", "fake-mediastreamtrack": "^2.1.0", "h264-profile-level-id": "^2.2.0", "npm-events-package": "npm:events@^3.3.0", "sdp-transform": "^2.15.0", "supports-color": "^10.0.0", "ua-parser-js": "^2.0.3"}, "devDependencies": {"@eslint/js": "^9.26.0", "@types/jest": "^29.5.14", "@types/node": "^22.15.18", "@types/sdp-transform": "^2.4.9", "@types/ua-parser-js": "^0.7.39", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-prettier": "^5.4.0", "globals": "^16.1.0", "jest": "^29.7.0", "open-cli": "^8.0.0", "prettier": "^3.5.3", "ts-jest": "^29.3.2", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1"}}