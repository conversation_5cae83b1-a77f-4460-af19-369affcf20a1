#!/usr/bin/env node

/**
 * 开发环境启动脚本
 */

const { execSync } = require('child_process')
const path = require('path')
const fs = require('fs')

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function checkNodeVersion() {
  const nodeVersion = process.version
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])
  
  if (majorVersion < 14) {
    log('❌ Node.js 版本过低，请升级到 14.0.0 或更高版本', 'red')
    process.exit(1)
  }
  
  log(`✅ Node.js 版本: ${nodeVersion}`, 'green')
}

function checkDependencies() {
  const packageJsonPath = path.join(__dirname, '../package.json')
  
  if (!fs.existsSync(packageJsonPath)) {
    log('❌ package.json 文件不存在', 'red')
    process.exit(1)
  }
  
  const nodeModulesPath = path.join(__dirname, '../node_modules')
  
  if (!fs.existsSync(nodeModulesPath)) {
    log('📦 正在安装依赖...', 'yellow')
    try {
      execSync('npm install', { 
        cwd: path.join(__dirname, '..'),
        stdio: 'inherit' 
      })
      log('✅ 依赖安装完成', 'green')
    } catch (error) {
      log('❌ 依赖安装失败', 'red')
      process.exit(1)
    }
  } else {
    log('✅ 依赖已安装', 'green')
  }
}

function showMenu() {
  log('\n🚀 MeetSync 开发环境', 'cyan')
  log('请选择运行平台:', 'bright')
  log('1. H5 (浏览器)', 'blue')
  log('2. 微信小程序', 'blue')
  log('3. App (Android/iOS)', 'blue')
  log('4. 全部平台', 'blue')
  log('5. 退出', 'red')
}

function runCommand(command, platform) {
  log(`\n🔧 正在启动 ${platform} 开发环境...`, 'yellow')
  
  try {
    execSync(command, {
      cwd: path.join(__dirname, '..'),
      stdio: 'inherit'
    })
  } catch (error) {
    log(`❌ ${platform} 启动失败`, 'red')
    console.error(error.message)
  }
}

function handleUserInput(input) {
  const choice = input.trim()
  
  switch (choice) {
    case '1':
      runCommand('npm run dev:h5', 'H5')
      break
    case '2':
      runCommand('npm run dev:mp-weixin', '微信小程序')
      break
    case '3':
      runCommand('npm run dev:app-plus', 'App')
      break
    case '4':
      log('\n🔧 正在启动所有平台...', 'yellow')
      runCommand('npm run dev:h5 & npm run dev:mp-weixin & npm run dev:app-plus', '所有平台')
      break
    case '5':
      log('👋 再见!', 'green')
      process.exit(0)
      break
    default:
      log('❌ 无效选择，请重新输入', 'red')
      promptUser()
      break
  }
}

function promptUser() {
  const readline = require('readline')
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  })
  
  rl.question('\n请输入选项 (1-5): ', (answer) => {
    rl.close()
    handleUserInput(answer)
  })
}

function checkConfig() {
  const configPath = path.join(__dirname, '../config/index.js')
  
  if (!fs.existsSync(configPath)) {
    log('⚠️  配置文件不存在，请检查 config/index.js', 'yellow')
    return false
  }
  
  try {
    const config = require(configPath)
    
    if (config.default.server.baseUrl.includes('your-domain.com')) {
      log('⚠️  请在 config/index.js 中配置正确的服务器地址', 'yellow')
      log('   当前地址: ' + config.default.server.baseUrl, 'yellow')
    } else {
      log('✅ 配置文件检查通过', 'green')
    }
    
    return true
  } catch (error) {
    log('❌ 配置文件格式错误', 'red')
    console.error(error.message)
    return false
  }
}

function showTips() {
  log('\n💡 开发提示:', 'cyan')
  log('• 确保后端服务器已启动', 'blue')
  log('• H5 开发需要 HTTPS 环境 (WebRTC 要求)', 'blue')
  log('• 移动端需要真机调试 (摄像头/麦克风权限)', 'blue')
  log('• 使用 Chrome DevTools 调试 H5 版本', 'blue')
  log('• 使用微信开发者工具调试小程序版本', 'blue')
}

function main() {
  log('🎬 MeetSync 开发环境启动器', 'magenta')
  log('=====================================', 'magenta')
  
  // 检查环境
  checkNodeVersion()
  checkDependencies()
  checkConfig()
  
  // 显示提示
  showTips()
  
  // 显示菜单
  showMenu()
  
  // 等待用户输入
  promptUser()
}

// 处理退出信号
process.on('SIGINT', () => {
  log('\n\n👋 开发环境已停止', 'yellow')
  process.exit(0)
})

process.on('SIGTERM', () => {
  log('\n\n👋 开发环境已停止', 'yellow')
  process.exit(0)
})

// 启动
main()
