package org.meetsync.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/test")  // 注意：由于context-path是/api，实际路径是/api/test
@CrossOrigin(origins = "*", maxAge = 3600)
@Tag(name = "测试接口", description = "用于测试Swagger功能的接口")
public class TestController {

    @Operation(
            summary = "Swagger测试接口",
            description = "测试Swagger是否正常工作"
    )
    @ApiResponse(responseCode = "200", description = "测试成功")
    @GetMapping("/swagger")
    public ResponseEntity<?> testSwagger() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Swagger功能正常！");
        response.put("timestamp", LocalDateTime.now());
        response.put("status", "success");

        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Lombok测试接口",
            description = "测试Lombok是否正常工作"
    )
    @ApiResponse(responseCode = "200", description = "测试成功")
    @GetMapping("/lombok")
    public ResponseEntity<?> testLombok() {
        // 这里使用了Lombok生成的方法
        TestData data = new TestData("Lombok功能正常！", LocalDateTime.now(), true);

        Map<String, Object> response = new HashMap<>();
        response.put("data", data);
        response.put("message", "Lombok注解工作正常");

        return ResponseEntity.ok(response);
    }

    // 内部测试类，使用Lombok注解
    public static class TestData {
        private String message;
        private LocalDateTime timestamp;
        private boolean success;

        public TestData(String message, LocalDateTime timestamp, boolean success) {
            this.message = message;
            this.timestamp = timestamp;
            this.success = success;
        }

        // Getters (在实际项目中会用@Data注解自动生成)
        public String getMessage() { return message; }
        public LocalDateTime getTimestamp() { return timestamp; }
        public boolean isSuccess() { return success; }
    }
}
