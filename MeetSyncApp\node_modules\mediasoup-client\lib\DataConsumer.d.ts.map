{"version": 3, "file": "DataConsumer.d.ts", "sourceRoot": "", "sources": ["../src/DataConsumer.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AACxD,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AAC7D,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,SAAS,CAAC;AAIvC,MAAM,MAAM,mBAAmB,CAAC,mBAAmB,SAAS,OAAO,GAAG,OAAO,IAC5E;IACC,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,oBAAoB,EAAE,oBAAoB,CAAC;IAC3C,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,OAAO,CAAC,EAAE,mBAAmB,CAAC;CAC9B,CAAC;AAEH,MAAM,MAAM,oBAAoB,GAC/B,oBAAoB,CAAC,0BAA0B,CAAC,CAAC;AAElD,MAAM,MAAM,kBAAkB,GAAG;IAChC,cAAc,EAAE,EAAE,CAAC;IACnB,IAAI,EAAE,EAAE,CAAC;IACT,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC;IACf,KAAK,EAAE,EAAE,CAAC;IACV,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;IAEf,QAAQ,EAAE,EAAE,CAAC;CACb,CAAC;AAEF,MAAM,MAAM,0BAA0B,GAAG;IACxC,KAAK,EAAE,EAAE,CAAC;CACV,CAAC;AAEF,qBAAa,YAAY,CACxB,mBAAmB,SAAS,OAAO,GAAG,OAAO,CAC5C,SAAQ,oBAAoB,CAAC,kBAAkB,CAAC;IAEjD,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAS;IAE7B,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAS;IAEzC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAiB;IAE9C,OAAO,CAAC,OAAO,CAAS;IAExB,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAuB;IAE7D,OAAO,CAAC,QAAQ,CAAsB;IAEtC,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,oBAAoB,CACM;gBAE5C,EACX,EAAE,EACF,cAAc,EACd,WAAW,EACX,oBAAoB,EACpB,OAAO,GACP,EAAE;QACF,EAAE,EAAE,MAAM,CAAC;QACX,cAAc,EAAE,MAAM,CAAC;QACvB,WAAW,EAAE,cAAc,CAAC;QAC5B,oBAAoB,EAAE,oBAAoB,CAAC;QAC3C,OAAO,CAAC,EAAE,mBAAmB,CAAC;KAC9B;IAcD;;OAEG;IACH,IAAI,EAAE,IAAI,MAAM,CAEf;IAED;;OAEG;IACH,IAAI,cAAc,IAAI,MAAM,CAE3B;IAED;;OAEG;IACH,IAAI,MAAM,IAAI,OAAO,CAEpB;IAED;;OAEG;IACH,IAAI,oBAAoB,IAAI,oBAAoB,CAE/C;IAED;;OAEG;IACH,IAAI,UAAU,IAAI,mBAAmB,CAEpC;IAED;;OAEG;IACH,IAAI,KAAK,IAAI,MAAM,CAElB;IAED;;OAEG;IACH,IAAI,QAAQ,IAAI,MAAM,CAErB;IAED;;OAEG;IACH,IAAI,UAAU,IAAI,UAAU,CAE3B;IAED;;OAEG;IACH,IAAI,UAAU,CAAC,UAAU,EAAE,UAAU,EAEpC;IAED;;OAEG;IACH,IAAI,OAAO,IAAI,mBAAmB,CAEjC;IAED;;OAEG;IACH,IAAI,OAAO,CAAC,OAAO,EAAE,mBAAmB,EAEvC;IAED,IAAI,QAAQ,IAAI,oBAAoB,CAEnC;IAED;;OAEG;IACH,KAAK,IAAI,IAAI;IAiBb;;OAEG;IACH,eAAe,IAAI,IAAI;IAiBvB,OAAO,CAAC,iBAAiB;CA2DzB"}