# MeetSync 服务端 Docker 管理 Makefile

.PHONY: help start stop restart logs build clean status health backup verify-db

# 默认目标
help:
	@echo "MeetSync 服务端 Docker 管理命令:"
	@echo ""
	@echo "  start      - 启动所有服务"
	@echo "  stop       - 停止所有服务"
	@echo "  restart    - 重启所有服务"
	@echo "  logs       - 查看所有服务日志"
	@echo "  build      - 重新构建镜像"
	@echo "  clean      - 清理所有容器和数据卷"
	@echo "  status     - 查看服务状态"
	@echo "  health     - 检查服务健康状态"
	@echo "  verify-db  - 验证数据库初始化"
	@echo "  backup     - 备份数据库"
	@echo ""
	@echo "示例:"
	@echo "  make start      # 启动服务"
	@echo "  make verify-db  # 验证数据库"
	@echo "  make logs       # 查看日志"
	@echo "  make stop       # 停止服务"

# 启动服务
start:
	@echo "🚀 启动 MeetSync 服务..."
	docker-compose up -d
	@echo "✅ 服务启动完成"
	@echo "📱 访问地址: http://localhost:3016"

# 停止服务
stop:
	@echo "🛑 停止 MeetSync 服务..."
	docker-compose down
	@echo "✅ 服务已停止"

# 重启服务
restart:
	@echo "🔄 重启 MeetSync 服务..."
	docker-compose restart
	@echo "✅ 服务重启完成"

# 查看日志
logs:
	@echo "📋 查看服务日志 (按 Ctrl+C 退出):"
	docker-compose logs -f

# 重新构建镜像
build:
	@echo "🔧 重新构建镜像..."
	docker-compose build --no-cache
	@echo "✅ 镜像构建完成"

# 清理所有容器和数据卷
clean:
	@echo "⚠️  警告: 这将删除所有数据!"
	@read -p "确定要继续吗? (y/N): " confirm && [ "$$confirm" = "y" ]
	@echo "🧹 清理容器和数据卷..."
	docker-compose down -v
	docker system prune -f
	@echo "✅ 清理完成"

# 查看服务状态
status:
	@echo "📊 服务状态:"
	docker-compose ps

# 检查服务健康状态
health:
	@echo "🏥 检查服务健康状态:"
	@echo ""
	@echo "MySQL:"
	@docker-compose exec mysql mysqladmin ping -h localhost -u root -p$${MYSQL_ROOT_PASSWORD} >/dev/null 2>&1 && echo "✅ MySQL 健康" || echo "❌ MySQL 不健康"
	@echo ""
	@echo "MeetSync 服务器:"
	@if curl -f http://localhost:3016/health >/dev/null 2>&1; then \
		echo "✅ 服务器健康"; \
		echo "📊 详细状态:"; \
		curl -s http://localhost:3016/health | python3 -m json.tool 2>/dev/null || curl -s http://localhost:3016/health; \
	else \
		echo "❌ 服务器不健康"; \
	fi

# 备份数据库
backup:
	@echo "💾 备份数据库..."
	@mkdir -p backups
	@docker-compose exec mysql mysqldump -u root -p$${MYSQL_ROOT_PASSWORD} mediasoup_rooms > backups/backup_$$(date +%Y%m%d_%H%M%S).sql
	@echo "✅ 数据库备份完成，保存在 backups/ 目录"

# 开发模式启动
dev:
	@echo "🔧 开发模式启动..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
	@echo "✅ 开发环境启动完成"

# 生产模式启动
prod: start

# 查看 MySQL 日志
mysql-logs:
	@echo "📋 MySQL 日志:"
	docker-compose logs -f mysql

# 查看服务器日志
server-logs:
	@echo "📋 MeetSync 服务器日志:"
	docker-compose logs -f meetsync-server

# 进入 MySQL 容器
mysql-shell:
	@echo "🐚 进入 MySQL 容器..."
	docker-compose exec mysql bash

# 进入服务器容器
server-shell:
	@echo "🐚 进入 MeetSync 服务器容器..."
	docker-compose exec meetsync-server bash

# 验证数据库初始化
verify-db:
	@echo "🗄️  验证数据库初始化..."
	@./verify-database.sh

# 更新服务
update:
	@echo "🔄 更新服务..."
	docker-compose pull
	docker-compose up -d
	@echo "✅ 服务更新完成"
