# Swagger API 文档使用指南

## 概述

MeetSync Java服务已集成Swagger/OpenAPI 3，提供完整的API文档和在线测试功能。

## 访问地址

启动应用后，可以通过以下地址访问API文档：

- **Swagger UI**: http://localhost:8080/api/swagger-ui.html
- **API文档JSON**: http://localhost:8080/api/v3/api-docs
- **API文档YAML**: http://localhost:8080/api/v3/api-docs.yaml

## 功能特性

### 1. 完整的API文档
- 📋 所有API端点的详细说明
- 📝 请求/响应参数说明
- 🔍 数据模型定义
- 💡 示例数据

### 2. 在线测试
- 🧪 直接在浏览器中测试API
- 🔐 支持JWT认证
- 📊 实时查看响应结果
- 📋 自动生成请求代码

### 3. JWT认证支持
- 🔑 内置JWT令牌认证
- 🛡️ 安全的API测试
- 🔄 令牌自动管理

## 使用步骤

### 1. 启动应用
```bash
.\start-java-server.bat
```

### 2. 访问Swagger UI
打开浏览器访问：http://localhost:8080/api/swagger-ui.html

### 3. 用户注册/登录
1. 展开 **认证管理** 分组
2. 使用 `POST /api/auth/register` 注册新用户
3. 或使用 `POST /api/auth/login` 登录现有用户

### 4. 设置JWT认证
1. 复制登录响应中的 `token` 值
2. 点击页面右上角的 **Authorize** 按钮
3. 在弹出框中输入JWT令牌（不需要'Bearer '前缀）
4. 点击 **Authorize** 确认

### 5. 测试受保护的API
设置认证后，可以测试需要登录的API：
- 获取用户信息
- 房间管理
- 权限管理等

## API分组说明

### 认证管理 (Authentication)
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/profile` - 获取用户信息
- `PUT /api/auth/profile` - 更新用户信息
- `PUT /api/auth/change-password` - 修改密码
- `POST /api/auth/validate` - 验证令牌

### 房间管理 (Room Management)
- `GET /api/rooms/public` - 获取公开房间
- `GET /api/rooms/my-rooms` - 获取我的房间
- `POST /api/rooms` - 创建房间
- `GET /api/rooms/{roomId}` - 获取房间详情
- `PUT /api/rooms/{roomId}` - 更新房间
- `DELETE /api/rooms/{roomId}` - 删除房间

### 健康检查 (Health Check)
- `GET /api/health` - 健康检查
- `GET /api/ping` - Ping测试

## 数据模型

### 用户相关
- **UserRegistrationDto** - 用户注册请求
- **UserLoginDto** - 用户登录请求
- **UserResponseDto** - 用户信息响应
- **JwtResponseDto** - JWT登录响应

### 房间相关
- **RoomCreateDto** - 房间创建请求
- **RoomResponseDto** - 房间信息响应

### 枚举类型
- **UserRole** - 用户角色 (guest, user, premium, admin)
- **PermissionType** - 权限类型 (view_only, audio_only, video_audio, full_access)

## 示例数据

### 用户注册
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "user"
}
```

### 用户登录
```json
{
  "username": "testuser",
  "password": "password123"
}
```

### 房间创建
```json
{
  "roomId": "test-room-001",
  "name": "测试房间",
  "description": "这是一个测试房间",
  "maxParticipants": 10,
  "isPublic": true,
  "requiredRole": "user"
}
```

## 开发集成

### 生成客户端代码
Swagger支持生成多种语言的客户端代码：

1. 访问 http://localhost:8080/api-docs
2. 复制JSON内容
3. 使用 [Swagger Codegen](https://swagger.io/tools/swagger-codegen/) 生成客户端

### API测试
可以使用Swagger UI进行API测试，也可以导出为：
- Postman Collection
- Insomnia Collection
- curl命令

## 配置说明

### Swagger配置
```yaml
springdoc:
  api-docs:
    path: /api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    try-it-out-enabled: true
```

### 安全配置
```java
@SecurityRequirement(name = "Bearer Authentication")
```

## 生产环境注意事项

### 1. 禁用Swagger UI
在生产环境中，建议禁用Swagger UI：
```yaml
springdoc:
  swagger-ui:
    enabled: false
```

### 2. 限制访问
可以通过Spring Security限制Swagger访问：
```java
.requestMatchers("/swagger-ui/**", "/api-docs/**").hasRole("ADMIN")
```

### 3. 自定义域名
更新服务器配置以使用正确的域名：
```java
new Server()
    .url("https://api.yourdomain.com/api")
    .description("生产服务器")
```

## 故障排除

### 1. 无法访问Swagger UI
- 检查应用是否正常启动
- 确认端口8080未被占用
- 验证URL路径是否正确

### 2. JWT认证失败
- 确保令牌格式正确
- 检查令牌是否过期
- 验证用户是否存在且激活

### 3. API测试失败
- 检查请求参数是否正确
- 验证数据库连接
- 查看应用日志获取详细错误信息

## 扩展功能

### 1. 自定义注解
可以添加更多Swagger注解来丰富文档：
```java
@ApiResponse(responseCode = "200", description = "成功")
@Parameter(description = "用户ID", example = "1")
```

### 2. 分组管理
可以按功能模块对API进行分组：
```java
@Tag(name = "用户管理", description = "用户相关操作")
```

### 3. 版本管理
支持API版本管理：
```java
@RequestMapping("/api/v1/users")
@RequestMapping("/api/v2/users")
```

通过Swagger，开发者可以更好地理解和使用MeetSync的API，提高开发效率和API的可维护性。
