{"name": "awaitqueue", "version": "3.2.0", "description": "TypeScript utility to enqueue asynchronous tasks and run them sequentially one after another", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://inakibaz.me)", "license": "ISC", "repository": {"type": "git", "url": "https://github.com/versatica/awaitqueue.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mediasoup"}, "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["LICENSE", "README.md", "npm-scripts.mjs", "lib"], "engines": {"node": ">=18"}, "scripts": {"prepare": "node npm-scripts.mjs prepare", "typescript:build": "node npm-scripts.mjs typescript:build", "typescript:watch": "node npm-scripts.mjs typescript:watch", "lint": "node npm-scripts.mjs lint", "format": "node npm-scripts.mjs format", "test": "node npm-scripts.mjs test", "coverage": "node npm-scripts.mjs coverage", "release:check": "node npm-scripts.mjs release:check", "release": "node npm-scripts.mjs release"}, "dependencies": {"debug": "^4.4.0"}, "devDependencies": {"@eslint/js": "^9.23.0", "@types/debug": "^4.1.12", "@types/jest": "^29.5.14", "@typescript-eslint/eslint-plugin": "^8.27.0", "@typescript-eslint/parser": "^8.27.0", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-prettier": "^5.2.4", "globals": "^16.0.0", "jest": "^29.7.0", "open-cli": "^8.0.0", "prettier": "^3.5.3", "ts-jest": "^29.3.0", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0"}}