/**
 * Clones the given value.
 */
export declare function clone<T>(value: T): T;
/**
 * Generates a random positive integer.
 */
export declare function generateRandomNumber(): number;
/**
 * Make an object or array recursively immutable.
 * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/freeze.
 */
export declare function deepFreeze<T>(object: T): T;
//# sourceMappingURL=utils.d.ts.map