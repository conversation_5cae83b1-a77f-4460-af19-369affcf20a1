<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="007e77c0-2ba0-48f3-8629-7208fd5a6a69" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/java/org/meetsync/dto/UserRegistrationDto.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/org/meetsync/dto/UserRegistrationDto.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/org/meetsync/dto/UserResponseDto.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/org/meetsync/dto/UserResponseDto.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/org/meetsync/entity/User.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/org/meetsync/entity/User.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/org/meetsync/repository/UserRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/org/meetsync/repository/UserRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/org/meetsync/service/MediaProxyService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/org/meetsync/service/MediaProxyService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/org/meetsync/service/UserService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/org/meetsync/service/UserService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/org/meetsync/websocket/ClientWebSocketHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/org/meetsync/websocket/ClientWebSocketHandler.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2y82DzI6g6xsF4mg96YSQyMvtOY" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Batch.init-git.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "last_opened_file_path": "D:/GitProjects/MeetSync/MeetSyncJavaServer/src/main/resources",
    "onboarding.tips.debug.path": "D:/GitProjects/MeetSync/MeetSyncJavaServer/src/main/java/org/meetsync/Main.java",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "应用程序.App.executor": "Debug"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\GitProjects\MeetSync\MeetSyncJavaServer\src\main\resources" />
    </key>
  </component>
  <component name="RunManager" selected="应用程序.App">
    <configuration name="App" type="Application" factoryName="Application">
      <option name="MAIN_CLASS_NAME" value="org.meetsync.MeetSyncApplication" />
      <module name="MeetSyncJavaServer" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="init-git" type="BatchConfigurationType" factoryName="Batch" temporary="true">
      <module name="MeetSyncJavaServer" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SCRIPT_NAME" value="init-git.bat" />
      <option name="PARAMETERS" value="" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Batch.init-git" />
      <item itemvalue="应用程序.App" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Batch.init-git" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="007e77c0-2ba0-48f3-8629-7208fd5a6a69" name="更改" comment="" />
      <created>1749201765401</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749201765401</updated>
    </task>
    <servers />
  </component>
  <component name="XDebuggerManager">
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="com.fasterxml.jackson.databind.node.ArrayNode" memberName="_children" />
        <PinnedItemInfo parentTag="com.fasterxml.jackson.databind.node.ObjectNode" memberName="_children" />
      </pinned-members>
    </pin-to-top-manager>
    <watches-manager>
      <configuration name="Application">
        <watch expression="((LinkedHashMap.Entry)((LinkedHashMap)((ObjectNode)jsonNode)._children).entrySet().toArray()[1]).getValue()" custom="java.util.LinkedHashMap,java.util.LinkedHashMap.Entry,com.fasterxml.jackson.databind.node.ObjectNode" />
      </configuration>
    </watches-manager>
  </component>
</project>