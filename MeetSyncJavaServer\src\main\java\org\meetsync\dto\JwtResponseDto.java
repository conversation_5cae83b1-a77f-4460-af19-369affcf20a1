package org.meetsync.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "JWT登录响应")
public class JwtResponseDto {

    @Schema(description = "JWT令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    @Schema(description = "令牌类型", example = "Bearer")
    private String type = "Bearer";

    @Schema(description = "用户信息")
    private UserResponseDto user;

    @Schema(description = "响应消息", example = "Login successful")
    private String message;

    // 自定义构造函数
    public JwtResponseDto(String token, UserResponseDto user) {
        this.token = token;
        this.user = user;
        this.type = "Bearer";
    }

    public JwtResponseDto(String token, UserResponseDto user, String message) {
        this.token = token;
        this.user = user;
        this.message = message;
        this.type = "Bearer";
    }
}
