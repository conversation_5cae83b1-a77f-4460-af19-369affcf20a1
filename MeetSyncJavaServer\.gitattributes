# Auto detect text files and perform LF normalization
* text=auto

# Java files
*.java text eol=lf
*.class binary
*.jar binary
*.war binary
*.ear binary

# XML files
*.xml text eol=lf
*.xsd text eol=lf
*.xsl text eol=lf

# Properties files
*.properties text eol=lf

# YAML files
*.yml text eol=lf
*.yaml text eol=lf

# JSON files
*.json text eol=lf

# SQL files
*.sql text eol=lf

# Shell scripts
*.sh text eol=lf
*.bat text eol=crlf

# Markdown files
*.md text eol=lf

# Configuration files
*.conf text eol=lf
*.config text eol=lf
*.ini text eol=lf

# Web files
*.html text eol=lf
*.css text eol=lf
*.js text eol=lf
*.jsx text eol=lf
*.ts text eol=lf
*.tsx text eol=lf

# Documentation
*.txt text eol=lf
*.rtf text eol=lf

# Images
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg text eol=lf

# Archives
*.zip binary
*.tar binary
*.gz binary
*.rar binary
*.7z binary

# Fonts
*.ttf binary
*.otf binary
*.woff binary
*.woff2 binary
*.eot binary

# Videos
*.mp4 binary
*.avi binary
*.mov binary
*.wmv binary

# Audio
*.mp3 binary
*.wav binary
*.ogg binary

# Documents
*.pdf binary
*.doc binary
*.docx binary
*.xls binary
*.xlsx binary
*.ppt binary
*.pptx binary

# Certificates and keys
*.pem binary
*.key binary
*.crt binary
*.p12 binary
*.jks binary

# Database files
*.db binary
*.sqlite binary
*.sqlite3 binary

# Maven wrapper
mvnw text eol=lf
mvnw.cmd text eol=crlf

# Gradle wrapper
gradlew text eol=lf
gradlew.bat text eol=crlf

# IDE files
*.iml text eol=lf
*.ipr text eol=lf
*.iws text eol=lf

# Log files
*.log text eol=lf

# Backup files
*.bak binary
*.backup binary

# Temporary files
*.tmp binary
*.temp binary
