const config = require('./config')
module.exports = class Room {
  constructor(room_id, worker, io) {
    this.id = room_id
    const mediaCodecs = config.mediasoup.router.mediaCodecs
    worker
      .createRouter({
        mediaCodecs
      })
      .then(
        function (router) {
          this.router = router
        }.bind(this)
      )

    this.peers = new Map()
    this.io = io
  }

  addPeer(peer) {
    this.peers.set(peer.id, peer)
  }

  getProducerListForPeer() {
    let producerList = []
    this.peers.forEach((peer) => {
      peer.producers.forEach((producer) => {
        producerList.push({
          producer_id: producer.id
        })
      })
    })
    return producerList
  }

  getRtpCapabilities() {
    return this.router.rtpCapabilities
  }

  async createWebRtcTransport(socket_id) {
    const { maxIncomingBitrate, initialAvailableOutgoingBitrate } = config.mediasoup.webRtcTransport

    const transport = await this.router.createWebRtcTransport({
      listenIps: config.mediasoup.webRtcTransport.listenIps,
      enableUdp: true,
      enableTcp: true,
      preferUdp: true,
      initialAvailableOutgoingBitrate
    })
    if (maxIncomingBitrate) {
      try {
        await transport.setMaxIncomingBitrate(maxIncomingBitrate)
      } catch (error) {}
    }

    transport.on(
      'dtlsstatechange',
      function (dtlsState) {
        if (dtlsState === 'closed') {
          console.log('Transport close', { name: this.peers.get(socket_id).name })
          transport.close()
        }
      }.bind(this)
    )

    transport.on('close', () => {
      console.log('Transport close', { name: this.peers.get(socket_id).name })
    })

    console.log('Adding transport', { transportId: transport.id })
    this.peers.get(socket_id).addTransport(transport)
    return {
      params: {
        id: transport.id,
        iceParameters: transport.iceParameters,
        iceCandidates: transport.iceCandidates,
        dtlsParameters: transport.dtlsParameters
      }
    }
  }

  async connectPeerTransport(socket_id, transport_id, dtlsParameters) {
    if (!this.peers.has(socket_id)) return

    await this.peers.get(socket_id).connectTransport(transport_id, dtlsParameters)
  }

  async produce(socket_id, producerTransportId, rtpParameters, kind) {
    // handle undefined errors
    return new Promise(
      async function (resolve, reject) {
        // 传递 mediaType 信息到 appData
        let producer = await this.peers.get(socket_id).createProducer(
          producerTransportId,
          rtpParameters,
          kind,
          { mediaType: kind }
        )
        resolve(producer.id)

        const producerData = [
          {
            producer_id: producer.id,
            producer_socket_id: socket_id
          }
        ]

        // 广播给其他用户
        this.broadCast(socket_id, 'newProducers', producerData)

        // 也发送给推流者本身，这样推流设备可以消费自己的流
        this.send(socket_id, 'newProducers', producerData)
      }.bind(this)
    )
  }

  async consume(socket_id, consumer_transport_id, producer_id, rtpCapabilities) {
    try {
      // handle nulls
      if (
        !this.router.canConsume({
          producerId: producer_id,
          rtpCapabilities
        })
      ) {
        console.error('can not consume')
        return null
      }

      const peer = this.peers.get(socket_id)
      if (!peer) {
        console.error('Peer not found:', socket_id)
        return null
      }

      const result = await peer.createConsumer(consumer_transport_id, producer_id, rtpCapabilities)

      if (!result) {
        console.error('Failed to create consumer')
        return null
      }

      let { consumer, params } = result

      consumer.on(
        'producerclose',
        function () {
          console.log('Consumer closed due to producerclose event', {
            name: `${this.peers.get(socket_id).name}`,
            consumer_id: `${consumer.id}`
          })
          this.peers.get(socket_id).removeConsumer(consumer.id)
          // tell client consumer is dead
          this.io.to(socket_id).emit('consumerClosed', {
            consumer_id: consumer.id
          })
        }.bind(this)
      )

      return params
    } catch (error) {
      console.error('Consume error:', error)
      return null
    }
  }

  async resume(socket_id, consumerId) {
    try {
      console.log('Resume request:', { socket_id, consumerId })

      const peer = this.peers.get(socket_id)
      if (!peer) {
        console.error('Peer not found for socket_id:', socket_id)
        throw new Error('Peer not found')
      }

      const consumer = peer.getConsumer(consumerId)
      if (!consumer) {
        console.error('Consumer not found for consumerId:', consumerId)
        throw new Error('Consumer not found')
      }

      // 额外检查 consumer 对象是否有 resume 方法
      if (typeof consumer.resume !== 'function') {
        console.error('Consumer does not have resume method:', consumer)
        throw new Error('Invalid consumer object')
      }

      await consumer.resume()
      console.log('Consumer resumed successfully:', consumerId)
    } catch (error) {
      console.error('Resume error:', error)
      throw error
    }
  }

  async removePeer(socket_id) {
    const peer = this.peers.get(socket_id)
    if (!peer) {
      console.warn('尝试移除不存在的用户:', socket_id)
      return
    }

    // 获取用户的所有生产者信息，在关闭前通知其他用户
    const producersToClose = []
    peer.producers.forEach((producer, producer_id) => {
      const mediaType = producer.appData?.mediaType || 'unknown'
      producersToClose.push({
        producer_id,
        mediaType,
        socket_id,
        username: peer.name || '未知用户'
      })
    })

    // 通知房间内其他用户这些生产者即将关闭
    producersToClose.forEach(producerInfo => {
      console.log(`广播生产者下线: ${producerInfo.username} 的 ${producerInfo.mediaType}`)
      this.broadCast(socket_id, 'producerClosed', {
        producer_id: producerInfo.producer_id,
        socket_id: producerInfo.socket_id,
        username: producerInfo.username,
        mediaType: producerInfo.mediaType,
        reason: 'user_disconnected'
      })
    })

    // 关闭用户连接
    peer.close()
    this.peers.delete(socket_id)
  }

  closeProducer(socket_id, producer_id) {
    this.peers.get(socket_id).closeProducer(producer_id)
  }

  broadCast(socket_id, name, data) {
    for (let otherID of Array.from(this.peers.keys()).filter((id) => id !== socket_id)) {
      this.send(otherID, name, data)
    }
  }

  send(socket_id, name, data) {
    this.io.to(socket_id).emit(name, data)
  }

  getPeers() {
    return this.peers
  }

  // 输出房间的生产者和消费者调试信息
  logRoomStatus() {
    console.log(`\n=== 房间 ${this.id} 状态信息 ===`)
    console.log(`参与者数量: ${this.peers.size}`)

    let totalProducers = 0
    let totalConsumers = 0

    this.peers.forEach((peer, socket_id) => {
      const producerCount = peer.producers.size
      const consumerCount = peer.consumers.size
      totalProducers += producerCount
      totalConsumers += consumerCount

      console.log(`用户 ${peer.name} (${socket_id}):`)
      console.log(`  - 生产者数量: ${producerCount}`)

      if (producerCount > 0) {
        peer.producers.forEach((producer, producer_id) => {
          const mediaType = producer.appData?.mediaType || 'unknown'
          console.log(`    * ${producer_id}: ${mediaType} (${producer.kind})`)
        })
      }

      console.log(`  - 消费者数量: ${consumerCount}`)
      if (consumerCount > 0) {
        peer.consumers.forEach((consumer, consumer_id) => {
          console.log(`    * ${consumer_id}: ${consumer.kind}`)
        })
      }
    })

    console.log(`房间总计: ${totalProducers} 个生产者, ${totalConsumers} 个消费者`)
    console.log(`=== 房间状态信息结束 ===\n`)
  }

  toJson() {
    return {
      id: this.id,
      peers: JSON.stringify([...this.peers])
    }
  }
}
