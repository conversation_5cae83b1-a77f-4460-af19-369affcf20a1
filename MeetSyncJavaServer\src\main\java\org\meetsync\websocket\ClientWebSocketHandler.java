package org.meetsync.websocket;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.meetsync.dto.UserResponseDto;
import org.meetsync.service.MediaProxyService;
import org.meetsync.service.RoomService;
import org.meetsync.service.UserService;
import org.meetsync.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.scheduling.annotation.Scheduled;

import java.io.IOException;
import java.net.URI;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 处理前端客户端的WebSocket连接
 * 用于实时通信：用户加入/离开、生产者状态变化等
 */
@Component
public class ClientWebSocketHandler implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(ClientWebSocketHandler.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private UserService userService;

    @Autowired
    private MediaProxyService mediaProxyService;

    @Autowired
    private RoomService roomService;

    // 存储活跃的WebSocket连接
    // Key: sessionId, Value: WebSocketSession
    private final Map<String, WebSocketSession> activeSessions = new ConcurrentHashMap<>();
    
    // 存储用户会话映射
    // Key: userId, Value: sessionId
    private final Map<Long, String> userSessions = new ConcurrentHashMap<>();
    
    // 存储房间会话映射
    // Key: roomId, Value: Set of sessionIds
    private final Map<String, ConcurrentHashMap<String, WebSocketSession>> roomSessions = new ConcurrentHashMap<>();

    // 存储会话心跳时间
    // Key: sessionId, Value: lastHeartbeatTime
    private final Map<String, Long> sessionHeartbeats = new ConcurrentHashMap<>();

    // 心跳检查间隔（毫秒）
    private static final long HEARTBEAT_INTERVAL = 15000; // 15秒检查一次
    private static final long HEARTBEAT_TIMEOUT = 30000; // 30秒超时（客户端10秒发送一次心跳）

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        logger.info("WebSocket连接建立: {}", session.getId());

        try {
            // 解析查询参数
            URI uri = session.getUri();
            if (uri == null) {
                logger.error("WebSocket URI为空");
                session.close(CloseStatus.BAD_DATA.withReason("Missing URI"));
                return;
            }

            Map<String, String> queryParams = UriComponentsBuilder.fromUri(uri)
                    .build()
                    .getQueryParams()
                    .toSingleValueMap();

            String token = queryParams.get("token");
            String roomId = queryParams.get("roomId");

            if (token == null || roomId == null) {
                logger.error("WebSocket连接缺少必要参数: token={}, roomId={}", token, roomId);
                session.close(CloseStatus.BAD_DATA.withReason("Missing token or roomId"));
                return;
            }

            // 验证JWT令牌
            String username = jwtUtil.getUsernameFromToken(token);
            if (username == null || !jwtUtil.validateToken(token, username)) {
                logger.error("WebSocket连接令牌无效: {}", token);
                session.close(CloseStatus.NOT_ACCEPTABLE.withReason("Invalid token"));
                return;
            }

            // 获取用户信息
            Optional<UserResponseDto> userU = userService.findByUsername(username);

            if (userU.isEmpty()){
                return;
            }

            UserResponseDto user = userU.get();
            // 验证用户是否有权限访问房间
            if (!roomService.canUserAccessRoom(user.getId(), roomId, null)) {
                logger.error("WebSocket连接用户无权限访问房间: userId={}, roomId={}", user.getId(), roomId);
                session.close(CloseStatus.NOT_ACCEPTABLE.withReason("No permission to access room"));
                return;
            }

            // 存储会话信息
            session.getAttributes().put("userId", user.getId());
            session.getAttributes().put("username", username);
            session.getAttributes().put("roomId", roomId);

            // 注册会话
            activeSessions.put(session.getId(), session);
            userSessions.put(user.getId(), session.getId());

            // 添加到房间会话映射
            roomSessions.computeIfAbsent(roomId, k -> new ConcurrentHashMap<>())
                    .put(session.getId(), session);

            // 初始化心跳时间
            sessionHeartbeats.put(session.getId(), System.currentTimeMillis());

            logger.info("用户 {} 加入房间 {} 的WebSocket连接 [会话ID: {}]", username, roomId, session.getId());

            // 打印连接统计信息
            logConnectionStats();

            // 通知房间内其他用户有新用户加入
            broadcastToRoom(roomId, createMessage("userJoined", Map.of(
                    "userId", user.getId(),
                    "username", username,
                    "participantCount", getRoomParticipantCount(roomId)
            )), session.getId());

        } catch (Exception e) {
            logger.error("WebSocket连接建立失败", e);
            session.close(CloseStatus.SERVER_ERROR.withReason("Connection setup failed"));
        }
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        if (message instanceof TextMessage) {
            String payload = ((TextMessage) message).getPayload();
            logger.debug("收到WebSocket消息: {}", payload);

            // 更新心跳时间
            sessionHeartbeats.put(session.getId(), System.currentTimeMillis());

            // 处理简单的ping消息
            if ("ping".equals(payload)) {
                session.sendMessage(new TextMessage("pong"));
                return;
            }

            try {
                JsonNode messageNode = objectMapper.readTree(payload);
                String type = messageNode.get("type").asText();
                JsonNode data = messageNode.get("data");

                handleClientMessage(session, type, data);

            } catch (Exception e) {
                logger.error("处理WebSocket消息失败", e);
                sendErrorMessage(session, "Message processing failed: " + e.getMessage());
            }
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        logger.error("WebSocket传输错误: sessionId={}", session.getId(), exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        Long userId = (Long) session.getAttributes().get("userId");
        String username = (String) session.getAttributes().get("username");
        String roomId = (String) session.getAttributes().get("roomId");

        logger.info("WebSocket连接关闭: sessionId={}, userId={}, username={}, roomId={}, status={}",
                sessionId, userId, username, roomId, closeStatus);

        // 打印连接统计信息
        logConnectionStats();

        // 清理会话信息
        activeSessions.remove(sessionId);
        sessionHeartbeats.remove(sessionId);
        if (userId != null) {
            userSessions.remove(userId);
        }
        
        if (roomId != null) {
            Map<String, WebSocketSession> roomSessionMap = roomSessions.get(roomId);
            if (roomSessionMap != null) {
                roomSessionMap.remove(sessionId);
                if (roomSessionMap.isEmpty()) {
                    roomSessions.remove(roomId);
                }
            }

            // 清理用户的媒体生产者
            if (userId != null && roomId != null) {
                try {
                    logger.info("清理用户媒体生产者: userId={}, roomId={}", userId, roomId);

                    // 检查并清理僵尸生产者
                    mediaProxyService.checkAndCleanupZombieProducers(roomId, userId);

                    // 执行正常清理
                    mediaProxyService.cleanupUserProducers(roomId, userId);
                } catch (Exception e) {
                    logger.error("清理用户媒体生产者失败: userId={}, roomId={}", userId, roomId, e);
                }
            }

            // 通知房间内其他用户有用户离开
            if (username != null) {
                broadcastToRoom(roomId, createMessage("userLeft", Map.of(
                        "userId", userId != null ? userId : 0,
                        "username", username,
                        "participantCount", getRoomParticipantCount(roomId)
                )), sessionId);
            }
        }
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 处理客户端消息
     */
    private void handleClientMessage(WebSocketSession session, String type, JsonNode data) {
        Long userId = (Long) session.getAttributes().get("userId");
        String roomId = (String) session.getAttributes().get("roomId");

        logger.debug("处理客户端消息: type={}, userId={}, roomId={}", type, userId, roomId);

        switch (type) {
            case "ping":
                // 更新心跳时间
                sessionHeartbeats.put(session.getId(), System.currentTimeMillis());
                sendMessage(session, createMessage("pong", Map.of("timestamp", System.currentTimeMillis())));
                break;
            case "heartbeat":
                // 更新心跳时间
                sessionHeartbeats.put(session.getId(), System.currentTimeMillis());
                sendMessage(session, createMessage("heartbeat_ack", Map.of("timestamp", System.currentTimeMillis())));
                break;
            case "getRoomInfo":
                handleGetRoomInfo(session, roomId);
                break;
            default:
                logger.warn("未知的客户端消息类型: {}", type);
                sendErrorMessage(session, "Unknown message type: " + type);
        }
    }

    /**
     * 处理获取房间信息请求
     */
    private void handleGetRoomInfo(WebSocketSession session, String roomId) {
        try {
            int participantCount = getRoomParticipantCount(roomId);
            sendMessage(session, createMessage("roomInfo", Map.of(
                    "roomId", roomId,
                    "participantCount", participantCount
            )));
        } catch (Exception e) {
            logger.error("获取房间信息失败", e);
            sendErrorMessage(session, "Failed to get room info");
        }
    }

    /**
     * 向房间内所有用户广播消息（除了排除的会话）
     */
    public void broadcastToRoom(String roomId, String message, String excludeSessionId) {
        Map<String, WebSocketSession> roomSessionMap = roomSessions.get(roomId);
        if (roomSessionMap == null) {
            return;
        }

        logger.debug("向房间 {} 广播消息: {}", roomId, message);

        // 使用串行流避免并发写入WebSocket
        roomSessionMap.values().stream()
                .filter(session -> !session.getId().equals(excludeSessionId))
                .filter(WebSocketSession::isOpen)
                .forEach(session -> {
                    synchronized (session) {
                        try {
                            session.sendMessage(new TextMessage(message));
                        } catch (IOException e) {
                            logger.error("发送广播消息失败: sessionId={}", session.getId(), e);
                        }
                    }
                });
    }

    /**
     * 向房间内所有用户广播消息
     */
    public void broadcastToRoom(String roomId, String message) {
        broadcastToRoom(roomId, message, null);
    }

    /**
     * 向特定用户发送消息
     */
    public void sendToUser(Long userId, String message) {
        String sessionId = userSessions.get(userId);
        if (sessionId != null) {
            WebSocketSession session = activeSessions.get(sessionId);
            if (session != null && session.isOpen()) {
                sendMessage(session, message);
            }
        }
    }

    /**
     * 发送消息到WebSocket会话
     */
    private void sendMessage(WebSocketSession session, String message) {
        synchronized (session) {
            try {
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage(message));
                }
            } catch (IOException e) {
                logger.error("发送WebSocket消息失败: sessionId={}", session.getId(), e);
            }
        }
    }

    /**
     * 发送错误消息
     */
    private void sendErrorMessage(WebSocketSession session, String error) {
        sendMessage(session, createMessage("error", Map.of("message", error)));
    }

    /**
     * 创建消息JSON字符串
     */
    public String createMessage(String type, Object data) {
        try {
            Map<String, Object> message = Map.of(
                    "type", type,
                    "data", data,
                    "timestamp", System.currentTimeMillis()
            );
            return objectMapper.writeValueAsString(message);
        } catch (Exception e) {
            logger.error("创建消息失败", e);
            return "{\"type\":\"error\",\"data\":{\"message\":\"Message creation failed\"}}";
        }
    }

    /**
     * 获取房间参与者数量
     */
    private int getRoomParticipantCount(String roomId) {
        Map<String, WebSocketSession> roomSessionMap = roomSessions.get(roomId);
        return roomSessionMap != null ? roomSessionMap.size() : 0;
    }

    /**
     * 通知生产者状态变化
     */
    public void notifyProducerClosed(String roomId, Long userId, String producerId, String mediaType, String reason) {
        String message = createMessage("producerClosed", Map.of(
                "producer_id", producerId,
                "socket_id", userSessions.get(userId),
                "username", getUsernameById(userId),
                "mediaType", mediaType,
                "reason", reason
        ));
        broadcastToRoom(roomId, message);
    }

    /**
     * 通知新生产者
     */
    public void notifyNewProducers(String roomId, Object producers) {
        String message = createMessage("newProducers", Map.of("producers", producers));
        broadcastToRoom(roomId, message);
    }

    /**
     * 根据用户ID获取用户名
     */
    private String getUsernameById(Long userId) {
        try {
            Optional<UserResponseDto> userP = userService.findById(userId);
            if (userP.isEmpty()){
                throw new Exception("ee");
            }
            UserResponseDto user = userP.get();
            return user.getUsername();
        } catch (Exception e) {
            logger.error("获取用户名失败: userId={}", userId, e);
            return "Unknown";
        }
    }

    /**
     * 定时检查心跳超时的连接
     */
    @Scheduled(fixedRate = HEARTBEAT_INTERVAL)
    public void checkHeartbeatTimeouts() {
        long currentTime = System.currentTimeMillis();
        List<String> timeoutSessions = new ArrayList<>();

        // 检查超时的会话
        sessionHeartbeats.forEach((sessionId, lastHeartbeat) -> {
            if (currentTime - lastHeartbeat > HEARTBEAT_TIMEOUT) {
                timeoutSessions.add(sessionId);
            }
        });

        // 关闭超时的连接
        for (String sessionId : timeoutSessions) {
            WebSocketSession session = activeSessions.get(sessionId);
            if (session != null && session.isOpen()) {
                try {
                    logger.warn("关闭心跳超时的WebSocket连接: sessionId={}", sessionId);
                    session.close(CloseStatus.GOING_AWAY.withReason("Heartbeat timeout"));
                } catch (Exception e) {
                    logger.error("关闭超时连接失败: sessionId={}", sessionId, e);
                }
            }
        }

        if (!timeoutSessions.isEmpty()) {
            logger.info("检查心跳超时完成，关闭了 {} 个超时连接", timeoutSessions.size());
        }
    }

    /**
     * 获取连接状态统计
     */
    public Map<String, Object> getConnectionStats() {
        return Map.of(
            "totalConnections", activeSessions.size(),
            "totalRooms", roomSessions.size(),
            "heartbeatSessions", sessionHeartbeats.size()
        );
    }

    /**
     * 打印连接统计信息
     */
    private void logConnectionStats() {
        Map<String, Integer> roomStats = new HashMap<>();
        Map<String, Set<String>> userStats = new HashMap<>();

        // 统计每个房间的连接数
        roomSessions.forEach((roomId, sessions) -> {
            roomStats.put(roomId, sessions.size());
        });

        // 统计每个用户的连接数
        activeSessions.forEach((sessionId, session) -> {
            String username = (String) session.getAttributes().get("username");
            String roomId = (String) session.getAttributes().get("roomId");
            if (username != null && roomId != null) {
                userStats.computeIfAbsent(username, k -> new HashSet<>()).add(sessionId);
            }
        });

        logger.info("=== WebSocket连接统计 ===");
        logger.info("总连接数: {}", activeSessions.size());
        logger.info("房间连接分布: {}", roomStats);

        // 检查重复连接
        userStats.forEach((username, sessionIds) -> {
            if (sessionIds.size() > 1) {
                logger.warn("⚠️ 用户 {} 有 {} 个重复连接: {}", username, sessionIds.size(), sessionIds);
            }
        });

        logger.info("========================");
    }
}
