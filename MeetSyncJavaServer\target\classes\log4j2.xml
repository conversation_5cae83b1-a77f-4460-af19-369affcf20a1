<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">
    <Properties>
        <!-- 日志文件路径 -->
        <Property name="LOG_HOME">logs</Property>
        <!-- 应用名称 -->
        <Property name="APP_NAME">meetsync-java-server</Property>
        <!-- 日志格式 -->
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</Property>
        <!-- 控制台日志格式（带颜色） -->
        <Property name="CONSOLE_LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} %highlight{[%thread] %-5level} %style{%logger{36}}{cyan} - %msg%n</Property>
    </Properties>

    <Appenders>
        <!-- 控制台输出 -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${CONSOLE_LOG_PATTERN}"/>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
        </Console>

        <!-- 所有日志文件 -->
        <RollingFile name="AllFile" fileName="${LOG_HOME}/${APP_NAME}.log"
                     filePattern="${LOG_HOME}/${APP_NAME}-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>

        <!-- 错误日志文件 -->
        <RollingFile name="ErrorFile" fileName="${LOG_HOME}/${APP_NAME}-error.log"
                     filePattern="${LOG_HOME}/${APP_NAME}-error-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>

        <!-- 应用专用日志文件 -->
        <RollingFile name="AppFile" fileName="${LOG_HOME}/${APP_NAME}-app.log"
                     filePattern="${LOG_HOME}/${APP_NAME}-app-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>

        <!-- SQL日志文件 -->
        <RollingFile name="SqlFile" fileName="${LOG_HOME}/${APP_NAME}-sql.log"
                     filePattern="${LOG_HOME}/${APP_NAME}-sql-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="15"/>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>
    </Appenders>

    <Loggers>
        <!-- 应用日志 -->
        <Logger name="org.meetsync" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AllFile"/>
            <AppenderRef ref="AppFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>

        <!-- SQL日志 -->
        <Logger name="org.hibernate.SQL" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="SqlFile"/>
        </Logger>
        
        <Logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="SqlFile"/>
        </Logger>

        <!-- Spring框架日志 -->
        <Logger name="org.springframework" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AllFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>

        <Logger name="org.springframework.security" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AllFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>

        <Logger name="org.springframework.web" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AllFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>

        <!-- 数据库连接池日志 -->
        <Logger name="com.zaxxer.hikari" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AllFile"/>
        </Logger>

        <!-- 根日志配置 -->
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AllFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Root>
    </Loggers>
</Configuration>
