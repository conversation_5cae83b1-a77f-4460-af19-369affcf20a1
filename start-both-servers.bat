@echo off
echo ========================================
echo MeetSync 双服务启动脚本
echo ========================================
echo.

echo 新架构说明:
echo - Java服务端: 用户管理、房间管理、API网关
echo - Node.js服务端: WebRTC媒体流处理
echo - 两服务通过WebSocket通信
echo.

echo 检查环境...

REM 检查Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Java未安装或未添加到PATH
    pause
    exit /b 1
)

REM 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Node.js未安装或未添加到PATH
    pause
    exit /b 1
)

echo [成功] 环境检查通过
echo.

echo ========================================
echo 启动Node.js媒体服务器
echo ========================================
echo.

cd MeetSyncServer

echo 安装Node.js依赖...
call npm install
if %errorlevel% neq 0 (
    echo [错误] Node.js依赖安装失败
    pause
    exit /b 1
)

echo 启动媒体服务器...
start "MeetSync Media Server" cmd /k "npm start"

echo 等待媒体服务器启动 (10秒)...
timeout /t 10 /nobreak >nul

cd ..

echo ========================================
echo 启动Java服务器
echo ========================================
echo.

cd MeetSyncJavaServer

echo 编译Java项目...
call .\mvnw.cmd clean compile
if %errorlevel% neq 0 (
    echo [错误] Java项目编译失败
    pause
    exit /b 1
)

echo 启动Java服务器...
start "MeetSync Java Server" cmd /k ".\mvnw.cmd spring-boot:run"

echo 等待Java服务器启动 (30秒)...
timeout /t 30 /nobreak >nul

cd ..

echo ========================================
echo 服务启动完成！
echo ========================================
echo.

echo 服务地址:
echo - Java API服务: http://localhost:8080/api
echo - Swagger文档: http://localhost:8080/api/swagger-ui.html
echo - 媒体服务器健康检查: http://localhost:3018/health
echo.

echo 测试连接...
echo.

echo 测试Java服务器...
curl -s http://localhost:8080/api/health >nul 2>&1
if %errorlevel% equ 0 (
    echo [成功] Java服务器运行正常
) else (
    echo [警告] Java服务器可能还在启动中
)

echo 测试媒体服务器...
curl -s http://localhost:3018/health >nul 2>&1
if %errorlevel% equ 0 (
    echo [成功] 媒体服务器运行正常
) else (
    echo [警告] 媒体服务器可能还在启动中
)

echo 测试媒体服务器连接状态...
curl -s http://localhost:8080/api/media/status >nul 2>&1
if %errorlevel% equ 0 (
    echo [成功] 媒体服务器连接正常
) else (
    echo [警告] 媒体服务器连接可能还在建立中
)

echo.
echo ========================================
echo 开发提示
echo ========================================
echo.
echo 1. 前端应该连接到Java服务端: http://localhost:8080/api
echo 2. 所有API请求都通过Java服务端
echo 3. WebRTC媒体请求会自动代理到Node.js服务端
echo 4. 查看日志: 两个命令行窗口分别显示服务日志
echo.
echo 常用API:
echo - 用户注册: POST /api/auth/register
echo - 用户登录: POST /api/auth/login
echo - 创建房间: POST /api/rooms
echo - 获取RTP能力: GET /api/media/rooms/{roomId}/rtp-capabilities
echo - 创建传输: POST /api/media/rooms/{roomId}/transports
echo.

echo 是否打开Swagger文档? (y/n)
set /p open_swagger=
if /i "%open_swagger%"=="y" (
    start http://localhost:8080/api/swagger-ui.html
)

echo.
echo 按任意键退出...
pause
