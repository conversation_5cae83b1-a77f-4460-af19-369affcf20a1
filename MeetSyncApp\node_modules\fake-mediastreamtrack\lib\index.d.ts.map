{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAGzE,MAAM,MAAM,OAAO,GAAG;IACrB,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC;CACvB,CAAC;AAEF,MAAM,MAAM,2BAA2B,CACtC,2BAA2B,SAAS,OAAO,GAAG,OAAO,IAClD;IACH,IAAI,EAAE,MAAM,CAAC;IACb,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,UAAU,CAAC,EAAE,qBAAqB,CAAC;IACnC,YAAY,CAAC,EAAE,sBAAsB,CAAC;IACtC,WAAW,CAAC,EAAE,qBAAqB,CAAC;IACpC,QAAQ,CAAC,EAAE,kBAAkB,CAAC;IAC9B,IAAI,CAAC,EAAE,2BAA2B,CAAC;CACnC,CAAC;AAEF,MAAM,WAAW,4BAA6B,SAAQ,wBAAwB;IAC7E,OAAO,EAAE,KAAK,CAAC;IACf,aAAa,EAAE,KAAK,CAAC;CACrB;AAED,qBAAa,oBAAoB,CAC/B,2BAA2B,SAAS,OAAO,GAAG,OAAO,CAEtD,SAAQ,eACR,YAAW,gBAAgB;;gBAsBf,EACX,IAAI,EACJ,EAAE,EACF,KAAK,EACL,WAAW,EACX,OAAO,EACP,KAAK,EACL,UAAU,EACV,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,IAAI,GACJ,EAAE,2BAA2B,CAAC,2BAA2B,CAAC;IAgB3D,IAAI,EAAE,IAAI,MAAM,CAEf;IAED,IAAI,IAAI,IAAI,MAAM,CAEjB;IAED,IAAI,KAAK,IAAI,MAAM,CAElB;IAED,IAAI,WAAW,IAAI,MAAM,CAExB;IAED,IAAI,WAAW,CAAC,WAAW,EAAE,MAAM,EAElC;IAED,IAAI,OAAO,IAAI,OAAO,CAErB;IAED;;OAEG;IACH,IAAI,OAAO,CAAC,OAAO,EAAE,OAAO,EAQ3B;IAED,IAAI,KAAK,IAAI,OAAO,CAEnB;IAED,IAAI,UAAU,IAAI,qBAAqB,CAEtC;IAED;;OAEG;IACH,IAAI,IAAI,IAAI,2BAA2B,CAEtC;IAED;;OAEG;IACH,IAAI,IAAI,CAAC,IAAI,EAAE,2BAA2B,EAEzC;IAED,IAAI,MAAM,IAAI,CAAC,CAAC,IAAI,EAAE,gBAAgB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,CAEhE;IAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,EAAE,oBAAoB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,EAU1E;IAED,IAAI,QAAQ,IAAI,CAAC,CAAC,IAAI,EAAE,gBAAgB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,CAIlE;IAED,IAAI,QAAQ,CACX,OAAO,EAAE,CAAC,CAAC,IAAI,EAAE,oBAAoB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,EAWhE;IAED,IAAI,OAAO,IAAI,CAAC,CAAC,IAAI,EAAE,gBAAgB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,CAEjE;IAED,IAAI,OAAO,CACV,OAAO,EAAE,CAAC,CAAC,IAAI,EAAE,oBAAoB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,EAWhE;IAED,IAAI,eAAe,IAAI,CAAC,CAAC,IAAI,EAAE,gBAAgB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,CAIzE;IAED,IAAI,eAAe,CAClB,OAAO,EAAE,CAAC,CAAC,IAAI,EAAE,oBAAoB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,EAWhE;IAED,IAAI,SAAS,IAAI,CAAC,CAAC,IAAI,EAAE,gBAAgB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,CAInE;IAED,IAAI,SAAS,CACZ,OAAO,EAAE,CAAC,CAAC,IAAI,EAAE,oBAAoB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,EAWhE;IAEQ,gBAAgB,CAAC,CAAC,SAAS,MAAM,4BAA4B,EACrE,IAAI,EAAE,CAAC,EACP,QAAQ,EAAE,CACT,IAAI,EAAE,oBAAoB,EAC1B,EAAE,EAAE,4BAA4B,CAAC,CAAC,CAAC,KAC/B,GAAG,EACR,OAAO,CAAC,EAAE,mBAAmB,GAC3B,IAAI;IAIE,mBAAmB,CAAC,CAAC,SAAS,MAAM,4BAA4B,EACxE,IAAI,EAAE,CAAC,EACP,QAAQ,EAAE,CACT,IAAI,EAAE,oBAAoB,EAC1B,EAAE,EAAE,4BAA4B,CAAC,CAAC,CAAC,KAC/B,GAAG,GACN,IAAI;IAIP;;;OAGG;IACH,IAAI,IAAI,IAAI;IAUZ;;;OAGG;IACH,KAAK,CACJ,iCAAiC,SAChC,OAAO,GAAG,2BAA2B,EACrC,EACD,EAAE,EACF,IAAI,GACJ,GAAE;QACF,EAAE,CAAC,EAAE,MAAM,CAAC;QACZ,IAAI,CAAC,EAAE,iCAAiC,CAAC;KACpC,GAAG,oBAAoB;IAgB7B,eAAe,IAAI,sBAAsB;IAIzC,cAAc,IAAI,qBAAqB;IAIjC,gBAAgB,CACrB,WAAW,GAAE,qBAA0B,GACrC,OAAO,CAAC,IAAI,CAAC;IAOhB,WAAW,IAAI,kBAAkB;IAIjC;;;OAGG;IACH,UAAU,IAAI,IAAI;IAWlB;;;OAGG;IACH,UAAU,IAAI,IAAI;IAUlB;;;OAGG;IACH,YAAY,IAAI,IAAI;CASpB"}