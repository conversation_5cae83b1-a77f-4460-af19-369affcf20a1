{"version": 3, "file": "Producer.d.ts", "sourceRoot": "", "sources": ["../src/Producer.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AAExD,OAAO,KAAK,EACX,SAAS,EACT,kBAAkB,EAClB,aAAa,EACb,qBAAqB,EACrB,MAAM,iBAAiB,CAAC;AACzB,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,SAAS,CAAC;AAIvC,MAAM,MAAM,eAAe,CAAC,eAAe,SAAS,OAAO,GAAG,OAAO,IAAI;IACxE,KAAK,CAAC,EAAE,gBAAgB,CAAC;IACzB,SAAS,CAAC,EAAE,qBAAqB,EAAE,CAAC;IACpC,YAAY,CAAC,EAAE,oBAAoB,CAAC;IACpC,KAAK,CAAC,EAAE,kBAAkB,CAAC;IAC3B,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,WAAW,CAAC,EAAE,mBAAmB,CAAC;IAClC,OAAO,CAAC,EAAE,eAAe,CAAC;CAC1B,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,mBAAmB,GAAG,CAAC,SAAS,EAAE,YAAY,KAAK,IAAI,CAAC;AAGpE,MAAM,MAAM,oBAAoB,GAAG;IAClC,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,uBAAuB,CAAC,EAAE,MAAM,CAAC;IACjC,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,qBAAqB,CAAC,EAAE,MAAM,CAAC;CAC/B,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG;IAC5B,cAAc,EAAE,EAAE,CAAC;IACnB,UAAU,EAAE,EAAE,CAAC;IAEf,QAAQ,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC,CAAC;IAC/C,SAAS,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC,CAAC;IAChD,eAAe,EAAE;QAChB,gBAAgB,GAAG,IAAI;QACvB,MAAM,IAAI;QACV,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI;KACtB,CAAC;IACF,qBAAqB,EAAE,CAAC,MAAM,EAAE,MAAM,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC,CAAC;IACpE,2BAA2B,EAAE;QAC5B,wBAAwB;QACxB,MAAM,IAAI;QACV,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI;KACtB,CAAC;IACF,WAAW,EAAE,CAAC,CAAC,KAAK,EAAE,cAAc,KAAK,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC,CAAC;IACvE,QAAQ,EAAE,EAAE,CAAC;CACb,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,sBAAsB,CAAC,CAAC;AAE5E,MAAM,MAAM,sBAAsB,GAAG;IACpC,KAAK,EAAE,EAAE,CAAC;IACV,KAAK,EAAE,EAAE,CAAC;IACV,MAAM,EAAE,EAAE,CAAC;IACX,UAAU,EAAE,EAAE,CAAC;CACf,CAAC;AAEF,qBAAa,QAAQ,CACpB,eAAe,SAAS,OAAO,GAAG,OAAO,CACxC,SAAQ,oBAAoB,CAAC,cAAc,CAAC;IAE7C,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAS;IAE7B,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAS;IAElC,OAAO,CAAC,OAAO,CAAS;IAExB,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAe;IAE3C,OAAO,CAAC,MAAM,CAA0B;IAExC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAY;IAElC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAgB;IAE/C,OAAO,CAAC,OAAO,CAAU;IAEzB,OAAO,CAAC,gBAAgB,CAAqB;IAE7C,OAAO,CAAC,WAAW,CAAU;IAE7B,OAAO,CAAC,oBAAoB,CAAU;IAEtC,OAAO,CAAC,eAAe,CAAU;IAEjC,OAAO,CAAC,QAAQ,CAAkB;IAElC,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,gBAAgB,CACM;gBAExC,EACX,EAAE,EACF,OAAO,EACP,SAAS,EACT,KAAK,EACL,aAAa,EACb,UAAU,EACV,mBAAmB,EACnB,cAAc,EACd,OAAO,GACP,EAAE;QACF,EAAE,EAAE,MAAM,CAAC;QACX,OAAO,EAAE,MAAM,CAAC;QAChB,SAAS,CAAC,EAAE,YAAY,CAAC;QACzB,KAAK,EAAE,gBAAgB,CAAC;QACxB,aAAa,EAAE,aAAa,CAAC;QAC7B,UAAU,EAAE,OAAO,CAAC;QACpB,mBAAmB,EAAE,OAAO,CAAC;QAC7B,cAAc,EAAE,OAAO,CAAC;QACxB,OAAO,CAAC,EAAE,eAAe,CAAC;KAC1B;IAyBD;;OAEG;IACH,IAAI,EAAE,IAAI,MAAM,CAEf;IAED;;OAEG;IACH,IAAI,OAAO,IAAI,MAAM,CAEpB;IAED;;OAEG;IACH,IAAI,MAAM,IAAI,OAAO,CAEpB;IAED;;OAEG;IACH,IAAI,IAAI,IAAI,SAAS,CAEpB;IAED;;OAEG;IACH,IAAI,SAAS,IAAI,YAAY,GAAG,SAAS,CAExC;IAED;;OAEG;IACH,IAAI,KAAK,IAAI,gBAAgB,GAAG,IAAI,CAEnC;IAED;;OAEG;IACH,IAAI,aAAa,IAAI,aAAa,CAEjC;IAED;;OAEG;IACH,IAAI,MAAM,IAAI,OAAO,CAEpB;IAED;;;;OAIG;IACH,IAAI,eAAe,IAAI,MAAM,GAAG,SAAS,CAExC;IAED;;OAEG;IACH,IAAI,OAAO,IAAI,eAAe,CAE7B;IAED;;OAEG;IACH,IAAI,OAAO,CAAC,OAAO,EAAE,eAAe,EAEnC;IAED,IAAI,QAAQ,IAAI,gBAAgB,CAE/B;IAED;;OAEG;IACH,KAAK,IAAI,IAAI;IAiBb;;OAEG;IACH,eAAe,IAAI,IAAI;IAiBvB;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,cAAc,CAAC;IAUzC;;OAEG;IACH,KAAK,IAAI,IAAI;IAyBb;;OAEG;IACH,MAAM,IAAI,IAAI;IAyBd;;OAEG;IACG,YAAY,CAAC,EAClB,KAAK,GACL,EAAE;QACF,KAAK,EAAE,gBAAgB,GAAG,IAAI,CAAC;KAC/B,GAAG,OAAO,CAAC,IAAI,CAAC;IAgDjB;;OAEG;IACG,kBAAkB,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAoBvD,wBAAwB,CAC7B,MAAM,EAAE,wBAAwB,GAC9B,OAAO,CAAC,IAAI,CAAC;IAYhB,OAAO,CAAC,YAAY;IASpB,OAAO,CAAC,WAAW;IAQnB,OAAO,CAAC,YAAY;CAcpB"}