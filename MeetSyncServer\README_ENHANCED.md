﻿# MediaSoup Video Rooms - Enhanced Version

一个基于 MediaSoup 的现代化视频会议系统，具有用户认证、权限管理和美观的用户界面。

## ? 新功能

### ? 用户认证系统
- 用户注册和登录
- JWT 令牌认证
- 游客模式支持
- 会话管理

### ? 权限系统
- **游客 (Guest)**: 仅观看
- **普通用户 (User)**: 音频通话
- **高级用户 (Premium)**: 音频 + 视频通话
- **管理员 (Admin)**: 完全访问权限（包括屏幕共享）

### ? 现代化界面
- 响应式设计
- Bootstrap 5 + 自定义样式
- 用户仪表板
- 房间管理界面
- 权限状态显示

### ?? 数据库支持
- MySQL 数据库
- 用户管理
- 房间管理
- 权限控制
- 活动日志

## ? 系统要求

- Node.js 16+
- MySQL 5.7+ 或 8.0+
- HTTPS 证书（用于 WebRTC）

## ?? 安装和配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd mediasoup-sfu-webrtc-video-rooms
```

### 2. 安装依赖
```bash
npm install
```

### 3. 配置环境变量
复制 `.env.example` 到 `.env` 并配置：
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=mediasoup_rooms

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# 服务器配置
PORT=3016
NODE_ENV=development
```

### 4. 初始化数据库
```bash
npm run init-db
```

### 5. 启动服务器
```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

## ? 使用指南

### 访问系统
1. 打开浏览器访问 `https://localhost:3016`
2. 首次访问会重定向到登录页面

### 用户注册/登录
1. 在登录页面选择"注册"标签
2. 填写用户名、邮箱和密码
3. 注册成功后自动登录

### 游客模式
1. 在登录页面点击"Continue as Guest"
2. 输入显示名称
3. 以游客身份进入系统（仅观看权限）

### 创建房间
1. 登录后在仪表板点击"Create Room"
2. 填写房间信息：
   - 房间ID（唯一标识）
   - 房间名称
   - 描述（可选）
   - 最大参与者数量
   - 是否公开
   - 所需最低角色

### 加入房间
1. 在仪表板的"Public Rooms"中选择房间
2. 或点击"Join Room"输入房间ID
3. 如果房间有密码，需要输入密码

### 权限说明
- **游客**: 只能观看其他人的视频/音频
- **普通用户**: 可以开启音频
- **高级用户**: 可以开启音频和视频
- **管理员**: 可以使用所有功能，包括屏幕共享

## ? 开发

### 项目结构
```
├── src/
│   ├── app.js              # 主应用文件
│   ├── config.js           # 配置文件
│   ├── database/           # 数据库相关
│   │   ├── connection.js   # 数据库连接
│   │   └── init.sql        # 初始化SQL
│   ├── middleware/         # 中间件
│   │   └── auth.js         # 认证中间件
│   ├── models/             # 数据模型
│   │   ├── User.js         # 用户模型
│   │   └── Room.js         # 房间模型
│   ├── routes/             # API路由
│   │   ├── auth.js         # 认证路由
│   │   └── rooms.js        # 房间路由
│   ├── Room.js             # MediaSoup房间类
│   └── Peer.js             # MediaSoup对等体类
├── public/                 # 前端文件
│   ├── login.html          # 登录页面
│   ├── dashboard.html      # 用户仪表板
│   ├── index.html          # 视频会议页面
│   ├── auth.js             # 认证逻辑
│   ├── dashboard.js        # 仪表板逻辑
│   └── ...
└── scripts/
    └── init-db.js          # 数据库初始化脚本
```

### API 端点

#### 认证 API
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/profile` - 获取用户信息
- `PUT /api/auth/profile` - 更新用户信息
- `PUT /api/auth/change-password` - 修改密码

#### 房间 API
- `GET /api/rooms/public` - 获取公开房间列表
- `GET /api/rooms/my-rooms` - 获取我的房间列表
- `GET /api/rooms/:roomId` - 获取房间详情
- `POST /api/rooms` - 创建房间
- `PUT /api/rooms/:roomId` - 更新房间
- `DELETE /api/rooms/:roomId` - 删除房间

### 数据库表结构

#### users 表
- 用户基本信息
- 角色管理
- 密码哈希

#### rooms 表
- 房间信息
- 创建者
- 访问控制

#### user_room_permissions 表
- 用户房间权限
- 权限级别
- 过期时间

#### room_activity_log 表
- 房间活动日志
- 用户行为追踪

## ? 安全特性

- JWT 令牌认证
- 密码哈希存储（bcrypt）
- 请求频率限制
- CORS 配置
- 权限验证
- SQL 注入防护

## ? 部署

### 生产环境配置
1. 设置环境变量 `NODE_ENV=production`
2. 配置 HTTPS 证书
3. 设置强密码和密钥
4. 配置防火墙规则
5. 设置反向代理（Nginx）

### Docker 部署
```bash
# 构建镜像
npm run docker-build

# 运行容器
npm run docker-run
```

## ? 贡献

欢迎提交 Issue 和 Pull Request！

## ? 许可证

ISC License

## ? 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 MySQL 服务是否运行
   - 验证数据库配置信息
   - 确保数据库用户有足够权限

2. **HTTPS 证书问题**
   - 确保 SSL 证书文件存在
   - 检查证书路径配置
   - 开发环境可以使用自签名证书

3. **权限问题**
   - 检查用户角色设置
   - 验证房间权限配置
   - 查看控制台错误信息

4. **WebRTC 连接问题**
   - 确保使用 HTTPS
   - 检查防火墙设置
   - 验证 STUN/TURN 服务器配置

### 日志查看
```bash
# 查看应用日志
npm run dev

# 查看数据库日志
# 根据你的 MySQL 配置查看相应日志文件
```

## ? 支持

如有问题，请创建 Issue 或联系开发团队。
