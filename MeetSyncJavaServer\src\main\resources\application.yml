# MeetSync Java Server Configuration
# 适配 MySQL 5.7 + 中国时区环境

server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /api
  error:
    include-message: always
    include-binding-errors: always
  # SSL 配置 (开发环境)
  ssl:
    enabled: ${SSL_ENABLED:true}
    key-store: ${SSL_KEY_STORE:classpath:keystore.p12}
    key-store-password: ${SSL_KEY_STORE_PASSWORD:meetsync123}
    key-store-type: ${SSL_KEY_STORE_TYPE:PKCS12}
    key-alias: ${SSL_KEY_ALIAS:meetsync}
    # 开发环境SSL配置
    protocol: TLS
    enabled-protocols: TLSv1.2,TLSv1.3

spring:
  application:
    name: meetsync-java-server

  # 数据源配置 - MySQL 5.7 优化
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:mediasoup_rooms}?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&characterEncoding=utf8&useUnicode=true&createDatabaseIfNotExist=true&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true
    username: ${DB_USER:root}
    password: ${DB_PASSWORD:qwertyui}
    driver-class-name: com.mysql.cj.jdbc.Driver

    # HikariCP 连接池配置
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000

  # JPA 配置 - MySQL 5.7 优化
  jpa:
    hibernate:
      ddl-auto: ${JPA_DDL_AUTO:update}
    show-sql: ${JPA_SHOW_SQL:false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL57Dialect
        format_sql: true
        jdbc:
          time_zone: Asia/Shanghai
    database-platform: org.hibernate.dialect.MySQL57Dialect
    open-in-view: false

  # 安全配置
  security:
    user:
      name: ${SECURITY_USER_NAME:admin}
      password: ${SECURITY_USER_PASSWORD:admin}

# JWT 配置
jwt:
  secret: ${JWT_SECRET:your-super-secret-jwt-key-change-this-in-production-meetsync-2024}
  expiration: ${JWT_EXPIRES_IN:86400000} # 24小时 (毫秒)

# 日志配置
logging:
  level:
    root: ${LOG_LEVEL_ROOT:INFO}
    org.meetsync: ${LOG_LEVEL_APP:INFO}
    org.springframework.security: ${LOG_LEVEL_SECURITY:WARN}
    org.springframework.web: ${LOG_LEVEL_WEB:WARN}
    org.hibernate.SQL: ${LOG_LEVEL_SQL:WARN}
    org.hibernate.type.descriptor.sql.BasicBinder: ${LOG_LEVEL_SQL_PARAMS:WARN}
    com.zaxxer.hikari: ${LOG_LEVEL_HIKARI:INFO}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE:logs/meetsync-java-server.log}

# CORS 配置
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:3016,https://localhost:3016,http://127.0.0.1:3000,http://127.0.0.1:3016,https://127.0.0.1:3016}
  allowed-methods: ${CORS_ALLOWED_METHODS:GET,POST,PUT,DELETE,OPTIONS,HEAD}
  allowed-headers: ${CORS_ALLOWED_HEADERS:*}
  allow-credentials: ${CORS_ALLOW_CREDENTIALS:true}
  max-age: ${CORS_MAX_AGE:3600}

# 应用特定配置
app:
  # 用户角色定义
  user-roles:
    guest: guest
    user: user
    premium: premium
    admin: admin

  # 权限类型定义
  permissions:
    view-only: view_only
    audio-only: audio_only
    video-audio: video_audio
    full-access: full_access

  # 业务配置
  room:
    max-participants-default: ${ROOM_MAX_PARTICIPANTS:10}
    max-participants-limit: ${ROOM_MAX_PARTICIPANTS_LIMIT:100}
    auto-create-enabled: ${ROOM_AUTO_CREATE:true}

  user:
    registration-enabled: ${USER_REGISTRATION_ENABLED:true}
    default-role: ${USER_DEFAULT_ROLE:user}

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: ${MANAGEMENT_ENDPOINTS:health,info,metrics}
  endpoint:
    health:
      show-details: ${MANAGEMENT_HEALTH_DETAILS:when-authorized}
  info:
    env:
      enabled: true

# 应用信息
info:
  app:
    name: MeetSync Java Server
    description: MeetSync后端服务 - 用户管理和房间管理
    version: 1.0.0
    encoding: UTF-8
    java:
      version: ${java.version}
  database:
    type: MySQL
    version: 5.7+
    timezone: Asia/Shanghai

# Swagger/OpenAPI 配置
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    try-it-out-enabled: true
    operations-sorter: method
    tags-sorter: alpha
    doc-expansion: none
    display-request-duration: true
  show-actuator: false
  default-consumes-media-type: application/json
  default-produces-media-type: application/json

# 注意：由于设置了 context-path: /api，实际访问路径为：
# Swagger UI: http://localhost:8080/api/swagger-ui.html
# API文档: http://localhost:8080/api/v3/api-docs

# MeetSync 媒体服务器配置
meetsync:
  media-server:
    # Node.js 媒体服务器连接配置
    url: ${MEDIA_SERVER_WS_URL:ws://localhost:3017/java-ws}
    auth-token: ${MEDIA_SERVER_TOKEN:media-server-secret-token}
    reconnect-interval: 5000
    timeout: 30000
    # 健康检查
    health-check:
      enabled: true
      interval: 60000
