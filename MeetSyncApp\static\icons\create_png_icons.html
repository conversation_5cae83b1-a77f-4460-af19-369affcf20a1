<!DOCTYPE html>
<html>
<head>
    <title>创建PNG图标</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1A1A1A; color: #E5E5E5; }
        .container { max-width: 800px; margin: 0 auto; }
        .icon-preview { display: inline-block; margin: 10px; padding: 10px; background: #2D2D2D; border-radius: 8px; }
        canvas { border: 1px solid #444; margin: 5px; }
        button { background: #FFD700; color: #1A1A1A; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #DAA520; }
        .download-link { color: #FFD700; text-decoration: none; margin: 10px; display: inline-block; }
        .download-link:hover { color: #DAA520; }
    </style>
</head>
<body>
    <div class="container">
        <h1>MeetSync 图标生成器</h1>
        <p>这个工具可以帮您生成符合黑金主题的PNG图标</p>
        
        <div class="icon-preview">
            <h3>普通状态图标 (dashboard.png)</h3>
            <canvas id="normalIcon" width="48" height="48"></canvas>
            <br>
            <button onclick="generateNormalIcon()">生成普通图标</button>
            <a id="normalDownload" class="download-link" style="display:none;">下载 dashboard.png</a>
        </div>
        
        <div class="icon-preview">
            <h3>激活状态图标 (dashboard-active.png)</h3>
            <canvas id="activeIcon" width="48" height="48"></canvas>
            <br>
            <button onclick="generateActiveIcon()">生成激活图标</button>
            <a id="activeDownload" class="download-link" style="display:none;">下载 dashboard-active.png</a>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #2D2D2D; border-radius: 8px;">
            <h3>使用说明：</h3>
            <ol>
                <li>点击"生成普通图标"和"生成激活图标"按钮</li>
                <li>点击对应的下载链接保存PNG文件</li>
                <li>将下载的文件放到 <code>MeetSyncApp/static/icons/</code> 目录下</li>
                <li>如果需要，可以修改 pages.json 使用 .png 扩展名</li>
            </ol>
        </div>
    </div>

    <script>
        function drawDashboardIcon(canvas, color, fillColor = 'transparent') {
            const ctx = canvas.getContext('2d');
            const size = 48;
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 设置样式
            ctx.strokeStyle = color;
            ctx.fillStyle = fillColor;
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            // 缩放到48x48
            const scale = size / 24;
            ctx.scale(scale, scale);
            
            // 绘制仪表板图标 (基于SVG路径)
            // 主矩形
            ctx.beginPath();
            ctx.roundRect(3, 4, 18, 12, 1);
            if (fillColor !== 'transparent') {
                ctx.fill();
            }
            ctx.stroke();
            
            // 垂直线
            ctx.beginPath();
            ctx.moveTo(7, 8);
            ctx.lineTo(7, 12);
            ctx.stroke();
            
            // 水平线1
            ctx.beginPath();
            ctx.moveTo(11, 8);
            ctx.lineTo(17, 8);
            ctx.stroke();
            
            // 水平线2
            ctx.beginPath();
            ctx.moveTo(11, 12);
            ctx.lineTo(17, 12);
            ctx.stroke();
            
            // 重置变换
            ctx.setTransform(1, 0, 0, 1, 0, 0);
        }
        
        function generateNormalIcon() {
            const canvas = document.getElementById('normalIcon');
            drawDashboardIcon(canvas, '#B8860B'); // 暗金色
            
            // 创建下载链接
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const link = document.getElementById('normalDownload');
                link.href = url;
                link.download = 'dashboard.png';
                link.style.display = 'inline-block';
            });
        }
        
        function generateActiveIcon() {
            const canvas = document.getElementById('activeIcon');
            drawDashboardIcon(canvas, '#FFD700', '#FFD700'); // 金色，带填充
            
            // 创建下载链接
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const link = document.getElementById('activeDownload');
                link.href = url;
                link.download = 'dashboard-active.png';
                link.style.display = 'inline-block';
            });
        }
        
        // 页面加载时自动生成图标
        window.onload = function() {
            generateNormalIcon();
            generateActiveIcon();
        };
    </script>
</body>
</html>
