# MeetSync App端部署检查清单

## 开发环境部署

### 1. 前置条件检查
- [ ] Node.js 已安装 (推荐 v16+)
- [ ] Java后端服务可访问 (端口8080)
- [ ] NodeJS媒体服务器可访问 (端口3016)
- [ ] 网络连接正常

### 2. 依赖安装
```bash
cd MeetSyncApp
npm install
```

### 3. 配置检查
- [ ] `config/index.js` 中的服务器地址正确
- [ ] 开发环境配置指向 `localhost:8080`
- [ ] WebSocket URL配置正确

### 4. 启动服务
```bash
# 方式1: 使用启动脚本
./start-dev.sh  # Linux/Mac
start-dev.bat   # Windows

# 方式2: 直接启动
npm run dev
```

### 5. 功能验证
- [ ] 访问登录页面正常
- [ ] 用户登录功能正常
- [ ] 房间列表加载正常
- [ ] 创建房间功能正常
- [ ] 视频会议功能正常
- [ ] 测试页面功能正常

## 生产环境部署

### 1. 环境配置
- [ ] 更新 `config/index.js` 生产环境配置
- [ ] 配置HTTPS域名
- [ ] 配置WSS WebSocket
- [ ] 配置CDN (可选)

### 2. 构建应用
```bash
# H5版本
npm run build:h5

# 移动App版本
npm run build:app-plus
```

### 3. 服务器配置
- [ ] Web服务器配置 (Nginx/Apache)
- [ ] SSL证书配置
- [ ] 反向代理配置
- [ ] 静态资源缓存配置

### 4. 安全检查
- [ ] API接口安全验证
- [ ] WebSocket连接安全
- [ ] 用户认证流程
- [ ] 数据传输加密

## 故障排除

### 常见问题

#### 1. 无法连接Java后端
**症状**: 登录失败，API请求超时
**解决方案**:
- 检查Java后端服务状态
- 验证网络连接
- 检查防火墙设置
- 使用测试页面诊断

#### 2. 媒体功能异常
**症状**: 无法开启音视频，WebRTC连接失败
**解决方案**:
- 检查NodeJS媒体服务器状态
- 验证Java后端与NodeJS连接
- 检查浏览器媒体权限
- 确认HTTPS环境 (生产环境必需)

#### 3. WebSocket连接失败
**症状**: 实时消息不工作，房间状态不更新
**解决方案**:
- 检查WebSocket URL配置
- 验证服务器WebSocket支持
- 检查代理服务器配置
- 确认防火墙WebSocket端口开放

### 调试工具

#### 1. 浏览器开发者工具
- Network标签: 检查API请求状态
- Console标签: 查看JavaScript错误
- Application标签: 检查本地存储

#### 2. 内置测试页面
- 访问 `/pages/test/java-backend`
- 运行连接测试
- 查看详细错误信息

#### 3. 日志检查
```javascript
// 在浏览器控制台中执行
console.log('当前用户:', authManager.getCurrentUser())
console.log('WebRTC状态:', webrtcJavaClient.isConnected)
console.log('配置信息:', config.server)
```

## 性能优化

### 1. 前端优化
- [ ] 代码分割和懒加载
- [ ] 图片资源优化
- [ ] 缓存策略配置
- [ ] CDN加速配置

### 2. 网络优化
- [ ] API请求缓存
- [ ] WebSocket连接池
- [ ] 媒体流质量自适应
- [ ] 网络状态监控

### 3. 用户体验优化
- [ ] 加载状态提示
- [ ] 错误处理和重试
- [ ] 离线功能支持
- [ ] 响应式设计

## 监控和维护

### 1. 监控指标
- [ ] API响应时间
- [ ] WebSocket连接稳定性
- [ ] 媒体流质量
- [ ] 用户活跃度

### 2. 日志收集
- [ ] 前端错误日志
- [ ] 用户行为日志
- [ ] 性能指标日志
- [ ] 安全事件日志

### 3. 定期维护
- [ ] 依赖包更新
- [ ] 安全补丁应用
- [ ] 性能优化调整
- [ ] 功能迭代更新

## 版本管理

### 1. 版本号规范
- 主版本号: 重大架构变更
- 次版本号: 新功能添加
- 修订号: Bug修复和小改进

### 2. 发布流程
- [ ] 代码审查
- [ ] 测试验证
- [ ] 构建打包
- [ ] 部署上线
- [ ] 监控验证

### 3. 回滚计划
- [ ] 备份当前版本
- [ ] 准备回滚脚本
- [ ] 监控部署状态
- [ ] 快速回滚机制

---

**注意**: 请根据实际部署环境调整配置和检查项目。如有问题，请参考技术文档或联系技术支持。
