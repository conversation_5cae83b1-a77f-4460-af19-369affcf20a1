package org.meetsync.entity;

public enum PermissionType {
    VIEW_ONLY("view_only"),
    AUDIO_ONLY("audio_only"),
    VIDEO_AUDIO("video_audio"),
    FULL_ACCESS("full_access");

    private final String value;

    PermissionType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static PermissionType fromString(String value) {
        for (PermissionType permission : PermissionType.values()) {
            if (permission.value.equalsIgnoreCase(value)) {
                return permission;
            }
        }
        throw new IllegalArgumentException("Unknown permission type: " + value);
    }

    @Override
    public String toString() {
        return value;
    }
}
