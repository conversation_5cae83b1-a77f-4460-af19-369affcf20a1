/* UAParser.js v2.0.3
   Copyright © 2012-2025 <PERSON><PERSON><PERSON> <<EMAIL>>
   AGPLv3 License */
function E(i){for(var e={},o=0;o<i.length;o++)e[i[o].toUpperCase()]=i[o];return e}var B=500,P="user-agent",w="",U="?",R="function",n="undefined",l="object",V="string",c="browser",u="cpu",h="device",p="engine",m="os",f="result",g="name",v="type",k="vendor",x="version",y="architecture",C="major",T="model",L="console",S="mobile",r="tablet",i="smarttv",e="wearable",G="xr",F="embedded",D="inapp",$="brands",_="formFactors",W="fullVersionList",q="platform",X="platformVersion",Y="bitness",t="sec-ch-ua",K=t+"-full-version-list",Q=t+"-arch",Z=t+"-"+Y,J=t+"-form-factors",ii=t+"-"+S,ei=t+"-"+T,oi=t+"-"+q,ti=oi+"-version",ri=[$,W,S,T,q,X,y,_,Y],ai="Amazon",o="Apple",si="ASUS",ni="BlackBerry",a="Google",wi="Huawei",bi="Lenovo",di="Honor",li="Microsoft",ci="Motorola",ui="Nvidia",hi="OnePlus",pi="OPPO",mi="Samsung",fi="Sony",gi="Xiaomi",vi="Zebra",ki="Chromium",s="Chromecast",xi="Firefox",b="Opera",yi="Facebook",d="Mobile ",Ci=" Browser",Ti="Windows",z=typeof window!==n&&window.navigator?window.navigator:void 0,O=z&&z.userAgentData?z.userAgentData:void 0,Si=function(i,e){if(typeof i===l&&0<i.length){for(var o in i)if(H(i[o])==H(e))return!0;return!1}return!!A(i)&&-1!==H(e).indexOf(H(i))},_i=function(i,e){for(var o in i)return/^(browser|cpu|device|engine|os)$/.test(o)||!!e&&_i(i[o])},A=function(i){return typeof i===V},qi=function(i){if(i){for(var e,o=[],t=N(/\\?\"/g,i).split(","),r=0;r<t.length;r++)-1<t[r].indexOf(";")?(e=Ai(t[r]).split(";v="),o[r]={brand:e[0],version:e[1]}):o[r]=Ai(t[r]);return o}},H=function(i){return A(i)?i.toLowerCase():i},zi=function(i){return A(i)?N(/[^\d\.]/g,i).split(".")[0]:void 0},M=function(i){for(var e in i){e=i[e];typeof e==l&&2==e.length?this[e[0]]=e[1]:this[e]=void 0}return this},N=function(i,e){return A(e)?e.replace(i,w):e},Oi=function(i){return N(/\\?\"/g,i)},Ai=function(i,e){if(A(i))return i=N(/^\s\s*/,i),typeof e===n?i:i.substring(0,B)},Hi=function(i,e){if(i&&e)for(var o,t,r,a,s,n=0;n<e.length&&!a;){for(var w=e[n],b=e[n+1],d=o=0;d<w.length&&!a&&w[d];)if(a=w[d++].exec(i))for(t=0;t<b.length;t++)s=a[++o],typeof(r=b[t])===l&&0<r.length?2===r.length?typeof r[1]==R?this[r[0]]=r[1].call(this,s):this[r[0]]=r[1]:3===r.length?typeof r[1]!==R||r[1].exec&&r[1].test?this[r[0]]=s?s.replace(r[1],r[2]):void 0:this[r[0]]=s?r[1].call(this,s,r[2]):void 0:4===r.length&&(this[r[0]]=s?r[3].call(this,s.replace(r[1],r[2])):void 0):this[r]=s||void 0;n+=2}},I=function(i,e){for(var o in e)if(typeof e[o]===l&&0<e[o].length){for(var t=0;t<e[o].length;t++)if(Si(e[o][t],i))return o===U?void 0:o}else if(Si(e[o],i))return o===U?void 0:o;return e.hasOwnProperty("*")?e["*"]:i},Mi={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Ni={embedded:"Automotive",mobile:"Mobile",tablet:["Tablet","EInk"],smarttv:"TV",wearable:"Watch",xr:["VR","XR"],"?":["Desktop","Unknown"],"*":void 0},Ii={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[x,[g,d+"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[x,[g,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[g,x],[/opios[\/ ]+([\w\.]+)/i],[x,[g,b+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[x,[g,b+" GX"]],[/\bopr\/([\w\.]+)/i],[x,[g,b]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[x,[g,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[x,[g,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon|otter|dooble|(?:lg |qute)browser)\/([-\w\.]+)/i,/(heytap|ovi|115|surf)browser\/([\d\.]+)/i,/(ecosia|weibo)(?:__| \w+@)([\d\.]+)/i],[g,x],[/quark(?:pc)?\/([-\w\.]+)/i],[x,[g,"Quark"]],[/\bddg\/([\w\.]+)/i],[x,[g,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[x,[g,"UCBrowser"]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[x,[g,"WeChat"]],[/konqueror\/([\w\.]+)/i],[x,[g,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[x,[g,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[x,[g,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[x,[g,"Smart "+bi+Ci]],[/(avast|avg)\/([\w\.]+)/i],[[g,/(.+)/,"$1 Secure"+Ci],x],[/\bfocus\/([\w\.]+)/i],[x,[g,xi+" Focus"]],[/\bopt\/([\w\.]+)/i],[x,[g,b+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[x,[g,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[x,[g,"Dolphin"]],[/coast\/([\w\.]+)/i],[x,[g,b+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[x,[g,"MIUI"+Ci]],[/fxios\/([\w\.-]+)/i],[x,[g,d+xi]],[/\bqihoobrowser\/?([\w\.]*)/i],[x,[g,"360"]],[/\b(qq)\/([\w\.]+)/i],[[g,/(.+)/,"$1Browser"],x],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[g,/(.+)/,"$1"+Ci],x],[/samsungbrowser\/([\w\.]+)/i],[x,[g,mi+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[x,[g,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[g,"Sogou Mobile"],x],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[g,x],[/(lbbrowser|rekonq)/i],[g],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[x,g],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[g,yi],x,[v,D]],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/(daum)apps[\/ ]([\w\.]+)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(instagram|snapchat)[\/ ]([-\w\.]+)/i],[g,x,[v,D]],[/\bgsa\/([\w\.]+) .*safari\//i],[x,[g,"GSA"],[v,D]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[x,[g,"TikTok"],[v,D]],[/\[(linkedin)app\]/i],[g,[v,D]],[/(chromium)[\/ ]([-\w\.]+)/i],[g,x],[/headlesschrome(?:\/([\w\.]+)| )/i],[x,[g,"Chrome Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[g,"Chrome WebView"],x],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[x,[g,"Android"+Ci]],[/chrome\/([\w\.]+) mobile/i],[x,[g,d+"Chrome"]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[g,x],[/version\/([\w\.\,]+) .*mobile(?:\/\w+ | ?)safari/i],[x,[g,d+"Safari"]],[/iphone .*mobile(?:\/\w+ | ?)safari/i],[[g,d+"Safari"]],[/version\/([\w\.\,]+) .*(safari)/i],[x,g],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[g,[x,"1"]],[/(webkit|khtml)\/([\w\.]+)/i],[g,x],[/(?:mobile|tablet);.*(firefox)\/([\w\.-]+)/i],[[g,d+xi],x],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[g,"Netscape"],x],[/(wolvic|librewolf)\/([\w\.]+)/i],[g,x],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[x,[g,xi+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(amaya|dillo|doris|icab|ladybird|lynx|mosaic|netsurf|obigo|polaris|w3m|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/\b(links) \(([\w\.]+)/i],[g,[x,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[g,[x,/[^\d\.]+./,w]]],cpu:[[/\b((amd|x|x86[-_]?|wow|win)64)\b/i],[[y,"amd64"]],[/(ia32(?=;))/i,/\b((i[346]|x)86)(pc)?\b/i],[[y,"ia32"]],[/\b(aarch64|arm(v?[89]e?l?|_?64))\b/i],[[y,"arm64"]],[/\b(arm(v[67])?ht?n?[fl]p?)\b/i],[[y,"armhf"]],[/( (ce|mobile); ppc;|\/[\w\.]+arm\b)/i],[[y,"arm"]],[/((ppc|powerpc)(64)?)( mac|;|\))/i],[[y,/ower/,w,H]],[/ sun4\w[;\)]/i],[[y,"sparc"]],[/\b(avr32|ia64(?=;)|68k(?=\))|\barm(?=v([1-7]|[5-7]1)l?|;|eabi)|(irix|mips|sparc)(64)?\b|pa-risc)/i],[[y,H]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[T,[k,mi],[v,r]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[T,[k,mi],[v,S]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[T,[k,o],[v,S]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[T,[k,o],[v,r]],[/(macintosh);/i],[T,[k,o]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[T,[k,"Sharp"],[v,S]],[/\b((?:brt|eln|hey2?|gdi|jdn)-a?[lnw]09|(?:ag[rm]3?|jdn2|kob2)-a?[lw]0[09]hn)(?: bui|\)|;)/i],[T,[k,di],[v,r]],[/honor([-\w ]+)[;\)]/i],[T,[k,di],[v,S]],[/\b((?:ag[rs][2356]?k?|bah[234]?|bg[2o]|bt[kv]|cmr|cpn|db[ry]2?|jdn2|got|kob2?k?|mon|pce|scm|sht?|[tw]gr|vrd)-[ad]?[lw][0125][09]b?|605hw|bg2-u03|(?:gem|fdr|m2|ple|t1)-[7a]0[1-4][lu]|t1-a2[13][lw]|mediapad[\w\. ]*(?= bui|\)))\b(?!.+d\/s)/i],[T,[k,wi],[v,r]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[T,[k,wi],[v,S]],[/oid[^\)]+; (2[\dbc]{4}(182|283|rp\w{2})[cgl]|m2105k81a?c)(?: bui|\))/i,/\b((?:red)?mi[-_ ]?pad[\w- ]*)(?: bui|\))/i],[[T,/_/g," "],[k,gi],[v,r]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i,/ ([\w ]+) miui\/v?\d/i],[[T,/_/g," "],[k,gi],[v,S]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[T,[k,pi],[v,S]],[/\b(opd2(\d{3}a?))(?: bui|\))/i],[T,[k,I,{OnePlus:["304","403","203"],"*":pi}],[v,r]],[/(vivo (5r?|6|8l?|go|one|s|x[il]?[2-4]?)[\w\+ ]*)(?: bui|\))/i],[T,[k,"BLU"],[v,S]],[/; vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[T,[k,"Vivo"],[v,S]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[T,[k,"Realme"],[v,S]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto(?! 360)[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[T,[k,ci],[v,S]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[T,[k,ci],[v,r]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[T,[k,"LG"],[v,r]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+(?!.*(?:browser|netcast|android tv|watch))(\w+)/i,/\blg-?([\d\w]+) bui/i],[T,[k,"LG"],[v,S]],[/(ideatab[-\w ]+|602lv|d-42a|a101lv|a2109a|a3500-hv|s[56]000|pb-6505[my]|tb-?x?\d{3,4}(?:f[cu]|xu|[av])|yt\d?-[jx]?\d+[lfmx])( bui|;|\)|\/)/i,/lenovo ?(b[68]0[08]0-?[hf]?|tab(?:[\w- ]+?)|tb[\w-]{6,7})( bui|;|\)|\/)/i],[T,[k,bi],[v,r]],[/(nokia) (t[12][01])/i],[k,T,[v,r]],[/(?:maemo|nokia).*(n900|lumia \d+|rm-\d+)/i,/nokia[-_ ]?(([-\w\. ]*))/i],[[T,/_/g," "],[v,S],[k,"Nokia"]],[/(pixel (c|tablet))\b/i],[T,[k,a],[v,r]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[T,[k,a],[v,S]],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[T,[k,fi],[v,S]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[T,"Xperia Tablet"],[k,fi],[v,r]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[T,[k,hi],[v,S]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[T,[k,ai],[v,r]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[T,/(.+)/g,"Fire Phone $1"],[k,ai],[v,S]],[/(playbook);[-\w\),; ]+(rim)/i],[T,k,[v,r]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[T,[k,ni],[v,S]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[T,[k,si],[v,r]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[T,[k,si],[v,S]],[/(nexus 9)/i],[T,[k,"HTC"],[v,r]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[k,[T,/_/g," "],[v,S]],[/tcl (xess p17aa)/i,/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])(_\w(\w|\w\w))?(\)| bui)/i],[T,[k,"TCL"],[v,r]],[/droid [\w\.]+; (418(?:7d|8v)|5087z|5102l|61(?:02[dh]|25[adfh]|27[ai]|56[dh]|59k|65[ah])|a509dl|t(?:43(?:0w|1[adepqu])|50(?:6d|7[adju])|6(?:09dl|10k|12b|71[efho]|76[hjk])|7(?:66[ahju]|67[hw]|7[045][bh]|71[hk]|73o|76[ho]|79w|81[hks]?|82h|90[bhsy]|99b)|810[hs]))(_\w(\w|\w\w))?(\)| bui)/i],[T,[k,"TCL"],[v,S]],[/(itel) ((\w+))/i],[[k,H],T,[v,I,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[T,[k,"Acer"],[v,r]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[T,[k,"Meizu"],[v,S]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[T,[k,"Ulefone"],[v,S]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[T,[k,"Energizer"],[v,S]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[T,[k,"Cat"],[v,S]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[T,[k,"Smartfren"],[v,S]],[/droid.+; (a(?:015|06[35]|142p?))/i],[T,[k,"Nothing"],[v,S]],[/; (x67 5g|tikeasy \w+|ac[1789]\d\w+)( b|\))/i,/archos ?(5|gamepad2?|([\w ]*[t1789]|hello) ?\d+[\w ]*)( b|\))/i],[T,[k,"Archos"],[v,r]],[/archos ([\w ]+)( b|\))/i,/; (ac[3-6]\d\w{2,8})( b|\))/i],[T,[k,"Archos"],[v,S]],[/(imo) (tab \w+)/i,/(infinix) (x1101b?)/i],[k,T,[v,r]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus(?! zenw)|dell|jolla|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (blu|hmd|imo|tcl)[_ ]([\w\+ ]+?)(?: bui|\)|; r)/i,/(hp) ([\w ]+\w)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w ]+?)(?: bui|\)|\/)/i,/(oppo) ?([\w ]+) bui/i],[k,T,[v,S]],[/(kobo)\s(ereader|touch)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i],[k,T,[v,r]],[/(surface duo)/i],[T,[k,li],[v,r]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[T,[k,"Fairphone"],[v,S]],[/((?:tegranote|shield t(?!.+d tv))[\w- ]*?)(?: b|\))/i],[T,[k,ui],[v,r]],[/(sprint) (\w+)/i],[k,T,[v,S]],[/(kin\.[onetw]{3})/i],[[T,/\./g," "],[k,li],[v,S]],[/droid.+; ([c6]+|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[T,[k,vi],[v,r]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[T,[k,vi],[v,S]],[/smart-tv.+(samsung)/i],[k,[v,i]],[/hbbtv.+maple;(\d+)/i],[[T,/^/,"SmartTV"],[k,mi],[v,i]],[/tcast.+(lg)e?. ([-\w]+)/i],[k,T,[v,i]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[k,"LG"],[v,i]],[/(apple) ?tv/i],[k,[T,o+" TV"],[v,i]],[/crkey.*devicetype\/chromecast/i],[[T,s+" Third Generation"],[k,a],[v,i]],[/crkey.*devicetype\/([^/]*)/i],[[T,/^/,"Chromecast "],[k,a],[v,i]],[/fuchsia.*crkey/i],[[T,s+" Nest Hub"],[k,a],[v,i]],[/crkey/i],[[T,s],[k,a],[v,i]],[/(portaltv)/i],[T,[k,yi],[v,i]],[/droid.+aft(\w+)( bui|\))/i],[T,[k,ai],[v,i]],[/(shield \w+ tv)/i],[T,[k,ui],[v,i]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[T,[k,"Sharp"],[v,i]],[/(bravia[\w ]+)( bui|\))/i],[T,[k,fi],[v,i]],[/(mi(tv|box)-?\w+) bui/i],[T,[k,gi],[v,i]],[/Hbbtv.*(technisat) (.*);/i],[k,T,[v,i]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[k,Ai],[T,Ai],[v,i]],[/droid.+; ([\w- ]+) (?:android tv|smart[- ]?tv)/i],[T,[v,i]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[v,i]],[/(ouya)/i,/(nintendo) (\w+)/i],[k,T,[v,L]],[/droid.+; (shield)( bui|\))/i],[T,[k,ui],[v,L]],[/(playstation \w+)/i],[T,[k,fi],[v,L]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[T,[k,li],[v,L]],[/\b(sm-[lr]\d\d[0156][fnuw]?s?|gear live)\b/i],[T,[k,mi],[v,e]],[/((pebble))app/i,/(asus|google|lg|oppo) ((pixel |zen)?watch[\w ]*)( bui|\))/i],[k,T,[v,e]],[/(ow(?:19|20)?we?[1-3]{1,3})/i],[T,[k,pi],[v,e]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[T,[k,o],[v,e]],[/(opwwe\d{3})/i],[T,[k,hi],[v,e]],[/(moto 360)/i],[T,[k,ci],[v,e]],[/(smartwatch 3)/i],[T,[k,fi],[v,e]],[/(g watch r)/i],[T,[k,"LG"],[v,e]],[/droid.+; (wt63?0{2,3})\)/i],[T,[k,vi],[v,e]],[/droid.+; (glass) \d/i],[T,[k,a],[v,G]],[/(pico) (4|neo3(?: link|pro)?)/i],[k,T,[v,G]],[/(quest( \d| pro)?s?).+vr/i],[T,[k,yi],[v,G]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[k,[v,F]],[/(aeobc)\b/i],[T,[k,ai],[v,F]],[/(homepod).+mac os/i],[T,[k,o],[v,F]],[/windows iot/i],[[v,F]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+?(mobile|vr|\d) safari/i],[T,[v,I,{mobile:"Mobile",xr:"VR","*":r}]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[v,r]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[v,S]],[/droid .+?; ([\w\. -]+)( bui|\))/i],[T,[k,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[x,[g,"EdgeHTML"]],[/(arkweb)\/([\w\.]+)/i],[g,x],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[x,[g,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[g,x],[/ladybird\//i],[[g,"LibWeb"]],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[x,g]],os:[[/microsoft (windows) (vista|xp)/i],[g,x],[/(windows (?:phone(?: os)?|mobile|iot))[\/ ]?([\d\.\w ]*)/i],[g,[x,I,Mi]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[x,I,Mi],[g,Ti]],[/[adehimnop]{4,7}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[x,/_/g,"."],[g,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[g,"macOS"],[x,/_/g,"."]],[/android ([\d\.]+).*crkey/i],[x,[g,s+" Android"]],[/fuchsia.*crkey\/([\d\.]+)/i],[x,[g,s+" Fuchsia"]],[/crkey\/([\d\.]+).*devicetype\/smartspeaker/i],[x,[g,s+" SmartSpeaker"]],[/linux.*crkey\/([\d\.]+)/i],[x,[g,s+" Linux"]],[/crkey\/([\d\.]+)/i],[x,[g,s]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[x,g],[/(ubuntu) ([\w\.]+) like android/i],[[g,/(.+)/,"$1 Touch"],x],[/(android|bada|blackberry|kaios|maemo|meego|openharmony|qnx|rim tablet os|sailfish|series40|symbian|tizen|webos)\w*[-\/\.; ]?([\d\.]*)/i],[g,x],[/\(bb(10);/i],[x,[g,ni]],[/(?:symbian ?os|symbos|s60(?=;)|series ?60)[-\/ ]?([\w\.]*)/i],[x,[g,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[x,[g,xi+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[x,[g,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[x,[g,"watchOS"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[g,"Chrome OS"],x],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) (\w+)/i,/(xbox); +xbox ([^\);]+)/i,/(pico) .+os([\w\.]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux)(?: arm\w*| x86\w*| ?)([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[g,x],[/(sunos) ?([\w\.\d]*)/i],[[g,"Solaris"],x],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[g,x]]},ji=(b={init:{},isIgnore:{},isIgnoreRgx:{},toString:{}},M.call(b.init,[[c,[g,x,C,v]],[u,[y]],[h,[v,T,k]],[p,[g,x]],[m,[g,x]]]),M.call(b.isIgnore,[[c,[x,C]],[p,[x]],[m,[x]]]),M.call(b.isIgnoreRgx,[[c,/ ?browser$/i],[m,/ ?os$/i]]),M.call(b.toString,[[c,[g,x]],[u,[y]],[h,[k,T]],[p,[g,x]],[m,[g,x]]]),b),Ei=function(e,i){var o=ji.init[i],t=ji.isIgnore[i]||0,r=ji.isIgnoreRgx[i]||0,a=ji.toString[i]||0;function s(){M.call(this,o)}return s.prototype.getItem=function(){return e},s.prototype.withClientHints=function(){return O?O.getHighEntropyValues(ri).then(function(i){return e.setCH(new Bi(i,!1)).parseCH().get()}):e.parseCH().get()},s.prototype.withFeatureCheck=function(){return e.detectFeature().get()},i!=f&&(s.prototype.is=function(i){var e,o=!1;for(e in this)if(this.hasOwnProperty(e)&&!Si(t,e)&&H(r?N(r,this[e]):this[e])==H(r?N(r,i):i)){if(o=!0,i!=n)break}else if(i==n&&o){o=!o;break}return o},s.prototype.toString=function(){var i,e=w;for(i in a)typeof this[a[i]]!==n&&(e+=(e?" ":w)+this[a[i]]);return e||n}),O||(s.prototype.then=function(i){function e(){for(var i in o)o.hasOwnProperty(i)&&(this[i]=o[i])}var o=this,t=(e.prototype={is:s.prototype.is,toString:s.prototype.toString},new e);return i(t),t}),new s};function Bi(i,e){if(i=i||{},M.call(this,ri),e)M.call(this,[[$,qi(i[t])],[W,qi(i[K])],[S,/\?1/.test(i[ii])],[T,Oi(i[ei])],[q,Oi(i[oi])],[X,Oi(i[ti])],[y,Oi(i[Q])],[_,qi(i[J])],[Y,Oi(i[Z])]]);else for(var o in i)this.hasOwnProperty(o)&&typeof i[o]!==n&&(this[o]=i[o])}function Pi(i,e,o,t){return this.get=function(i){return i?this.data.hasOwnProperty(i)?this.data[i]:void 0:this.data},this.set=function(i,e){return this.data[i]=e,this},this.setCH=function(i){return this.uaCH=i,this},this.detectFeature=function(){if(z&&z.userAgent==this.ua)switch(this.itemType){case c:z.brave&&typeof z.brave.isBrave==R&&this.set(g,"Brave");break;case h:!this.get(v)&&O&&O[S]&&this.set(v,S),"Macintosh"==this.get(T)&&z&&typeof z.standalone!==n&&z.maxTouchPoints&&2<z.maxTouchPoints&&this.set(T,"iPad").set(v,r);break;case m:!this.get(g)&&O&&O[q]&&this.set(g,O[q]);break;case f:var e=this.data,i=function(i){return e[i].getItem().detectFeature().get()};this.set(c,i(c)).set(u,i(u)).set(h,i(h)).set(p,i(p)).set(m,i(m))}return this},this.parseUA=function(){return this.itemType!=f&&Hi.call(this.data,this.ua,this.rgxMap),this.itemType==c&&this.set(C,zi(this.get(x))),this},this.parseCH=function(){var i,e=this.uaCH,o=this.rgxMap;switch(this.itemType){case c:case p:var t,r=e[W]||e[$];if(r)for(var a in r){var s=r[a].brand||r[a],a=r[a].version;this.itemType!=c||/not.a.brand/i.test(s)||t&&(!/chrom/i.test(t)||s==ki)||(s=I(s,{Chrome:"Google Chrome",Edge:"Microsoft Edge","Chrome WebView":"Android WebView","Chrome Headless":"HeadlessChrome","Huawei Browser":"HuaweiBrowser","MIUI Browser":"Miui Browser","Opera Mobi":"OperaMobile",Yandex:"YaBrowser"}),this.set(g,s).set(x,a).set(C,zi(a)),t=s),this.itemType==p&&s==ki&&this.set(x,a)}break;case u:var n=e[y];n&&("64"==e[Y]&&(n+="64"),Hi.call(this.data,n+";",o));break;case h:if(e[S]&&this.set(v,S),e[T]&&(this.set(T,e[T]),this.get(v)&&this.get(k)||(Hi.call(n={},"droid 9; "+e[T]+")",o),!this.get(v)&&n.type&&this.set(v,n.type),!this.get(k)&&n.vendor&&this.set(k,n.vendor))),e[_]){if("string"!=typeof e[_])for(var w=0;!i&&w<e[_].length;)i=I(e[_][w++],Ni);else i=I(e[_],Ni);this.set(v,i)}break;case m:var b,n=e[q];n&&(b=e[X],n==Ti&&(b=13<=parseInt(zi(b),10)?"11":"10"),this.set(g,n).set(x,b)),this.get(g)==Ti&&"Xbox"==e[T]&&this.set(g,"Xbox").set(x,void 0);break;case f:var d=this.data,n=function(i){return d[i].getItem().setCH(e).parseCH().get()};this.set(c,n(c)).set(u,n(u)).set(h,n(h)).set(p,n(p)).set(m,n(m))}return this},M.call(this,[["itemType",i],["ua",e],["uaCH",t],["rgxMap",o],["data",Ei(this,i)]]),this}function j(i,e,o){var t,r,a,s,n;return typeof i===l?(e=_i(i,!0)?(typeof e===l&&(o=e),i):void(o=i),i=void 0):typeof i!==V||_i(e,!0)||(o=e,e=void 0),o&&typeof o.append===R&&(t={},o.forEach(function(i,e){t[e]=i}),o=t),this instanceof j?(r=typeof i===V?i:o&&o[P]?o[P]:z&&z.userAgent?z.userAgent:w,a=new Bi(o,!0),s=e?((i,e)=>{var o,t={},r=e;if(!_i(e))for(var a in r={},e)for(var s in e[a])r[s]=e[a][s].concat(r[s]||[]);for(o in i)t[o]=r[o]&&r[o].length%2==0?r[o].concat(i[o]):i[o];return t})(Ii,e):Ii,M.call(this,[["getBrowser",(n=function(i){return i==f?function(){return new Pi(i,r,s,a).set("ua",r).set(c,this.getBrowser()).set(u,this.getCPU()).set(h,this.getDevice()).set(p,this.getEngine()).set(m,this.getOS()).get()}:function(){return new Pi(i,r,s[i],a).parseUA().get()}})(c)],["getCPU",n(u)],["getDevice",n(h)],["getEngine",n(p)],["getOS",n(m)],["getResult",n(f)],["getUA",function(){return r}],["setUA",function(i){return A(i)&&(r=i.length>B?Ai(i,B):i),this}]]).setUA(r),this):new j(i,e,o).getResult()}j.VERSION="2.0.3",j.BROWSER=E([g,x,C,v]),j.CPU=E([y]),j.DEVICE=E([T,k,v,L,S,i,r,e,F]),j.ENGINE=j.OS=E([g,x]);export{j as UAParser};