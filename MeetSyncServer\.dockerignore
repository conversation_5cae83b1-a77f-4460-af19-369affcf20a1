# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 开发工具
.git/
.gitignore
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
logs/
*.log

# 运行时文件
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
.nyc_output/

# 依赖目录
.npm/
.eslintcache

# 可选的 npm 缓存目录
.npm

# 可选的 REPL 历史
.node_repl_history

# 输出的二进制文件
*.tgz

# Yarn 完整性文件
.yarn-integrity

# dotenv 环境变量文件
.env
.env.test
.env.local
.env.production

# 测试文件
test/
tests/
__tests__/
*.test.js
*.spec.js

# 文档
README.md
*.md
docs/

# Docker 相关
Dockerfile
.dockerignore
docker-compose*.yml

# 备份文件
*.bak
*.backup

# 临时文件
tmp/
temp/
