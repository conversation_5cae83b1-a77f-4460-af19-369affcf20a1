/**
 * Java 服务端连接器
 * 负责与 Java 服务端的 WebSocket 通信
 */

const WebSocket = require('ws')
const EventEmitter = require('events')

class JavaServerConnector extends EventEmitter {
  constructor(mediaServer) {
    super()
    this.mediaServer = mediaServer
    this.ws = null
    this.isConnected = false
    this.reconnectInterval = 5000
    this.heartbeatInterval = 30000
    this.heartbeatTimer = null
    this.reconnectTimer = null
    this.pendingRequests = new Map() // requestId -> { resolve, reject, timeout }
    
    // 从配置获取连接信息
    this.javaServerUrl = process.env.JAVA_SERVER_WS_URL || 'ws://localhost:8080/media-server-ws'
    this.authToken = process.env.JAVA_SERVER_TOKEN || 'media-server-secret-token'
  }

  async connect() {
    return new Promise((resolve, reject) => {
      try {
        console.log(`🔗 连接到 Java 服务端: ${this.javaServerUrl}`)
        
        this.ws = new WebSocket(this.javaServerUrl, {
          rejectUnauthorized: false, // 接受自签名证书
          headers: {
            'Authorization': `Bearer ${this.authToken}`,
            'User-Agent': 'MeetSync-MediaServer/1.0'
          }
        })

        this.ws.on('open', () => {
          console.log('✅ 已连接到 Java 服务端')
          this.isConnected = true
          this.startHeartbeat()
          this.emit('connected')
          resolve()
        })

        this.ws.on('message', (data) => {
          this.handleMessage(data)
        })

        this.ws.on('close', (code, reason) => {
          console.log(`🔌 与 Java 服务端连接断开 [${code}]: ${reason}`)
          this.isConnected = false
          this.stopHeartbeat()
          this.emit('disconnected')
          this.scheduleReconnect()
        })

        this.ws.on('error', (error) => {
          console.error('❌ Java 服务端连接错误:', error)
          this.emit('error', error)
          if (!this.isConnected) {
            reject(error)
          }
        })

        // 连接超时
        setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error('连接超时'))
          }
        }, 10000)

      } catch (error) {
        reject(error)
      }
    })
  }

  handleMessage(data) {
    try {
      const message = JSON.parse(data.toString())
      console.log('📨 收到 Java 服务端消息:', message.type)

      switch (message.type) {
        case 'ping':
          this.sendPong()
          break
        case 'pong':
            console.log('🏓 收到 Java 服务端心跳响应')
          break
          
        case 'request':
          this.handleRequest(message)
          break
          
        case 'response':
          this.handleResponse(message)
          break
          
        default:
          console.warn('⚠️ 未知消息类型:', message.type)
      }
    } catch (error) {
      console.error('❌ 处理消息失败:', error)
    }
  }

  async handleRequest(message) {
    const { requestId, method, data } = message
    
    try {
      console.log(`🔄 处理请求: ${method} [${requestId}]`)
      
      const result = await this.mediaServer.handleJavaRequest(method, requestId, data)
      
      this.sendResponse(requestId, result.success, result.data, result.error)
      
    } catch (error) {
      console.error(`❌ 处理请求失败 [${method}]:`, error)
      this.sendResponse(requestId, false, null, error.message)
    }
  }

  handleResponse(message) {
    const { requestId, success, data, error } = message
    const pending = this.pendingRequests.get(requestId)
    
    if (pending) {
      clearTimeout(pending.timeout)
      this.pendingRequests.delete(requestId)
      
      if (success) {
        pending.resolve(data)
      } else {
        pending.reject(new Error(error || 'Request failed'))
      }
    }
  }

  sendResponse(requestId, success, data = null, error = null) {
    const response = {
      type: 'response',
      requestId,
      success,
      data,
      error,
      timestamp: Date.now()
    }
    
    this.send(response)
  }

  async sendRequest(method, data, timeout = 30000) {
    return new Promise((resolve, reject) => {
      if (!this.isConnected) {
        reject(new Error('未连接到 Java 服务端'))
        return
      }

      const requestId = this.generateRequestId()
      const request = {
        type: 'request',
        requestId,
        method,
        data,
        timestamp: Date.now()
      }

      // 设置超时
      const timeoutTimer = setTimeout(() => {
        this.pendingRequests.delete(requestId)
        reject(new Error(`请求超时: ${method}`))
      }, timeout)

      this.pendingRequests.set(requestId, {
        resolve,
        reject,
        timeout: timeoutTimer
      })

      this.send(request)
    })
  }

  send(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('⚠️ WebSocket 未连接，无法发送消息')
    }
  }

  sendPong() {
    this.send({ type: 'pong', timestamp: Date.now() })
  }

  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'heartbeat', timestamp: Date.now() })
      }
    }, this.heartbeatInterval)
  }

  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  scheduleReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }
    
    this.reconnectTimer = setTimeout(() => {
      console.log('🔄 尝试重新连接到 Java 服务端...')
      this.connect().catch(error => {
        console.error('❌ 重连失败:', error)
        this.scheduleReconnect()
      })
    }, this.reconnectInterval)
  }

  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  close() {
    this.stopHeartbeat()
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }
    
    // 拒绝所有待处理的请求
    for (const [requestId, pending] of this.pendingRequests) {
      clearTimeout(pending.timeout)
      pending.reject(new Error('连接已关闭'))
    }
    this.pendingRequests.clear()
    
    if (this.ws) {
      this.ws.close()
    }
  }

  // 通知 Java 服务端生产者状态变化
  notifyProducerStatusChange(roomId, userId, producerId, status, mediaType = 'unknown', reason = 'unknown') {
    this.send({
      type: 'notification',
      event: 'producer_status_change',
      data: {
        roomId,
        userId,
        producerId,
        status, // 'created', 'closed', 'paused', 'resumed'
        mediaType,
        reason
      },
      timestamp: Date.now()
    })
  }

  // 通知 Java 服务端消费者状态变化
  notifyConsumerStatusChange(roomId, userId, consumerId, status) {
    this.send({
      type: 'notification',
      event: 'consumer_status_change',
      data: {
        roomId,
        userId,
        consumerId,
        status // 'created', 'closed', 'paused', 'resumed'
      },
      timestamp: Date.now()
    })
  }

  // 通知 Java 服务端有新生产者
  notifyNewProducers(roomId, producers) {
    this.send({
      type: 'notification',
      event: 'new_producers',
      data: {
        roomId,
        producers
      },
      timestamp: Date.now()
    })
  }

  // 通知 Java 服务端用户离开房间
  notifyUserLeft(roomId, userId) {
    this.send({
      type: 'notification',
      event: 'user_left',
      data: {
        roomId,
        userId
      },
      timestamp: Date.now()
    })
  }
}

module.exports = JavaServerConnector
