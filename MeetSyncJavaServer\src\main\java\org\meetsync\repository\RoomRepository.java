package org.meetsync.repository;

import org.meetsync.entity.Room;
import org.meetsync.entity.User;
import org.meetsync.entity.UserRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface RoomRepository extends JpaRepository<Room, Long> {

    /**
     * Find room by room ID
     */
    Optional<Room> findByRoomIdAndIsActiveTrue(String roomId);

    /**
     * Check if room ID exists
     */
    boolean existsByRoomId(String roomId);

    /**
     * Find all public rooms
     */
    @Query("SELECT r FROM Room r WHERE r.isPublic = true AND r.isActive = true ORDER BY r.createdAt DESC")
    List<Room> findPublicRooms();

    /**
     * Find rooms created by user
     */
    List<Room> findByCreatorAndIsActiveTrueOrderByCreatedAtDesc(User creator);

    /**
     * Find rooms by creator ID
     */
    @Query("SELECT r FROM Room r WHERE r.creator.id = :creatorId AND r.isActive = true ORDER BY r.createdAt DESC")
    List<Room> findByCreatorId(@Param("creatorId") Long creatorId);

    /**
     * Find public rooms that user can access based on required role
     */
    @Query("SELECT r FROM Room r WHERE r.isPublic = true AND r.isActive = true AND " +
           "(r.requiredRole = 'GUEST' OR " +
           "(r.requiredRole = 'USER' AND :userRole IN ('USER', 'PREMIUM', 'ADMIN')) OR " +
           "(r.requiredRole = 'PREMIUM' AND :userRole IN ('PREMIUM', 'ADMIN')) OR " +
           "(r.requiredRole = 'ADMIN' AND :userRole = 'ADMIN')) " +
           "ORDER BY r.createdAt DESC")
    List<Room> findAccessiblePublicRooms(@Param("userRole") String userRole);

    /**
     * Find rooms with specific required role
     */
    List<Room> findByRequiredRoleAndIsActiveTrueOrderByCreatedAtDesc(UserRole requiredRole);

    /**
     * Find rooms created after a specific date
     */
    List<Room> findByCreatedAtAfterAndIsActiveTrueOrderByCreatedAtDesc(LocalDateTime date);

    /**
     * Deactivate room
     */
    @Modifying
    @Query("UPDATE Room r SET r.isActive = false WHERE r.id = :roomId")
    void deactivateRoom(@Param("roomId") Long roomId);

    /**
     * Deactivate room by room ID
     */
    @Modifying
    @Query("UPDATE Room r SET r.isActive = false WHERE r.roomId = :roomId")
    void deactivateRoomByRoomId(@Param("roomId") String roomId);

    /**
     * Count rooms by creator
     */
    @Query("SELECT COUNT(r) FROM Room r WHERE r.creator.id = :creatorId AND r.isActive = true")
    long countByCreatorId(@Param("creatorId") Long creatorId);

    /**
     * Count public rooms
     */
    @Query("SELECT COUNT(r) FROM Room r WHERE r.isPublic = true AND r.isActive = true")
    long countPublicRooms();

    /**
     * Find rooms by name containing (case insensitive)
     */
    @Query("SELECT r FROM Room r WHERE LOWER(r.name) LIKE LOWER(CONCAT('%', :name, '%')) AND r.isActive = true ORDER BY r.createdAt DESC")
    List<Room> findByNameContainingIgnoreCase(@Param("name") String name);

    /**
     * Find rooms that user has specific permissions for
     */
    @Query("SELECT DISTINCT r FROM Room r " +
           "JOIN r.userPermissions up " +
           "WHERE up.user.id = :userId AND up.isActive = true AND r.isActive = true " +
           "ORDER BY r.createdAt DESC")
    List<Room> findRoomsWithUserPermissions(@Param("userId") Long userId);

    /**
     * Find rooms user can access (either public with matching role or has explicit permission)
     */
    @Query("SELECT DISTINCT r FROM Room r " +
           "LEFT JOIN r.userPermissions up " +
           "WHERE r.isActive = true AND " +
           "((r.isPublic = true AND " +
           "  (r.requiredRole = 'GUEST' OR " +
           "   (r.requiredRole = 'USER' AND :userRole IN ('USER', 'PREMIUM', 'ADMIN')) OR " +
           "   (r.requiredRole = 'PREMIUM' AND :userRole IN ('PREMIUM', 'ADMIN')) OR " +
           "   (r.requiredRole = 'ADMIN' AND :userRole = 'ADMIN'))) OR " +
           " (up.user.id = :userId AND up.isActive = true)) " +
           "ORDER BY r.createdAt DESC")
    List<Room> findAccessibleRooms(@Param("userId") Long userId, @Param("userRole") String userRole);
}
