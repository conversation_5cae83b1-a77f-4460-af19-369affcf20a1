/**
 * Service Worker 管理工具
 * 提供 Service Worker 状态监控和管理功能
 */

class ServiceWorkerManager {
  constructor() {
    this.registration = null
    this.isSupported = 'serviceWorker' in navigator
    this.listeners = new Map()
    
    this.init()
  }

  /**
   * 初始化
   */
  async init() {
    if (!this.isSupported) {
      console.warn('⚠️ Service Worker 不被支持')
      return
    }

    try {
      // 获取现有注册
      this.registration = await navigator.serviceWorker.getRegistration()
      
      if (this.registration) {
        console.log('📱 发现已注册的 Service Worker:', this.registration)
        this.setupEventListeners()
      }
    } catch (error) {
      console.error('❌ 获取 Service Worker 注册失败:', error)
    }
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    if (!this.registration) return

    // 监听状态变化
    if (this.registration.installing) {
      this.trackWorker(this.registration.installing, 'installing')
    }

    if (this.registration.waiting) {
      this.trackWorker(this.registration.waiting, 'waiting')
    }

    if (this.registration.active) {
      this.trackWorker(this.registration.active, 'active')
    }

    // 监听更新
    this.registration.addEventListener('updatefound', () => {
      console.log('🔄 Service Worker 更新发现')
      this.emit('updatefound', this.registration.installing)
      
      if (this.registration.installing) {
        this.trackWorker(this.registration.installing, 'installing')
      }
    })
  }

  /**
   * 跟踪 Worker 状态
   */
  trackWorker(worker, initialState) {
    console.log(`📊 跟踪 Service Worker 状态: ${initialState}`)
    
    worker.addEventListener('statechange', () => {
      console.log(`🔄 Service Worker 状态变更: ${worker.state}`)
      this.emit('statechange', { worker, state: worker.state })
      
      switch (worker.state) {
        case 'installed':
          if (navigator.serviceWorker.controller) {
            console.log('🆕 新版本 Service Worker 已安装，等待激活')
            this.emit('updateready', worker)
          } else {
            console.log('✅ Service Worker 首次安装完成')
            this.emit('installed', worker)
          }
          break
          
        case 'activated':
          console.log('🚀 Service Worker 已激活')
          this.emit('activated', worker)
          break
          
        case 'redundant':
          console.log('🗑️ Service Worker 已废弃')
          this.emit('redundant', worker)
          break
      }
    })
  }

  /**
   * 事件监听
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  /**
   * 移除事件监听
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`Service Worker 事件处理错误 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    if (!this.isSupported) {
      return { supported: false, status: 'not_supported' }
    }

    if (!this.registration) {
      return { supported: true, status: 'not_registered' }
    }

    const status = {
      supported: true,
      status: 'registered',
      scope: this.registration.scope,
      installing: !!this.registration.installing,
      waiting: !!this.registration.waiting,
      active: !!this.registration.active
    }

    if (this.registration.active) {
      status.activeState = this.registration.active.state
    }

    return status
  }

  /**
   * 手动检查更新
   */
  async checkForUpdate() {
    if (!this.registration) {
      console.warn('⚠️ 没有注册的 Service Worker')
      return false
    }

    try {
      console.log('🔍 检查 Service Worker 更新...')
      await this.registration.update()
      console.log('✅ 更新检查完成')
      return true
    } catch (error) {
      console.error('❌ 检查更新失败:', error)
      return false
    }
  }

  /**
   * 跳过等待，立即激活新版本
   */
  async skipWaiting() {
    if (!this.registration || !this.registration.waiting) {
      console.warn('⚠️ 没有等待中的 Service Worker')
      return false
    }

    try {
      console.log('⏭️ 跳过等待，激活新版本...')
      this.registration.waiting.postMessage({ type: 'SKIP_WAITING' })
      return true
    } catch (error) {
      console.error('❌ 跳过等待失败:', error)
      return false
    }
  }

  /**
   * 卸载 Service Worker
   */
  async unregister() {
    if (!this.registration) {
      console.warn('⚠️ 没有注册的 Service Worker')
      return false
    }

    try {
      console.log('🗑️ 卸载 Service Worker...')
      const result = await this.registration.unregister()
      
      if (result) {
        console.log('✅ Service Worker 卸载成功')
        this.registration = null
      } else {
        console.log('❌ Service Worker 卸载失败')
      }
      
      return result
    } catch (error) {
      console.error('❌ 卸载 Service Worker 时出错:', error)
      return false
    }
  }

  /**
   * 清除所有缓存
   */
  async clearCaches() {
    try {
      console.log('🧹 清除所有缓存...')
      const cacheNames = await caches.keys()
      
      await Promise.all(
        cacheNames.map(cacheName => {
          console.log(`🗑️ 删除缓存: ${cacheName}`)
          return caches.delete(cacheName)
        })
      )
      
      console.log('✅ 所有缓存已清除')
      return true
    } catch (error) {
      console.error('❌ 清除缓存失败:', error)
      return false
    }
  }

  /**
   * 获取缓存信息
   */
  async getCacheInfo() {
    try {
      const cacheNames = await caches.keys()
      const cacheInfo = []

      for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName)
        const keys = await cache.keys()
        
        cacheInfo.push({
          name: cacheName,
          size: keys.length,
          urls: keys.map(request => request.url)
        })
      }

      return cacheInfo
    } catch (error) {
      console.error('❌ 获取缓存信息失败:', error)
      return []
    }
  }

  /**
   * 发送消息给 Service Worker
   */
  async postMessage(message) {
    if (!this.registration || !this.registration.active) {
      console.warn('⚠️ 没有活跃的 Service Worker')
      return false
    }

    try {
      this.registration.active.postMessage(message)
      return true
    } catch (error) {
      console.error('❌ 发送消息给 Service Worker 失败:', error)
      return false
    }
  }
}

// 创建全局实例
const swManager = new ServiceWorkerManager()

// 导出
export default swManager

// 全局访问（用于调试）
if (typeof window !== 'undefined') {
  window.swManager = swManager
}
