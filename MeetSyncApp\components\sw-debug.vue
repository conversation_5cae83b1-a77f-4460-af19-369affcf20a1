<template>
  <view v-if="showDebug" class="sw-debug-panel">
    <view class="debug-header">
      <text class="debug-title">Service Worker 调试面板</text>
      <button class="close-btn" @click="showDebug = false">✕</button>
    </view>
    
    <view class="debug-content">
      <!-- 状态信息 -->
      <view class="debug-section">
        <text class="section-title">📊 状态信息</text>
        <view class="status-item">
          <text class="label">支持状态:</text>
          <text class="value" :class="status.supported ? 'success' : 'error'">
            {{ status.supported ? '✅ 支持' : '❌ 不支持' }}
          </text>
        </view>
        <view class="status-item">
          <text class="label">注册状态:</text>
          <text class="value" :class="getStatusClass(status.status)">
            {{ getStatusText(status.status) }}
          </text>
        </view>
        <view v-if="status.scope" class="status-item">
          <text class="label">作用域:</text>
          <text class="value small">{{ status.scope }}</text>
        </view>
      </view>

      <!-- Worker 状态 -->
      <view v-if="status.status === 'registered'" class="debug-section">
        <text class="section-title">🔄 Worker 状态</text>
        <view class="worker-states">
          <view class="worker-state" :class="{ active: status.installing }">
            <text class="state-icon">🔧</text>
            <text class="state-text">Installing</text>
          </view>
          <view class="worker-state" :class="{ active: status.waiting }">
            <text class="state-icon">⏳</text>
            <text class="state-text">Waiting</text>
          </view>
          <view class="worker-state" :class="{ active: status.active }">
            <text class="state-icon">🚀</text>
            <text class="state-text">Active</text>
          </view>
        </view>
      </view>

      <!-- 缓存信息 -->
      <view class="debug-section">
        <text class="section-title">💾 缓存信息</text>
        <view v-if="cacheInfo.length === 0" class="no-cache">
          <text>暂无缓存数据</text>
        </view>
        <view v-else class="cache-list">
          <view v-for="cache in cacheInfo" :key="cache.name" class="cache-item">
            <text class="cache-name">{{ cache.name }}</text>
            <text class="cache-size">{{ cache.size }} 项</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="debug-section">
        <text class="section-title">🛠️ 操作</text>
        <view class="action-buttons">
          <button class="action-btn primary" @click="checkUpdate">
            检查更新
          </button>
          <button 
            v-if="status.waiting" 
            class="action-btn warning" 
            @click="skipWaiting"
          >
            跳过等待
          </button>
          <button class="action-btn secondary" @click="refreshStatus">
            刷新状态
          </button>
          <button class="action-btn danger" @click="clearCaches">
            清除缓存
          </button>
          <button class="action-btn danger" @click="unregisterSW">
            卸载 SW
          </button>
        </view>
      </view>

      <!-- 日志 -->
      <view class="debug-section">
        <text class="section-title">📝 日志</text>
        <view class="log-container">
          <view v-for="(log, index) in logs" :key="index" class="log-item">
            <text class="log-time">{{ log.time }}</text>
            <text class="log-message">{{ log.message }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 调试按钮 -->
  <button v-if="!showDebug" class="debug-toggle" @click="showDebug = true">
    🐛
  </button>
</template>

<script>
import swManager from '../utils/sw-manager.js'

export default {
  name: 'SwDebug',
  data() {
    return {
      showDebug: false,
      status: {
        supported: false,
        status: 'not_supported'
      },
      cacheInfo: [],
      logs: []
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      // 获取初始状态
      await this.refreshStatus()
      
      // 监听 Service Worker 事件
      swManager.on('statechange', this.onStateChange)
      swManager.on('updatefound', this.onUpdateFound)
      swManager.on('activated', this.onActivated)
      
      this.addLog('调试面板初始化完成')
    },
    
    async refreshStatus() {
      this.status = swManager.getStatus()
      this.cacheInfo = await swManager.getCacheInfo()
      this.addLog('状态已刷新')
    },
    
    async checkUpdate() {
      this.addLog('检查更新中...')
      const result = await swManager.checkForUpdate()
      this.addLog(result ? '更新检查完成' : '更新检查失败')
    },
    
    async skipWaiting() {
      this.addLog('跳过等待中...')
      const result = await swManager.skipWaiting()
      this.addLog(result ? '跳过等待成功' : '跳过等待失败')
    },
    
    async clearCaches() {
      if (!confirm('确定要清除所有缓存吗？')) return
      
      this.addLog('清除缓存中...')
      const result = await swManager.clearCaches()
      this.addLog(result ? '缓存清除成功' : '缓存清除失败')
      
      if (result) {
        await this.refreshStatus()
      }
    },
    
    async unregisterSW() {
      if (!confirm('确定要卸载 Service Worker 吗？')) return
      
      this.addLog('卸载 Service Worker 中...')
      const result = await swManager.unregister()
      this.addLog(result ? 'Service Worker 卸载成功' : 'Service Worker 卸载失败')
      
      if (result) {
        await this.refreshStatus()
      }
    },
    
    onStateChange(data) {
      this.addLog(`状态变更: ${data.state}`)
      this.refreshStatus()
    },
    
    onUpdateFound() {
      this.addLog('发现 Service Worker 更新')
      this.refreshStatus()
    },
    
    onActivated() {
      this.addLog('Service Worker 已激活')
      this.refreshStatus()
    },
    
    addLog(message) {
      const time = new Date().toLocaleTimeString()
      this.logs.unshift({ time, message })
      
      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }
    },
    
    getStatusClass(status) {
      switch (status) {
        case 'registered': return 'success'
        case 'not_registered': return 'warning'
        case 'not_supported': return 'error'
        default: return ''
      }
    },
    
    getStatusText(status) {
      switch (status) {
        case 'registered': return '✅ 已注册'
        case 'not_registered': return '⚠️ 未注册'
        case 'not_supported': return '❌ 不支持'
        default: return status
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sw-debug-panel {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  width: 600rpx;
  max-height: 80vh;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 16rpx;
  z-index: 9999;
  overflow: hidden;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #333;
  border-bottom: 2rpx solid #555;
}

.debug-title {
  font-size: 28rpx;
  font-weight: bold;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 32rpx;
  padding: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.debug-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20rpx;
}

.debug-section {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  color: #4CAF50;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
  font-size: 24rpx;
}

.label {
  color: #ccc;
}

.value {
  &.success { color: #4CAF50; }
  &.warning { color: #FF9800; }
  &.error { color: #F44336; }
  &.small { font-size: 20rpx; }
}

.worker-states {
  display: flex;
  gap: 20rpx;
}

.worker-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
  border-radius: 8rpx;
  background: #333;
  opacity: 0.5;
  
  &.active {
    opacity: 1;
    background: #4CAF50;
  }
}

.state-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.state-text {
  font-size: 20rpx;
}

.cache-list {
  max-height: 200rpx;
  overflow-y: auto;
}

.cache-item {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #555;
  font-size: 22rpx;
}

.cache-name {
  color: #81C784;
}

.cache-size {
  color: #ccc;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.action-btn {
  padding: 15rpx 20rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: white;
  
  &.primary { background: #2196F3; }
  &.secondary { background: #757575; }
  &.warning { background: #FF9800; }
  &.danger { background: #F44336; }
}

.log-container {
  max-height: 300rpx;
  overflow-y: auto;
  background: #222;
  border-radius: 8rpx;
  padding: 15rpx;
}

.log-item {
  display: flex;
  gap: 15rpx;
  margin-bottom: 8rpx;
  font-size: 20rpx;
}

.log-time {
  color: #888;
  min-width: 120rpx;
}

.log-message {
  color: #ccc;
}

.debug-toggle {
  position: fixed;
  bottom: 100rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: #2196F3;
  border: none;
  font-size: 40rpx;
  z-index: 9998;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
}

.no-cache {
  text-align: center;
  color: #888;
  font-size: 22rpx;
  padding: 20rpx;
}
</style>
