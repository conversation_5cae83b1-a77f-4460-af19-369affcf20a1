{"name": "fake-mediastreamtrack", "version": "2.1.0", "description": "Fake W3C MediaStreamTrack implementation", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://inakibaz.me)", "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/ibc/fake-mediastreamtrack.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}, "files": ["LICENSE", "README.md", "npm-scripts.mjs", "lib"], "engines": {"node": ">=18"}, "scripts": {"prepare": "node npm-scripts.mjs prepare", "typescript:build": "node npm-scripts.mjs typescript:build", "typescript:watch": "node npm-scripts.mjs typescript:watch", "lint": "node npm-scripts.mjs lint", "format": "node npm-scripts.mjs format", "test": "node npm-scripts.mjs test", "coverage": "node npm-scripts.mjs coverage", "release:check": "node npm-scripts.mjs release:check", "release": "node npm-scripts.mjs release"}, "dependencies": {"uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.26.0", "@types/jest": "^29.5.14", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-prettier": "^5.4.0", "globals": "^16.0.0", "jest": "^29.7.0", "open-cli": "^8.0.0", "prettier": "^3.5.3", "ts-jest": "^29.3.2", "typescript": "^5.8.3", "typescript-eslint": "^8.31.1"}}