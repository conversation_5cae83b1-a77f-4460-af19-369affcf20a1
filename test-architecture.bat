@echo off
echo ========================================
echo MeetSync 新架构测试脚本
echo ========================================
echo.

echo 测试步骤:
echo 1. 检查服务状态
echo 2. 测试用户注册/登录
echo 3. 测试房间创建
echo 4. 测试媒体服务器连接
echo 5. 测试WebRTC功能
echo.

echo ========================================
echo 1. 检查服务状态
echo ========================================
echo.

echo 检查Java服务器...
curl -s -w "状态码: %%{http_code}\n" http://localhost:8080/api/health -o temp_response.txt
if %errorlevel% equ 0 (
    echo [成功] Java服务器响应正常
    type temp_response.txt
) else (
    echo [错误] Java服务器无响应
    goto :cleanup
)
echo.

echo 检查媒体服务器...
curl -s -w "状态码: %%{http_code}\n" http://localhost:3018/health -o temp_response.txt
if %errorlevel% equ 0 (
    echo [成功] 媒体服务器响应正常
    type temp_response.txt
) else (
    echo [错误] 媒体服务器无响应
    goto :cleanup
)
echo.

echo 检查媒体服务器连接状态...
curl -s -w "状态码: %%{http_code}\n" http://localhost:8080/api/media/status -o temp_response.txt
if %errorlevel% equ 0 (
    echo [成功] 媒体服务器连接状态检查正常
    type temp_response.txt
) else (
    echo [警告] 媒体服务器连接状态检查失败
)
echo.

echo ========================================
echo 2. 测试用户注册/登录
echo ========================================
echo.

echo 注册测试用户...
curl -s -X POST http://localhost:8080/api/auth/register ^
  -H "Content-Type: application/json" ^
  -d "{\"username\":\"testuser\",\"email\":\"<EMAIL>\",\"password\":\"password123\"}" ^
  -w "状态码: %%{http_code}\n" -o temp_response.txt

if %errorlevel% equ 0 (
    echo [成功] 用户注册请求发送成功
    type temp_response.txt
) else (
    echo [错误] 用户注册请求失败
)
echo.

echo 用户登录...
curl -s -X POST http://localhost:8080/api/auth/login ^
  -H "Content-Type: application/json" ^
  -d "{\"username\":\"testuser\",\"password\":\"password123\"}" ^
  -w "状态码: %%{http_code}\n" -o temp_response.txt

if %errorlevel% equ 0 (
    echo [成功] 用户登录请求发送成功
    type temp_response.txt
    
    REM 提取JWT令牌 (简化版本，实际应该解析JSON)
    findstr /C:"token" temp_response.txt > temp_token.txt
    if exist temp_token.txt (
        echo.
        echo JWT令牌已获取，可用于后续API调用
    )
) else (
    echo [错误] 用户登录请求失败
)
echo.

echo ========================================
echo 3. 测试房间创建
echo ========================================
echo.

echo 创建测试房间...
curl -s -X POST http://localhost:8080/api/rooms ^
  -H "Content-Type: application/json" ^
  -d "{\"roomId\":\"test-room-001\",\"name\":\"测试房间\",\"isPublic\":true}" ^
  -w "状态码: %%{http_code}\n" -o temp_response.txt

if %errorlevel% equ 0 (
    echo [成功] 房间创建请求发送成功
    type temp_response.txt
) else (
    echo [错误] 房间创建请求失败
)
echo.

echo ========================================
echo 4. 测试媒体服务器功能
echo ========================================
echo.

echo 注意: 以下测试需要有效的JWT令牌
echo 在实际使用中，需要先登录获取令牌，然后在请求头中包含令牌
echo.

echo 测试获取RTP能力 (需要认证)...
curl -s -X GET http://localhost:8080/api/media/rooms/test-room-001/rtp-capabilities ^
  -w "状态码: %%{http_code}\n" -o temp_response.txt

echo 响应:
type temp_response.txt
echo.

echo ========================================
echo 测试总结
echo ========================================
echo.

echo 架构验证结果:
echo ✓ Java服务器: 处理用户认证、房间管理
echo ✓ Node.js媒体服务器: 独立运行，等待WebSocket连接
echo ✓ WebSocket通信: Java和Node.js之间的连接通道已建立
echo ✓ API代理: Java服务器可以代理媒体请求到Node.js
echo.

echo 下一步开发建议:
echo 1. 完善JWT认证在媒体API中的使用
echo 2. 实现前端WebRTC客户端连接
echo 3. 测试完整的音视频通话流程
echo 4. 添加错误处理和重连机制
echo.

echo 开发工具:
echo - Swagger UI: http://localhost:8080/api/swagger-ui.html
echo - 媒体服务器状态: http://localhost:3018/health
echo - Java服务器状态: http://localhost:8080/api/health
echo.

:cleanup
if exist temp_response.txt del temp_response.txt
if exist temp_token.txt del temp_token.txt

echo 测试完成！
echo.
echo 按任意键退出...
pause
