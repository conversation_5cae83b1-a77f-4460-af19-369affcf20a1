# MeetSync 项目结构文档

## 📋 项目概述

MeetSync 是一个基于 WebRTC 的实时视频会议系统，采用微服务架构，包含前端应用、Java 后端服务、NodeJS 媒体服务器和 MySQL 数据库。

## 🏗️ 整体架构

```
MeetSync/
├── MeetSyncApp/              # 前端应用 (uni-app)
├── MeetSyncJavaServer/       # Java 后端服务 (Spring Boot)
├── MeetSyncServer/           # NodeJS 媒体服务器 (mediasoup)
├── mysql-dockerfile/         # MySQL 数据库配置
├── docker-compose.yml        # Docker 编排配置
└── 文档和配置文件
```

## 🎨 前端应用 (MeetSyncApp)

### 技术栈
- **框架**: uni-app (Vue.js)
- **WebRTC**: mediasoup-client
- **通信**: WebSocket + HTTP API
- **构建工具**: HBuilderX/CLI

### 目录结构
```
MeetSyncApp/
├── pages/                    # 页面文件
│   ├── auth/                 # 认证相关页面
│   │   └── login.vue         # 登录页面
│   ├── dashboard/            # 仪表板
│   │   └── index.vue         # 主仪表板页面
│   ├── room/                 # 房间相关页面
│   │   └── index.vue         # 视频会议房间页面
│   └── test/                 # 测试页面
│       └── java-backend.vue  # 后端测试页面
├── utils/                    # 工具类
│   ├── api.js                # API 接口封装
│   ├── auth.js               # 认证工具
│   ├── network.js            # 网络请求管理
│   ├── webrtc-java.js        # WebRTC 客户端 (Java后端版)
│   └── webrtc.js             # WebRTC 客户端 (原版)
├── components/               # 组件库
├── static/                   # 静态资源
├── config/                   # 配置文件
├── manifest.json             # 应用配置
├── pages.json                # 页面路由配置
└── package.json              # 依赖配置
```

### 核心功能
- **用户认证**: JWT 令牌认证
- **房间管理**: 创建、加入、离开房间
- **音视频通话**: WebRTC 实时通信
- **权限控制**: 基于角色的权限管理
- **响应式设计**: 支持移动端和桌面端

## ☕ Java 后端服务 (MeetSyncJavaServer)

### 技术栈
- **框架**: Spring Boot 3.2.0
- **数据库**: MySQL 5.7 + JPA/Hibernate
- **安全**: Spring Security + JWT
- **WebSocket**: Spring WebSocket
- **API 文档**: Swagger/OpenAPI 3
- **构建工具**: Maven

### 目录结构
```
MeetSyncJavaServer/src/main/java/org/meetsync/
├── config/                   # 配置类
│   ├── SecurityConfig.java   # 安全配置
│   ├── WebSocketConfig.java  # WebSocket 配置
│   ├── SwaggerConfig.java    # API 文档配置
│   └── JpaConfig.java        # 数据库配置
├── controller/               # 控制器层
│   ├── AuthController.java   # 认证控制器
│   ├── RoomController.java   # 房间控制器
│   ├── MediaController.java  # 媒体控制器
│   └── InviteCodeController.java # 邀请码控制器
├── entity/                   # 实体类
│   ├── User.java             # 用户实体
│   ├── Room.java             # 房间实体
│   ├── InviteCode.java       # 邀请码实体
│   └── UserRoomPermission.java # 用户房间权限实体
├── dto/                      # 数据传输对象
│   ├── UserLoginDto.java     # 用户登录 DTO
│   ├── UserRegistrationDto.java # 用户注册 DTO
│   └── RoomCreateDto.java    # 房间创建 DTO
├── service/                  # 服务层
│   ├── UserService.java      # 用户服务
│   ├── RoomService.java      # 房间服务
│   ├── MediaProxyService.java # 媒体代理服务
│   └── InviteCodeService.java # 邀请码服务
├── repository/               # 数据访问层
│   ├── UserRepository.java   # 用户仓库
│   ├── RoomRepository.java   # 房间仓库
│   └── InviteCodeRepository.java # 邀请码仓库
├── websocket/                # WebSocket 处理
│   ├── ClientWebSocketHandler.java # 客户端 WebSocket 处理器
│   └── MediaServerWebSocketHandler.java # 媒体服务器 WebSocket 处理器
├── security/                 # 安全相关
│   └── JwtAuthenticationFilter.java # JWT 认证过滤器
├── util/                     # 工具类
│   └── JwtUtil.java          # JWT 工具类
└── MeetSyncApplication.java  # 主启动类
```

### 核心功能
- **用户管理**: 注册、登录、权限管理
- **房间管理**: 房间 CRUD、权限控制
- **媒体代理**: WebRTC 信令转发
- **WebSocket 通信**: 实时消息推送
- **邀请码系统**: 基于邀请码的注册机制

## 🎬 NodeJS 媒体服务器 (MeetSyncServer)

### 技术栈
- **运行时**: Node.js
- **WebRTC**: mediasoup
- **Web 框架**: Express.js
- **WebSocket**: ws
- **数据库**: MySQL (连接池)

### 目录结构
```
MeetSyncServer/src/
├── media/                    # 媒体处理
│   └── MediaRoom.js          # 媒体房间管理
├── connectors/               # 连接器
│   └── JavaServerConnector.js # Java 服务器连接器
├── config.js                 # 配置文件
├── media-server.js           # 主服务器文件
├── app.js                    # Express 应用
├── Room.js                   # 房间管理 (旧版)
└── Peer.js                   # 对等连接管理 (旧版)
```

### 核心功能
- **WebRTC 媒体处理**: 基于 mediasoup 的音视频流管理
- **房间管理**: 媒体房间的创建和管理
- **传输管理**: WebRTC 传输的创建和维护
- **生产者/消费者管理**: 音视频流的生产和消费
- **与 Java 后端通信**: WebSocket 双向通信

## 🗄️ 数据库设计 (MySQL)

### 主要表结构
```sql
-- 用户表
users (id, username, password, email, role, created_at, updated_at)

-- 房间表
rooms (id, name, description, creator_id, password, created_at, updated_at)

-- 用户房间权限表
user_room_permissions (id, user_id, room_id, permission_type, granted_at)

-- 邀请码表
invite_codes (id, code, inviter_id, invite_time, is_used, use_time, user_id)

-- 房间活动日志表
room_activity_logs (id, room_id, user_id, action, details, created_at)
```

## 🐳 部署配置

### Docker 服务
```yaml
services:
  - meetsync-server    # NodeJS 媒体服务器
  - mysql             # MySQL 数据库
```

### 端口配置
- **前端应用**: 开发端口 (HBuilderX 自动分配)
- **Java 后端**: 8080 (HTTP API + WebSocket)
- **NodeJS 媒体服务器**: 3016 (HTTP + WebSocket)
- **MySQL 数据库**: 3306
- **WebRTC 媒体端口**: 10000-10100/UDP

## 🔄 服务间通信

### 通信架构
```
前端应用 (MeetSyncApp)
    ↕ HTTP API + WebSocket
Java 后端 (MeetSyncJavaServer:8080)
    ↕ WebSocket
NodeJS 媒体服务器 (MeetSyncServer:3016)
    ↕ MySQL 连接
数据库 (MySQL:3306)
```

### 通信协议
1. **前端 ↔ Java 后端**: 
   - HTTP REST API (认证、房间管理)
   - WebSocket (实时消息、状态同步)

2. **Java 后端 ↔ NodeJS 媒体服务器**:
   - WebSocket (媒体信令转发)

3. **前端 ↔ NodeJS 媒体服务器**:
   - 通过 Java 后端代理 (不直接通信)

## 🚀 启动流程

### 开发环境启动顺序
1. **启动 MySQL 数据库**
2. **启动 Java 后端服务** (端口 8080)
3. **启动 NodeJS 媒体服务器** (端口 3016)
4. **启动前端应用** (开发服务器)

### 生产环境部署
```bash
# 使用 Docker Compose 一键部署
docker-compose up -d
```

## 🔧 核心功能模块

### 1. 用户认证与权限管理
- **JWT 令牌认证**: 无状态认证机制
- **角色权限控制**: guest、user、premium、admin 四级权限
- **邀请码注册**: 基于邀请码的用户注册系统
- **会话管理**: WebSocket 连接状态管理

### 2. 房间管理系统
- **房间 CRUD**: 创建、查询、更新、删除房间
- **权限控制**: 基于用户角色的房间访问控制
- **实时人数统计**: WebSocket 连接去重统计
- **房间状态同步**: 实时同步房间参与者状态

### 3. WebRTC 音视频通信
- **媒体流管理**: 基于 mediasoup 的专业级媒体处理
- **传输管理**: WebRTC 传输的创建、连接、清理
- **生产者管理**: 音频、视频流的生产和分发
- **消费者管理**: 远程流的接收和播放
- **设备管理**: 摄像头、麦克风设备切换

### 4. 实时通信系统
- **WebSocket 双向通信**: 客户端与服务器实时消息传递
- **心跳机制**: 连接状态检测和自动重连
- **消息路由**: 房间内消息广播和点对点消息
- **状态同步**: 用户状态、媒体状态实时同步

## 📱 前端页面功能

### 登录页面 (`/pages/auth/login.vue`)
- 用户登录表单
- JWT 令牌获取和存储
- 登录状态验证
- 错误处理和用户反馈

### 仪表板页面 (`/pages/dashboard/index.vue`)
- 房间列表展示
- 创建新房间
- 加入现有房间
- 用户信息显示

### 视频会议房间页面 (`/pages/room/index.vue`)
- **视频容器**: 固定在页面顶部，显示主播视频流
- **悬浮控制**: 右侧悬浮音视频控制按钮
- **权限控制**: 仅 admin 角色显示视频控制按钮
- **实时状态**: 房间人数、连接状态实时更新
- **资源清理**: 退出时彻底清理 WebRTC 和 WebSocket 资源

## 🔌 API 接口设计

### 认证接口
```
POST /api/auth/login          # 用户登录
POST /api/auth/register       # 用户注册 (需邀请码)
POST /api/auth/logout         # 用户登出
GET  /api/auth/me             # 获取当前用户信息
```

### 房间接口
```
GET    /api/rooms             # 获取房间列表
POST   /api/rooms             # 创建新房间
GET    /api/rooms/{id}        # 获取房间详情
PUT    /api/rooms/{id}        # 更新房间信息
DELETE /api/rooms/{id}        # 删除房间
POST   /api/rooms/{id}/join   # 加入房间
POST   /api/rooms/{id}/leave  # 离开房间
```

### 媒体接口
```
POST /api/media/rooms/{roomId}/transports        # 创建传输
POST /api/media/rooms/{roomId}/producers         # 创建生产者
POST /api/media/rooms/{roomId}/consumers         # 创建消费者
GET  /api/media/rooms/{roomId}/producers         # 获取生产者列表
DELETE /api/media/producers/{producerId}         # 关闭生产者
```

## 🔄 WebSocket 消息协议

### 客户端 → 服务器
```json
{
  "type": "ping",                    // 心跳检测
  "type": "heartbeat",               // 心跳消息
  "type": "getRoomInfo",             // 获取房间信息
  "type": "leave_room",              // 离开房间
  "data": { ... }                    // 消息数据
}
```

### 服务器 → 客户端
```json
{
  "type": "pong",                    // 心跳响应
  "type": "heartbeat_ack",           // 心跳确认
  "type": "userJoined",              // 用户加入
  "type": "userLeft",                // 用户离开
  "type": "newProducers",            // 新生产者通知
  "type": "producerClosed",          // 生产者关闭通知
  "type": "roomInfo",                // 房间信息
  "data": { ... },                   // 消息数据
  "timestamp": 1234567890            // 时间戳
}
```

## 🛡️ 安全机制

### 认证安全
- **JWT 令牌**: 使用 HS256 算法签名
- **令牌过期**: 设置合理的过期时间
- **密码加密**: BCrypt 哈希加密存储
- **CORS 配置**: 跨域请求安全控制

### 权限控制
- **角色权限**: 基于角色的访问控制 (RBAC)
- **房间权限**: 细粒度的房间访问权限
- **API 权限**: 接口级别的权限验证
- **WebSocket 权限**: 连接和消息权限验证

### 数据安全
- **SQL 注入防护**: JPA 参数化查询
- **XSS 防护**: 输入数据验证和转义
- **HTTPS 支持**: 生产环境强制 HTTPS
- **敏感信息保护**: 密码、令牌等敏感信息保护

## 📊 性能优化

### 前端优化
- **组件懒加载**: 按需加载页面组件
- **资源压缩**: 静态资源压缩和缓存
- **WebRTC 优化**: 传输重建机制和错误恢复
- **内存管理**: 及时清理 WebRTC 资源

### 后端优化
- **连接池**: 数据库连接池优化
- **缓存机制**: 热点数据缓存
- **异步处理**: 非阻塞 I/O 操作
- **资源清理**: 定期清理僵尸连接和生产者

### 媒体优化
- **编解码优化**: 适应性码率控制
- **网络适应**: 根据网络状况调整质量
- **资源复用**: 传输和连接复用
- **错误恢复**: 自动重连和状态恢复

## 🐛 故障排查

### 常见问题
1. **WebRTC 连接失败**: 检查 STUN/TURN 服务器配置
2. **WebSocket 断开**: 检查心跳机制和网络状态
3. **权限错误**: 验证 JWT 令牌和用户权限
4. **媒体流问题**: 检查设备权限和浏览器兼容性

### 日志系统
- **前端日志**: 浏览器控制台和本地存储
- **Java 后端日志**: Logback 日志框架
- **NodeJS 日志**: Console 和文件日志
- **数据库日志**: MySQL 慢查询和错误日志

## 📈 监控指标

### 系统监控
- **连接数统计**: WebSocket 连接数量
- **房间统计**: 活跃房间和用户数量
- **资源使用**: CPU、内存、网络使用率
- **错误率**: 接口错误率和异常统计

### 业务监控
- **用户活跃度**: 登录用户和活跃用户统计
- **房间使用率**: 房间创建和使用频率
- **通话质量**: 音视频质量指标
- **性能指标**: 响应时间和吞吐量

---

## 📝 更新日志

### 最新更新 (2024-12-20)
- ✅ 修复房间人数统计 (按用户ID去重)
- ✅ 优化视频容器布局 (顶部吸附)
- ✅ 实现悬浮控制按钮 (权限控制)
- ✅ 完善退出房间资源清理
- ✅ 增强 WebRTC 连接稳定性
- ✅ 优化移动端响应式设计

### 技术债务
- [ ] 添加单元测试覆盖
- [ ] 实现集群部署支持
- [ ] 添加监控和告警系统
- [ ] 优化数据库查询性能
- [ ] 实现音视频录制功能
