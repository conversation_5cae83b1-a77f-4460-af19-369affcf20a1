<template>
  <view class="test-container">
    <view class="header">
      <text class="title">Java后端连接测试</text>
    </view>
    
    <view class="test-section">
      <view class="section-title">认证测试</view>
      <view class="test-item">
        <text class="test-label">当前用户:</text>
        <text class="test-value">{{ currentUser ? currentUser.username : '未登录' }}</text>
      </view>
      <view class="test-item">
        <text class="test-label">Token:</text>
        <text class="test-value">{{ authToken ? authToken.substring(0, 20) + '...' : '无' }}</text>
      </view>
    </view>
    
    <view class="test-section">
      <view class="section-title">API连接测试</view>
      <view class="test-item">
        <text class="test-label">基础URL:</text>
        <text class="test-value">{{ baseUrl }}</text>
      </view>
      <view class="test-item">
        <text class="test-label">媒体API URL:</text>
        <text class="test-value">{{ mediaApiUrl }}</text>
      </view>
      <button class="test-btn" @click="testHealthCheck" :disabled="testing">
        {{ testing ? '测试中...' : '健康检查' }}
      </button>
      <view v-if="healthResult" class="test-result" :class="{ success: healthResult.success }">
        {{ healthResult.message }}
      </view>
    </view>
    
    <view class="test-section">
      <view class="section-title">媒体服务器测试</view>
      <button class="test-btn" @click="testMediaServer" :disabled="testing">
        {{ testing ? '测试中...' : '测试媒体服务器' }}
      </button>
      <view v-if="mediaResult" class="test-result" :class="{ success: mediaResult.success }">
        {{ mediaResult.message }}
      </view>
    </view>
    
    <view class="test-section">
      <view class="section-title">WebRTC测试</view>
      <button class="test-btn" @click="testWebRTC" :disabled="testing">
        {{ testing ? '测试中...' : '测试WebRTC连接' }}
      </button>
      <view v-if="webrtcResult" class="test-result" :class="{ success: webrtcResult.success }">
        {{ webrtcResult.message }}
      </view>
    </view>
    
    <view class="actions">
      <button class="back-btn" @click="goBack">返回</button>
    </view>
  </view>
</template>

<script>
import authManager from '../../utils/auth.js'
import apiManager from '../../utils/api.js'
import config from '../../config/index.js'

export default {
  name: 'JavaBackendTest',
  data() {
    return {
      testing: false,
      currentUser: null,
      authToken: null,
      baseUrl: '',
      mediaApiUrl: '',
      healthResult: null,
      mediaResult: null,
      webrtcResult: null
    }
  },
  onLoad() {
    this.loadUserInfo()
    this.loadConfig()
  },
  methods: {
    loadUserInfo() {
      this.currentUser = authManager.getCurrentUser()
      this.authToken = authManager.getAuthToken()
    },
    
    loadConfig() {
      this.baseUrl = config.server.baseUrl
      this.mediaApiUrl = config.server.mediaApiUrl
    },
    
    async testHealthCheck() {
      this.testing = true
      this.healthResult = null

      try {
        // 测试基本API连接
        const response = await apiManager.verifyToken()
        this.healthResult = {
          success: true,
          message: `API连接成功: ${response.message || '服务正常'}`
        }
      } catch (error) {
        this.healthResult = {
          success: false,
          message: `API连接失败: ${error.message}`
        }
      } finally {
        this.testing = false
      }
    },
    
    async testMediaServer() {
      this.testing = true
      this.mediaResult = null

      try {
        // 先测试媒体服务器状态
        const statusResponse = await apiManager.getMediaServerStatus()
        console.log('媒体服务器状态响应:', statusResponse)

        // 然后测试创建媒体房间
        const testRoomId = 'test-room-' + Date.now()
        const createResponse = await apiManager.createMediaRoom(testRoomId)
        console.log('创建媒体房间响应:', createResponse)

        this.mediaResult = {
          success: true,
          message: `媒体服务器测试成功: 状态正常，房间创建成功`
        }
      } catch (error) {
        console.error('媒体服务器测试失败:', error)
        this.mediaResult = {
          success: false,
          message: `媒体服务器测试失败: ${error.message}`
        }
      } finally {
        this.testing = false
      }
    },
    
    async testWebRTC() {
      this.testing = true
      this.webrtcResult = null
      
      try {
        // 导入WebRTC Java客户端
        const { webrtcJavaClient } = await import('../../utils/webrtc-java.js')
        
        // 测试基本连接
        await webrtcJavaClient.checkMediaServerStatus()
        
        this.webrtcResult = {
          success: true,
          message: 'WebRTC连接测试成功'
        }
      } catch (error) {
        this.webrtcResult = {
          success: false,
          message: `WebRTC连接测试失败: ${error.message}`
        }
      } finally {
        this.testing = false
      }
    },
    
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.test-container {
  padding: 40rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 10rpx;
}

.test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.test-label {
  font-size: 28rpx;
  color: #666;
}

.test-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
  word-break: break-all;
}

.test-btn {
  width: 100%;
  padding: 20rpx;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.test-btn:disabled {
  background: #ccc;
}

.test-result {
  margin-top: 15rpx;
  padding: 15rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  background: #f8d7da;
  color: #721c24;
}

.test-result.success {
  background: #d4edda;
  color: #155724;
}

.actions {
  text-align: center;
  margin-top: 40rpx;
}

.back-btn {
  padding: 20rpx 40rpx;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}
</style>
