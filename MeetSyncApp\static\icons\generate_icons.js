// Node.js 脚本用于生成PNG图标
// 运行前需要安装: npm install canvas
// 运行命令: node generate_icons.js

const fs = require('fs');
const { createCanvas } = require('canvas');

function drawDashboardIcon(ctx, size, color, fillColor = null) {
    // 设置样式
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    // 计算缩放比例
    const scale = size / 24;
    
    // 保存当前状态
    ctx.save();
    ctx.scale(scale, scale);
    
    // 绘制主矩形
    ctx.beginPath();
    ctx.roundRect(3, 4, 18, 12, 1);
    
    if (fillColor) {
        ctx.fillStyle = fillColor;
        ctx.fill();
    }
    ctx.stroke();
    
    // 绘制垂直线
    ctx.beginPath();
    ctx.moveTo(7, 8);
    ctx.lineTo(7, 12);
    ctx.stroke();
    
    // 绘制水平线1
    ctx.beginPath();
    ctx.moveTo(11, 8);
    ctx.lineTo(17, 8);
    ctx.stroke();
    
    // 绘制水平线2
    ctx.beginPath();
    ctx.moveTo(11, 12);
    ctx.lineTo(17, 12);
    ctx.stroke();
    
    // 恢复状态
    ctx.restore();
}

function generateIcon(filename, size, color, fillColor = null) {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // 设置透明背景
    ctx.clearRect(0, 0, size, size);
    
    // 绘制图标
    drawDashboardIcon(ctx, size, color, fillColor);
    
    // 保存文件
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync(filename, buffer);
    console.log(`Generated: ${filename}`);
}

// 生成图标
try {
    // 普通状态图标 (暗金色)
    generateIcon('dashboard.png', 48, '#B8860B');
    
    // 激活状态图标 (金色，带填充)
    generateIcon('dashboard-active.png', 48, '#FFD700', '#FFD700');
    
    console.log('图标生成完成！');
    console.log('请将生成的 dashboard.png 和 dashboard-active.png 文件复制到 MeetSyncApp/static/icons/ 目录下');
    
} catch (error) {
    console.error('生成图标时出错:', error.message);
    console.log('请确保已安装 canvas 依赖: npm install canvas');
}
