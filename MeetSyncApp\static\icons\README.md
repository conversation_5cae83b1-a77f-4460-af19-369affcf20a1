# 图标文件说明

这个文件夹应该包含应用的各种尺寸图标文件。

## 需要的图标文件

### 应用图标
- `icon-72x72.png` - 72x72 像素
- `icon-96x96.png` - 96x96 像素  
- `icon-128x128.png` - 128x128 像素
- `icon-144x144.png` - 144x144 像素
- `icon-152x152.png` - 152x152 像素
- `icon-192x192.png` - 192x192 像素
- `icon-384x384.png` - 384x384 像素
- `icon-512x512.png` - 512x512 像素

### Favicon
- `favicon-16x16.png` - 16x16 像素
- `favicon-32x32.png` - 32x32 像素
- `apple-touch-icon.png` - 180x180 像素

### 快捷方式图标
- `shortcut-join.png` - 96x96 像素
- `shortcut-create.png` - 96x96 像素

### 通知图标
- `badge-72x72.png` - 72x72 像素
- `action-join.png` - 24x24 像素
- `action-dismiss.png` - 24x24 像素

### 标签栏图标
- `dashboard.png` - 标签栏图标（未选中）
- `dashboard-active.png` - 标签栏图标（选中）

## 图标设计建议

1. **主色调**: 使用 #007bff (蓝色) 作为主色
2. **图标主题**: 视频会议相关，建议使用摄像头、麦克风等元素
3. **背景**: 建议使用圆角矩形背景
4. **格式**: PNG 格式，支持透明背景
5. **风格**: 现代扁平化设计

## 生成工具推荐

- [PWA Builder](https://www.pwabuilder.com/) - 在线生成 PWA 图标
- [Favicon Generator](https://favicon.io/) - 生成 Favicon
- [App Icon Generator](https://appicon.co/) - 生成各种尺寸的应用图标

## 临时解决方案

在开发阶段，您可以：
1. 使用 emoji 📹 作为临时图标
2. 使用在线工具生成简单的图标
3. 从现有的图标库中选择合适的图标

## 注意事项

- 所有图标都应该是正方形
- 确保在不同背景下都清晰可见
- 遵循各平台的设计规范
- 定期检查图标在不同设备上的显示效果
