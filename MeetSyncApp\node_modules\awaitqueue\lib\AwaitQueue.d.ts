import type { AwaitQueueTask, AwaitQueueTaskDump } from './types';
export declare class AwaitQueue {
    private readonly pendingTasks;
    private nextTaskId;
    private stopping;
    constructor();
    get size(): number;
    push<T>(task: AwaitQueueTask<T>, name?: string): Promise<T>;
    stop(): void;
    remove(taskIdx: number): void;
    dump(): AwaitQueueTaskDump[];
    private execute;
}
//# sourceMappingURL=AwaitQueue.d.ts.map