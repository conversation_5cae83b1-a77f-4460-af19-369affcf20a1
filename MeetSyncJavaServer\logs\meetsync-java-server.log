2025-06-19 00:09:27.216 [https-jsse-nio-8080-exec-45] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7c714f98 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:09:27.246 [https-jsse-nio-8080-exec-45] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1e5dbfeb (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:09:27.248 [https-jsse-nio-8080-exec-45] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@38d9bcc5 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:09:27.249 [https-jsse-nio-8080-exec-45] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6b42bd04 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:09:27.249 [https-jsse-nio-8080-exec-45] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@54ccac6c (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:11:34.414 [https-jsse-nio-8080-exec-79] INFO  o.m.controller.MediaController - 清理用户生产者请求: roomId=UD94D1, userId=2
2025-06-19 00:11:34.488 [https-jsse-nio-8080-exec-36] INFO  o.meetsync.service.MediaProxyService - java.util.concurrent.CompletableFuture@5511bfc9[Not completed, 1 dependents]
2025-06-19 00:11:34.560 [https-jsse-nio-8080-exec-81] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"cd804139-ecec-4a63-a10b-ecb2e5080148","iceParameters":{"usernameFragment":"g9yl5l0yxcabljpo5ejyquhbatjsic69","password":"67s7fny0368xfxb2fwx4wsn14zxqs6k7","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10075,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10007,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"cd804139-ecec-4a63-a10b-ecb2e5080148"}
2025-06-19 00:11:34.561 [https-jsse-nio-8080-exec-81] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=cd804139-ecec-4a63-a10b-ecb2e5080148, iceParameters={usernameFragment=g9yl5l0yxcabljpo5ejyquhbatjsic69, password=67s7fny0368xfxb2fwx4wsn14zxqs6k7, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10075, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10007, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=cd804139-ecec-4a63-a10b-ecb2e5080148}
2025-06-19 00:11:34.607 [https-jsse-nio-8080-exec-91] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"b9880527-3d61-4dab-9830-f1dfea4f4302","iceParameters":{"usernameFragment":"6tdlky5wqv9jtg1i76yaprmp3ge4epm7","password":"wv33eo7e4xj1vyb45go4jdgnxys2sr09","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10024,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10046,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"b9880527-3d61-4dab-9830-f1dfea4f4302"}
2025-06-19 00:11:34.608 [https-jsse-nio-8080-exec-91] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=b9880527-3d61-4dab-9830-f1dfea4f4302, iceParameters={usernameFragment=6tdlky5wqv9jtg1i76yaprmp3ge4epm7, password=wv33eo7e4xj1vyb45go4jdgnxys2sr09, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10024, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10046, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=b9880527-3d61-4dab-9830-f1dfea4f4302}
2025-06-19 00:11:34.637 [https-jsse-nio-8080-exec-89] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接建立: 341f0724-00b4-b792-6861-2f6a3aacd428
2025-06-19 00:11:34.643 [https-jsse-nio-8080-exec-89] INFO  o.m.websocket.ClientWebSocketHandler - 用户 admin 加入房间 UD94D1 的WebSocket连接
2025-06-19 00:16:35.638 [https-jsse-nio-8080-exec-29] ERROR o.m.websocket.ClientWebSocketHandler - WebSocket传输错误: sessionId=341f0724-00b4-b792-6861-2f6a3aacd428
java.io.EOFException: null
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1296)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1247)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.read(NioEndpoint.java:1191)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:74)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-19 00:16:35.640 [https-jsse-nio-8080-exec-29] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接关闭: sessionId=341f0724-00b4-b792-6861-2f6a3aacd428, userId=2, username=admin, roomId=UD94D1, status=CloseStatus[code=1006, reason=null]
2025-06-19 00:16:35.640 [https-jsse-nio-8080-exec-29] INFO  o.m.websocket.ClientWebSocketHandler - 清理用户媒体生产者: userId=2, roomId=UD94D1
2025-06-19 00:21:07.324 [https-jsse-nio-8080-exec-29] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@454bbf77 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:21:07.326 [https-jsse-nio-8080-exec-29] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2ecfb50c (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:21:07.328 [https-jsse-nio-8080-exec-29] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@18a0909 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:21:07.330 [https-jsse-nio-8080-exec-29] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@19a2be48 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:21:07.332 [https-jsse-nio-8080-exec-29] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@64560e84 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:21:07.336 [https-jsse-nio-8080-exec-29] INFO  o.m.controller.MediaController - 连接传输请求: roomId=UD94D1, transportId=cd804139-ecec-4a63-a10b-ecb2e5080148, dtlsParameters={"dtlsParameters":{"role":"client","fingerprints":[{"algorithm":"sha-256","value":"58:CF:C8:D1:32:C9:58:A4:9D:4E:3C:E8:87:AD:99:4D:7D:1C:75:F4:DD:5C:6C:A6:1A:61:83:5A:C1:6B:AD:A4"}]}}
2025-06-19 00:21:07.362 [https-jsse-nio-8080-exec-106] INFO  o.m.controller.MediaController - 创建生产者请求: roomId=UD94D1, transportId=cd804139-ecec-4a63-a10b-ecb2e5080148, kind=video
2025-06-19 00:21:07.372 [https-jsse-nio-8080-exec-36] INFO  o.m.w.MediaServerWebSocketHandler - 收到媒体服务器通知: new_producers
2025-06-19 00:21:07.372 [https-jsse-nio-8080-exec-36] INFO  o.meetsync.service.MediaProxyService - 收到新生产者通知: 房间=UD94D1, 生产者数量=1
2025-06-19 00:21:07.373 [https-jsse-nio-8080-exec-106] INFO  o.m.controller.MediaController - 媒体服务器返回的生产者数据: {"producerId":"2623659d-e426-41f4-b9ee-905458420681","kind":"video"}
2025-06-19 00:21:07.373 [https-jsse-nio-8080-exec-106] INFO  o.m.controller.MediaController - 最终响应数据: {producer={producerId=2623659d-e426-41f4-b9ee-905458420681, kind=video}, message=生产者创建成功, userId=2, roomId=UD94D1}
2025-06-19 00:27:49.094 [https-jsse-nio-8080-exec-45] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@65fc1fa1 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:27:49.096 [https-jsse-nio-8080-exec-45] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7b4a18bf (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:27:49.097 [https-jsse-nio-8080-exec-45] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@66148ac7 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:27:49.098 [https-jsse-nio-8080-exec-45] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@639080b3 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:27:49.099 [https-jsse-nio-8080-exec-45] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@15637147 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:27:50.650 [https-jsse-nio-8080-exec-106] INFO  o.m.controller.MediaController - 清理用户生产者请求: roomId=UD94D1, userId=2
2025-06-19 00:27:50.654 [https-jsse-nio-8080-exec-66] INFO  o.m.w.MediaServerWebSocketHandler - 收到媒体服务器通知: producer_status_change
2025-06-19 00:27:50.654 [https-jsse-nio-8080-exec-66] INFO  o.meetsync.service.MediaProxyService - 生产者状态变化: 房间=UD94D1, 用户=2, 生产者=2623659d-e426-41f4-b9ee-905458420681, 状态=closed, 媒体类型=video
2025-06-19 00:27:50.656 [https-jsse-nio-8080-exec-66] ERROR o.m.w.MediaServerWebSocketHandler - 处理媒体服务器消息失败
java.lang.NullPointerException: null
	at java.base/java.util.Objects.requireNonNull(Objects.java:208)
	at java.base/java.util.ImmutableCollections$MapN.<init>(ImmutableCollections.java:1186)
	at java.base/java.util.Map.of(Map.java:1445)
	at org.meetsync.websocket.ClientWebSocketHandler.notifyProducerClosed(ClientWebSocketHandler.java:346)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:713)
	at org.meetsync.websocket.ClientWebSocketHandler$$SpringCGLIB$$0.notifyProducerClosed(<generated>)
	at org.meetsync.service.MediaProxyService.handleProducerStatusChange(MediaProxyService.java:239)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:713)
	at org.meetsync.service.MediaProxyService$$SpringCGLIB$$0.handleProducerStatusChange(<generated>)
	at org.meetsync.websocket.MediaServerWebSocketHandler.handleNotification(MediaServerWebSocketHandler.java:229)
	at org.meetsync.websocket.MediaServerWebSocketHandler.handleMessage(MediaServerWebSocketHandler.java:83)
	at org.springframework.web.socket.handler.WebSocketHandlerDecorator.handleMessage(WebSocketHandlerDecorator.java:75)
	at org.springframework.web.socket.handler.LoggingWebSocketHandlerDecorator.handleMessage(LoggingWebSocketHandlerDecorator.java:56)
	at org.springframework.web.socket.handler.ExceptionWebSocketHandlerDecorator.handleMessage(ExceptionWebSocketHandlerDecorator.java:58)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter.handleTextMessage(StandardWebSocketHandlerAdapter.java:113)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter$3.onMessage(StandardWebSocketHandlerAdapter.java:84)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter$3.onMessage(StandardWebSocketHandlerAdapter.java:81)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-19 00:27:50.725 [https-jsse-nio-8080-exec-79] INFO  o.meetsync.service.MediaProxyService - java.util.concurrent.CompletableFuture@3c00f2f9[Not completed, 1 dependents]
2025-06-19 00:27:50.799 [https-jsse-nio-8080-exec-36] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"d58c00fd-09cd-447d-87cc-22c0f7de7456","iceParameters":{"usernameFragment":"4er9rp856je2yh3ipr8mcuxevorrj6gv","password":"qolr97uvkpsgsj90vx20qg3ctu1pl86t","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10005,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10011,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"d58c00fd-09cd-447d-87cc-22c0f7de7456"}
2025-06-19 00:27:50.800 [https-jsse-nio-8080-exec-36] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=d58c00fd-09cd-447d-87cc-22c0f7de7456, iceParameters={usernameFragment=4er9rp856je2yh3ipr8mcuxevorrj6gv, password=qolr97uvkpsgsj90vx20qg3ctu1pl86t, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10005, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10011, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=d58c00fd-09cd-447d-87cc-22c0f7de7456}
2025-06-19 00:27:50.855 [https-jsse-nio-8080-exec-81] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"fb215b67-08c9-4cff-8628-42e83b0ec23b","iceParameters":{"usernameFragment":"jxr54py3uauh76tr8mo65mzor3rmgv2c","password":"57h3y38dk4sztw3hu42ofg5al1po6lgq","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10006,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10078,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"fb215b67-08c9-4cff-8628-42e83b0ec23b"}
2025-06-19 00:27:50.856 [https-jsse-nio-8080-exec-81] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=fb215b67-08c9-4cff-8628-42e83b0ec23b, iceParameters={usernameFragment=jxr54py3uauh76tr8mo65mzor3rmgv2c, password=57h3y38dk4sztw3hu42ofg5al1po6lgq, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10006, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10078, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=fb215b67-08c9-4cff-8628-42e83b0ec23b}
2025-06-19 00:27:50.890 [https-jsse-nio-8080-exec-45] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接建立: 3b18f163-469a-3a66-9414-ec672df82732
2025-06-19 00:27:50.897 [https-jsse-nio-8080-exec-45] INFO  o.m.websocket.ClientWebSocketHandler - 用户 admin 加入房间 UD94D1 的WebSocket连接
2025-06-19 00:27:55.754 [https-jsse-nio-8080-exec-91] INFO  o.m.controller.MediaController - 关闭生产者请求: roomId=UD94D1, userId=2, producerId=2623659d-e426-41f4-b9ee-905458420681
2025-06-19 00:27:55.760 [https-jsse-nio-8080-exec-91] ERROR o.m.controller.MediaController - 关闭生产者失败: 2623659d-e426-41f4-b9ee-905458420681
java.util.concurrent.ExecutionException: java.lang.RuntimeException: Producer 2623659d-e426-41f4-b9ee-905458420681 not found
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2073)
	at org.meetsync.controller.MediaController.closeProducer(MediaController.java:516)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doDelete(FrameworkServlet.java:936)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:596)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.meetsync.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.lang.RuntimeException: Producer 2623659d-e426-41f4-b9ee-905458420681 not found
	at org.meetsync.websocket.MediaServerWebSocketHandler.handleResponse(MediaServerWebSocketHandler.java:215)
	at org.meetsync.websocket.MediaServerWebSocketHandler.handleMessage(MediaServerWebSocketHandler.java:80)
	at org.springframework.web.socket.handler.WebSocketHandlerDecorator.handleMessage(WebSocketHandlerDecorator.java:75)
	at org.springframework.web.socket.handler.LoggingWebSocketHandlerDecorator.handleMessage(LoggingWebSocketHandlerDecorator.java:56)
	at org.springframework.web.socket.handler.ExceptionWebSocketHandlerDecorator.handleMessage(ExceptionWebSocketHandlerDecorator.java:58)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter.handleTextMessage(StandardWebSocketHandlerAdapter.java:113)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter$3.onMessage(StandardWebSocketHandlerAdapter.java:84)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter$3.onMessage(StandardWebSocketHandlerAdapter.java:81)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	... 7 common frames omitted
2025-06-19 00:27:57.255 [https-jsse-nio-8080-exec-45] INFO  o.m.controller.MediaController - 清理用户生产者请求: roomId=UD94D1, userId=2
2025-06-19 00:27:57.343 [https-jsse-nio-8080-exec-29] INFO  o.meetsync.service.MediaProxyService - java.util.concurrent.CompletableFuture@7e39e85a[Not completed, 1 dependents]
2025-06-19 00:27:57.408 [https-jsse-nio-8080-exec-81] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"f71a38ec-26d2-4ef8-a742-360329f0c3b2","iceParameters":{"usernameFragment":"nmqy1fe9bkmwq5un8vb3i4fegdblfmr8","password":"50g8ftkjl2kiw7oxmtpp4il86zh7t8pu","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10076,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10048,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"f71a38ec-26d2-4ef8-a742-360329f0c3b2"}
2025-06-19 00:27:57.408 [https-jsse-nio-8080-exec-81] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=f71a38ec-26d2-4ef8-a742-360329f0c3b2, iceParameters={usernameFragment=nmqy1fe9bkmwq5un8vb3i4fegdblfmr8, password=50g8ftkjl2kiw7oxmtpp4il86zh7t8pu, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10076, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10048, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=f71a38ec-26d2-4ef8-a742-360329f0c3b2}
2025-06-19 00:27:57.448 [https-jsse-nio-8080-exec-45] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"c8031f45-488c-462c-b72d-564a7e12ba04","iceParameters":{"usernameFragment":"6ihfipvoekm5qfw3nv2cfiolb1ra7cd0","password":"ck3x0392wm87g5upt1k21k679nl0xmf6","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10032,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10072,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"c8031f45-488c-462c-b72d-564a7e12ba04"}
2025-06-19 00:27:57.449 [https-jsse-nio-8080-exec-45] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=c8031f45-488c-462c-b72d-564a7e12ba04, iceParameters={usernameFragment=6ihfipvoekm5qfw3nv2cfiolb1ra7cd0, password=ck3x0392wm87g5upt1k21k679nl0xmf6, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10032, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10072, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=c8031f45-488c-462c-b72d-564a7e12ba04}
2025-06-19 00:27:57.477 [https-jsse-nio-8080-exec-79] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接建立: 9048eb75-afa6-26da-81bc-310c032846b8
2025-06-19 00:27:57.482 [https-jsse-nio-8080-exec-79] INFO  o.m.websocket.ClientWebSocketHandler - 用户 admin 加入房间 UD94D1 的WebSocket连接
2025-06-19 00:28:00.653 [https-jsse-nio-8080-exec-66] INFO  o.m.controller.MediaController - 关闭生产者请求: roomId=UD94D1, userId=2, producerId=2623659d-e426-41f4-b9ee-905458420681
2025-06-19 00:28:00.658 [https-jsse-nio-8080-exec-66] ERROR o.m.controller.MediaController - 关闭生产者失败: 2623659d-e426-41f4-b9ee-905458420681
java.util.concurrent.ExecutionException: java.lang.RuntimeException: Producer 2623659d-e426-41f4-b9ee-905458420681 not found
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2073)
	at org.meetsync.controller.MediaController.closeProducer(MediaController.java:516)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doDelete(FrameworkServlet.java:936)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:596)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.meetsync.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.lang.RuntimeException: Producer 2623659d-e426-41f4-b9ee-905458420681 not found
	at org.meetsync.websocket.MediaServerWebSocketHandler.handleResponse(MediaServerWebSocketHandler.java:215)
	at org.meetsync.websocket.MediaServerWebSocketHandler.handleMessage(MediaServerWebSocketHandler.java:80)
	at org.springframework.web.socket.handler.WebSocketHandlerDecorator.handleMessage(WebSocketHandlerDecorator.java:75)
	at org.springframework.web.socket.handler.LoggingWebSocketHandlerDecorator.handleMessage(LoggingWebSocketHandlerDecorator.java:56)
	at org.springframework.web.socket.handler.ExceptionWebSocketHandlerDecorator.handleMessage(ExceptionWebSocketHandlerDecorator.java:58)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter.handleTextMessage(StandardWebSocketHandlerAdapter.java:113)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter$3.onMessage(StandardWebSocketHandlerAdapter.java:84)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter$3.onMessage(StandardWebSocketHandlerAdapter.java:81)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	... 7 common frames omitted
2025-06-19 00:28:03.049 [https-jsse-nio-8080-exec-66] INFO  o.m.controller.MediaController - 关闭生产者请求: roomId=UD94D1, userId=2, producerId=2623659d-e426-41f4-b9ee-905458420681
2025-06-19 00:28:03.052 [https-jsse-nio-8080-exec-66] ERROR o.m.controller.MediaController - 关闭生产者失败: 2623659d-e426-41f4-b9ee-905458420681
java.util.concurrent.ExecutionException: java.lang.RuntimeException: Producer 2623659d-e426-41f4-b9ee-905458420681 not found
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2073)
	at org.meetsync.controller.MediaController.closeProducer(MediaController.java:516)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doDelete(FrameworkServlet.java:936)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:596)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.meetsync.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.lang.RuntimeException: Producer 2623659d-e426-41f4-b9ee-905458420681 not found
	at org.meetsync.websocket.MediaServerWebSocketHandler.handleResponse(MediaServerWebSocketHandler.java:215)
	at org.meetsync.websocket.MediaServerWebSocketHandler.handleMessage(MediaServerWebSocketHandler.java:80)
	at org.springframework.web.socket.handler.WebSocketHandlerDecorator.handleMessage(WebSocketHandlerDecorator.java:75)
	at org.springframework.web.socket.handler.LoggingWebSocketHandlerDecorator.handleMessage(LoggingWebSocketHandlerDecorator.java:56)
	at org.springframework.web.socket.handler.ExceptionWebSocketHandlerDecorator.handleMessage(ExceptionWebSocketHandlerDecorator.java:58)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter.handleTextMessage(StandardWebSocketHandlerAdapter.java:113)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter$3.onMessage(StandardWebSocketHandlerAdapter.java:84)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter$3.onMessage(StandardWebSocketHandlerAdapter.java:81)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	... 7 common frames omitted
2025-06-19 00:28:58.185 [https-jsse-nio-8080-exec-81] INFO  o.m.controller.MediaController - 清理用户生产者请求: roomId=UD94D1, userId=2
2025-06-19 00:28:58.271 [https-jsse-nio-8080-exec-79] INFO  o.meetsync.service.MediaProxyService - java.util.concurrent.CompletableFuture@5238e53f[Not completed, 1 dependents]
2025-06-19 00:28:58.335 [https-jsse-nio-8080-exec-36] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"8a40d07f-ed24-4382-985d-ed52e9d821d2","iceParameters":{"usernameFragment":"h0mb53pcpa7mmytfe5bwmc61y34rr36o","password":"rqwpb1f2bspc4w3lon5mwi0j8dm1dtke","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10043,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10049,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"8a40d07f-ed24-4382-985d-ed52e9d821d2"}
2025-06-19 00:28:58.336 [https-jsse-nio-8080-exec-36] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=8a40d07f-ed24-4382-985d-ed52e9d821d2, iceParameters={usernameFragment=h0mb53pcpa7mmytfe5bwmc61y34rr36o, password=rqwpb1f2bspc4w3lon5mwi0j8dm1dtke, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10043, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10049, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=8a40d07f-ed24-4382-985d-ed52e9d821d2}
2025-06-19 00:28:58.388 [https-jsse-nio-8080-exec-45] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"331b9936-8f73-42f6-b11f-17ac33907eba","iceParameters":{"usernameFragment":"z179gtuf2eitb6d38ikm9afc3vnew3qk","password":"5zxnavohs04rlgbtiwa8f0l6p5lsitcu","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10096,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10079,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"331b9936-8f73-42f6-b11f-17ac33907eba"}
2025-06-19 00:28:58.388 [https-jsse-nio-8080-exec-45] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=331b9936-8f73-42f6-b11f-17ac33907eba, iceParameters={usernameFragment=z179gtuf2eitb6d38ikm9afc3vnew3qk, password=5zxnavohs04rlgbtiwa8f0l6p5lsitcu, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10096, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10079, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=331b9936-8f73-42f6-b11f-17ac33907eba}
2025-06-19 00:28:58.419 [https-jsse-nio-8080-exec-66] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接建立: 2256c98a-6892-05c3-ec42-a6b9d66add6f
2025-06-19 00:28:58.423 [https-jsse-nio-8080-exec-66] INFO  o.m.websocket.ClientWebSocketHandler - 用户 admin 加入房间 UD94D1 的WebSocket连接
2025-06-19 00:29:02.165 [https-jsse-nio-8080-exec-81] INFO  o.m.controller.MediaController - 关闭生产者请求: roomId=UD94D1, userId=2, producerId=2623659d-e426-41f4-b9ee-905458420681
2025-06-19 00:29:02.171 [https-jsse-nio-8080-exec-81] ERROR o.m.controller.MediaController - 关闭生产者失败: 2623659d-e426-41f4-b9ee-905458420681
java.util.concurrent.ExecutionException: java.lang.RuntimeException: Producer 2623659d-e426-41f4-b9ee-905458420681 not found
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2073)
	at org.meetsync.controller.MediaController.closeProducer(MediaController.java:516)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doDelete(FrameworkServlet.java:936)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:596)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.meetsync.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:97)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.lang.RuntimeException: Producer 2623659d-e426-41f4-b9ee-905458420681 not found
	at org.meetsync.websocket.MediaServerWebSocketHandler.handleResponse(MediaServerWebSocketHandler.java:215)
	at org.meetsync.websocket.MediaServerWebSocketHandler.handleMessage(MediaServerWebSocketHandler.java:80)
	at org.springframework.web.socket.handler.WebSocketHandlerDecorator.handleMessage(WebSocketHandlerDecorator.java:75)
	at org.springframework.web.socket.handler.LoggingWebSocketHandlerDecorator.handleMessage(LoggingWebSocketHandlerDecorator.java:56)
	at org.springframework.web.socket.handler.ExceptionWebSocketHandlerDecorator.handleMessage(ExceptionWebSocketHandlerDecorator.java:58)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter.handleTextMessage(StandardWebSocketHandlerAdapter.java:113)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter$3.onMessage(StandardWebSocketHandlerAdapter.java:84)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter$3.onMessage(StandardWebSocketHandlerAdapter.java:81)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:390)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:130)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:484)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:284)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:130)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	... 7 common frames omitted
2025-06-19 00:33:59.419 [https-jsse-nio-8080-exec-89] ERROR o.m.websocket.ClientWebSocketHandler - WebSocket传输错误: sessionId=2256c98a-6892-05c3-ec42-a6b9d66add6f
java.io.EOFException: null
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1296)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1247)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.read(NioEndpoint.java:1191)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:74)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-19 00:33:59.421 [https-jsse-nio-8080-exec-89] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接关闭: sessionId=2256c98a-6892-05c3-ec42-a6b9d66add6f, userId=2, username=admin, roomId=UD94D1, status=CloseStatus[code=1006, reason=null]
2025-06-19 00:33:59.421 [https-jsse-nio-8080-exec-89] INFO  o.m.websocket.ClientWebSocketHandler - 清理用户媒体生产者: userId=2, roomId=UD94D1
2025-06-19 00:34:00.426 [https-jsse-nio-8080-exec-109] ERROR o.m.websocket.ClientWebSocketHandler - WebSocket传输错误: sessionId=3b18f163-469a-3a66-9414-ec672df82732
java.io.EOFException: null
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1296)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1247)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.read(NioEndpoint.java:1191)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:74)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-19 00:34:00.427 [https-jsse-nio-8080-exec-109] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接关闭: sessionId=3b18f163-469a-3a66-9414-ec672df82732, userId=2, username=admin, roomId=UD94D1, status=CloseStatus[code=1006, reason=null]
2025-06-19 00:34:00.427 [https-jsse-nio-8080-exec-79] ERROR o.m.websocket.ClientWebSocketHandler - WebSocket传输错误: sessionId=9048eb75-afa6-26da-81bc-310c032846b8
java.io.EOFException: null
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1296)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1247)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.read(NioEndpoint.java:1191)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:74)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-19 00:34:00.427 [https-jsse-nio-8080-exec-109] INFO  o.m.websocket.ClientWebSocketHandler - 清理用户媒体生产者: userId=2, roomId=UD94D1
2025-06-19 00:34:00.427 [https-jsse-nio-8080-exec-79] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接关闭: sessionId=9048eb75-afa6-26da-81bc-310c032846b8, userId=2, username=admin, roomId=UD94D1, status=CloseStatus[code=1006, reason=null]
2025-06-19 00:34:00.427 [https-jsse-nio-8080-exec-79] INFO  o.m.websocket.ClientWebSocketHandler - 清理用户媒体生产者: userId=2, roomId=UD94D1
2025-06-19 00:34:00.428 [https-jsse-nio-8080-exec-109] WARN  o.s.w.s.h.ExceptionWebSocketHandlerDecorator - Unhandled exception after connection closed for ExceptionWebSocketHandlerDecorator [delegate=LoggingWebSocketHandlerDecorator [delegate=org.meetsync.websocket.ClientWebSocketHandler@401f189e]]
java.lang.IllegalStateException: Message will not be sent because the WebSocket session has been closed
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.writeMessagePart(WsRemoteEndpointImplBase.java:450)
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.sendMessageBlock(WsRemoteEndpointImplBase.java:308)
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.sendMessageBlock(WsRemoteEndpointImplBase.java:250)
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.sendPartialString(WsRemoteEndpointImplBase.java:223)
	at org.apache.tomcat.websocket.WsRemoteEndpointBasic.sendText(WsRemoteEndpointBasic.java:48)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketSession.sendTextMessage(StandardWebSocketSession.java:215)
	at org.springframework.web.socket.adapter.AbstractWebSocketSession.sendMessage(AbstractWebSocketSession.java:108)
	at org.meetsync.websocket.ClientWebSocketHandler.lambda$broadcastToRoom$2(ClientWebSocketHandler.java:267)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.concurrent.ConcurrentHashMap$ValueSpliterator.forEachRemaining(ConcurrentHashMap.java:3612)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.meetsync.websocket.ClientWebSocketHandler.broadcastToRoom(ClientWebSocketHandler.java:264)
	at org.meetsync.websocket.ClientWebSocketHandler.afterConnectionClosed(ClientWebSocketHandler.java:197)
	at org.springframework.web.socket.handler.WebSocketHandlerDecorator.afterConnectionClosed(WebSocketHandlerDecorator.java:85)
	at org.springframework.web.socket.handler.LoggingWebSocketHandlerDecorator.afterConnectionClosed(LoggingWebSocketHandlerDecorator.java:72)
	at org.springframework.web.socket.handler.ExceptionWebSocketHandlerDecorator.afterConnectionClosed(ExceptionWebSocketHandlerDecorator.java:78)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter.onClose(StandardWebSocketHandlerAdapter.java:144)
	at org.apache.tomcat.websocket.WsSession.fireEndpointOnClose(WsSession.java:662)
	at org.apache.tomcat.websocket.WsSession.onClose(WsSession.java:629)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.close(WsHttpUpgradeHandler.java:256)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:158)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-19 00:43:07.469 [https-jsse-nio-8080-exec-66] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@44f62d73 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:43:07.470 [https-jsse-nio-8080-exec-66] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4f11ea33 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:43:07.472 [https-jsse-nio-8080-exec-66] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2e960d30 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:43:07.473 [https-jsse-nio-8080-exec-66] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5bfe2024 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:43:07.474 [https-jsse-nio-8080-exec-66] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4fce7984 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-19 00:43:07.590 [https-jsse-nio-8080-exec-36] INFO  o.m.controller.MediaController - 清理用户生产者请求: roomId=UD94D1, userId=2
2025-06-19 00:43:07.608 [https-jsse-nio-8080-exec-89] INFO  o.m.controller.MediaController - 清理用户生产者请求: roomId=UD94D1, userId=2
2025-06-19 00:43:07.638 [https-jsse-nio-8080-exec-109] INFO  o.meetsync.service.MediaProxyService - java.util.concurrent.CompletableFuture@472dfea1[Not completed, 1 dependents]
2025-06-19 00:43:07.647 [https-jsse-nio-8080-exec-91] INFO  o.meetsync.service.MediaProxyService - java.util.concurrent.CompletableFuture@2a8a3562[Not completed, 1 dependents]
2025-06-19 00:43:07.709 [https-jsse-nio-8080-exec-89] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"791b2559-6350-4de1-9fee-7a27d68de061","iceParameters":{"usernameFragment":"szp0ftekiw78pypd9sm9kqj1btot6r2a","password":"e9raxb0e8yt6zw3rr2ovm4tr9zqvgh0s","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10071,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10019,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"791b2559-6350-4de1-9fee-7a27d68de061"}
2025-06-19 00:43:07.710 [https-jsse-nio-8080-exec-89] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=791b2559-6350-4de1-9fee-7a27d68de061, iceParameters={usernameFragment=szp0ftekiw78pypd9sm9kqj1btot6r2a, password=e9raxb0e8yt6zw3rr2ovm4tr9zqvgh0s, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10071, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10019, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=791b2559-6350-4de1-9fee-7a27d68de061}
2025-06-19 00:43:07.736 [https-jsse-nio-8080-exec-45] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"177cc8d7-d1d8-41b7-a9cf-8cf92f407df4","iceParameters":{"usernameFragment":"94nhm0yg34tq5hl0e1c2nt7xsxarueqp","password":"ju5jom0mhyz8zfnmszuc5zpbuzoxc4sz","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10049,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10036,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"177cc8d7-d1d8-41b7-a9cf-8cf92f407df4"}
2025-06-19 00:43:07.737 [https-jsse-nio-8080-exec-45] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=177cc8d7-d1d8-41b7-a9cf-8cf92f407df4, iceParameters={usernameFragment=94nhm0yg34tq5hl0e1c2nt7xsxarueqp, password=ju5jom0mhyz8zfnmszuc5zpbuzoxc4sz, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10049, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10036, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=177cc8d7-d1d8-41b7-a9cf-8cf92f407df4}
2025-06-19 00:43:07.765 [https-jsse-nio-8080-exec-36] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"aa7ce4c7-b815-4617-866f-251c90344007","iceParameters":{"usernameFragment":"rxu82gnw9295t8mtgqbpbx052vqqip3y","password":"x7ge4ydero7z3u0j641ntnyj45gokzt8","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10085,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10022,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"aa7ce4c7-b815-4617-866f-251c90344007"}
2025-06-19 00:43:07.766 [https-jsse-nio-8080-exec-36] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=aa7ce4c7-b815-4617-866f-251c90344007, iceParameters={usernameFragment=rxu82gnw9295t8mtgqbpbx052vqqip3y, password=x7ge4ydero7z3u0j641ntnyj45gokzt8, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10085, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10022, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=aa7ce4c7-b815-4617-866f-251c90344007}
2025-06-19 00:43:07.790 [https-jsse-nio-8080-exec-109] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"f47460d4-a494-43b5-8cea-d5d2126081c9","iceParameters":{"usernameFragment":"aweoz4xu559syhoyv5vh096buq29b213","password":"g2o2pez0z3jysb6cxflvenk984o3x8jt","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10025,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10058,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"f47460d4-a494-43b5-8cea-d5d2126081c9"}
2025-06-19 00:43:07.791 [https-jsse-nio-8080-exec-109] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=f47460d4-a494-43b5-8cea-d5d2126081c9, iceParameters={usernameFragment=aweoz4xu559syhoyv5vh096buq29b213, password=g2o2pez0z3jysb6cxflvenk984o3x8jt, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10025, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10058, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=f47460d4-a494-43b5-8cea-d5d2126081c9}
2025-06-19 00:43:07.797 [https-jsse-nio-8080-exec-91] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接建立: e51141bb-8052-d19b-6b56-992e1256d33d
2025-06-19 00:43:07.804 [https-jsse-nio-8080-exec-91] INFO  o.m.websocket.ClientWebSocketHandler - 用户 admin 加入房间 UD94D1 的WebSocket连接
2025-06-19 00:43:07.820 [https-jsse-nio-8080-exec-66] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接建立: c25c521b-cdcb-d3c9-78da-12f5ef958c90
2025-06-19 00:43:07.825 [https-jsse-nio-8080-exec-66] INFO  o.m.websocket.ClientWebSocketHandler - 用户 admin 加入房间 UD94D1 的WebSocket连接
2025-06-19 00:44:04.299 [https-jsse-nio-8080-exec-109] INFO  o.m.controller.MediaController - 清理用户生产者请求: roomId=UD94D1, userId=2
2025-06-19 00:44:04.306 [https-jsse-nio-8080-exec-106] INFO  o.m.controller.MediaController - 清理用户生产者请求: roomId=UD94D1, userId=2
2025-06-19 00:44:04.363 [https-jsse-nio-8080-exec-66] INFO  o.meetsync.service.MediaProxyService - java.util.concurrent.CompletableFuture@51e92fca[Not completed, 1 dependents]
2025-06-19 00:44:04.391 [https-jsse-nio-8080-exec-79] INFO  o.meetsync.service.MediaProxyService - java.util.concurrent.CompletableFuture@391bf17d[Not completed, 1 dependents]
2025-06-19 00:44:04.428 [https-jsse-nio-8080-exec-29] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"81828d6c-9f9b-4265-8813-28efdd23b0e6","iceParameters":{"usernameFragment":"n9nmr8ts8o7147huwmszwxuh1h4q98y7","password":"pz1g5az62u17mp3ciou9u74vfrm03a89","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10086,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10056,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"81828d6c-9f9b-4265-8813-28efdd23b0e6"}
2025-06-19 00:44:04.429 [https-jsse-nio-8080-exec-29] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=81828d6c-9f9b-4265-8813-28efdd23b0e6, iceParameters={usernameFragment=n9nmr8ts8o7147huwmszwxuh1h4q98y7, password=pz1g5az62u17mp3ciou9u74vfrm03a89, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10086, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10056, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=81828d6c-9f9b-4265-8813-28efdd23b0e6}
2025-06-19 00:44:04.459 [https-jsse-nio-8080-exec-36] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"48c38f86-d08c-47d7-b5d9-c0a728b918b2","iceParameters":{"usernameFragment":"r6b41cogj187l6wygfspu9118ep8ujpz","password":"pgx23m6qx7exrwmo2pyfw3nfacjiohr1","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10058,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10023,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"48c38f86-d08c-47d7-b5d9-c0a728b918b2"}
2025-06-19 00:44:04.460 [https-jsse-nio-8080-exec-36] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=48c38f86-d08c-47d7-b5d9-c0a728b918b2, iceParameters={usernameFragment=r6b41cogj187l6wygfspu9118ep8ujpz, password=pgx23m6qx7exrwmo2pyfw3nfacjiohr1, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10058, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10023, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=48c38f86-d08c-47d7-b5d9-c0a728b918b2}
2025-06-19 00:44:04.483 [https-jsse-nio-8080-exec-66] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"4ad12760-4ac5-47c8-905a-4f03c5353bb2","iceParameters":{"usernameFragment":"7cdsgcvtc31uwikj41qdh5o61g6zlj9o","password":"livu2q9byt70e8adengr7r2o7i81fdzq","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10031,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10030,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"4ad12760-4ac5-47c8-905a-4f03c5353bb2"}
2025-06-19 00:44:04.484 [https-jsse-nio-8080-exec-66] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=4ad12760-4ac5-47c8-905a-4f03c5353bb2, iceParameters={usernameFragment=7cdsgcvtc31uwikj41qdh5o61g6zlj9o, password=livu2q9byt70e8adengr7r2o7i81fdzq, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10031, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10030, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=4ad12760-4ac5-47c8-905a-4f03c5353bb2}
2025-06-19 00:44:04.508 [https-jsse-nio-8080-exec-81] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"60408d48-c627-4fe9-85c7-f75162989c4e","iceParameters":{"usernameFragment":"1sc4z1wfd2ga8vot2lphc6pkeztzd49q","password":"7u2e9nu5n4egatif4j3jygr6gtrhn6rk","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10038,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10069,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"60408d48-c627-4fe9-85c7-f75162989c4e"}
2025-06-19 00:44:04.509 [https-jsse-nio-8080-exec-81] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=60408d48-c627-4fe9-85c7-f75162989c4e, iceParameters={usernameFragment=1sc4z1wfd2ga8vot2lphc6pkeztzd49q, password=7u2e9nu5n4egatif4j3jygr6gtrhn6rk, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10038, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10069, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=60408d48-c627-4fe9-85c7-f75162989c4e}
2025-06-19 00:44:04.515 [https-jsse-nio-8080-exec-89] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接建立: cfe71358-0c61-7309-d0c4-cfbe3ba2e09a
2025-06-19 00:44:04.521 [https-jsse-nio-8080-exec-89] INFO  o.m.websocket.ClientWebSocketHandler - 用户 admin 加入房间 UD94D1 的WebSocket连接
2025-06-19 00:44:04.543 [https-jsse-nio-8080-exec-109] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接建立: c0438f62-4bbf-b7ba-fefb-9e6d5c7b0ddc
2025-06-19 00:44:04.548 [https-jsse-nio-8080-exec-109] INFO  o.m.websocket.ClientWebSocketHandler - 用户 admin 加入房间 UD94D1 的WebSocket连接
2025-06-19 00:44:25.955 [https-jsse-nio-8080-exec-45] INFO  o.m.controller.MediaController - 清理用户生产者请求: roomId=UD94D1, userId=2
2025-06-19 00:44:25.955 [https-jsse-nio-8080-exec-91] INFO  o.m.controller.MediaController - 清理用户生产者请求: roomId=UD94D1, userId=2
2025-06-19 00:44:26.018 [https-jsse-nio-8080-exec-81] INFO  o.meetsync.service.MediaProxyService - java.util.concurrent.CompletableFuture@39351ef4[Not completed, 1 dependents]
2025-06-19 00:44:26.035 [https-jsse-nio-8080-exec-79] INFO  o.meetsync.service.MediaProxyService - java.util.concurrent.CompletableFuture@325f8c93[Not completed, 1 dependents]
2025-06-19 00:44:26.084 [https-jsse-nio-8080-exec-66] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"08bbc14c-f7c9-4ce8-8a6a-ce64820a0aad","iceParameters":{"usernameFragment":"txoq9023hrtwl632ayd7q5z0u46hevsn","password":"z3i476o5b536bctw44vhs3xeoawzs921","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10059,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10033,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"08bbc14c-f7c9-4ce8-8a6a-ce64820a0aad"}
2025-06-19 00:44:26.085 [https-jsse-nio-8080-exec-66] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=08bbc14c-f7c9-4ce8-8a6a-ce64820a0aad, iceParameters={usernameFragment=txoq9023hrtwl632ayd7q5z0u46hevsn, password=z3i476o5b536bctw44vhs3xeoawzs921, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10059, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10033, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=08bbc14c-f7c9-4ce8-8a6a-ce64820a0aad}
2025-06-19 00:44:26.104 [https-jsse-nio-8080-exec-109] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"bfc0e5fc-1dab-458a-a8c5-cbf2ffc5dc59","iceParameters":{"usernameFragment":"s25smf1n1gl6rmiyhz6h7gaomdyfsnvz","password":"6gf60d757yf8hc0sf903tu0ykn0l2x1x","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10027,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10024,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"bfc0e5fc-1dab-458a-a8c5-cbf2ffc5dc59"}
2025-06-19 00:44:26.104 [https-jsse-nio-8080-exec-109] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=bfc0e5fc-1dab-458a-a8c5-cbf2ffc5dc59, iceParameters={usernameFragment=s25smf1n1gl6rmiyhz6h7gaomdyfsnvz, password=6gf60d757yf8hc0sf903tu0ykn0l2x1x, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10027, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10024, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=bfc0e5fc-1dab-458a-a8c5-cbf2ffc5dc59}
2025-06-19 00:44:26.137 [https-jsse-nio-8080-exec-106] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"1c8cc7c8-dd9f-4864-a2e6-044f6e8386b3","iceParameters":{"usernameFragment":"5o2zxnlg1ufeyaxbqtjomwudencn7zmo","password":"f2g5vpfan4lg0ofd8ztuougj4x2hp9s6","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10007,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10057,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"1c8cc7c8-dd9f-4864-a2e6-044f6e8386b3"}
2025-06-19 00:44:26.138 [https-jsse-nio-8080-exec-106] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=1c8cc7c8-dd9f-4864-a2e6-044f6e8386b3, iceParameters={usernameFragment=5o2zxnlg1ufeyaxbqtjomwudencn7zmo, password=f2g5vpfan4lg0ofd8ztuougj4x2hp9s6, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10007, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10057, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=1c8cc7c8-dd9f-4864-a2e6-044f6e8386b3}
2025-06-19 00:44:26.158 [https-jsse-nio-8080-exec-81] INFO  o.m.controller.MediaController - NodeJS返回的传输数据: {"id":"11105311-d454-482f-aca1-adb0a3e33acb","iceParameters":{"usernameFragment":"ebxb9spi7y2uxfmdfgecudmrcn7rusvu","password":"8t7tbij0pwcorlk3h286wbg1m1l50i98","iceLite":true},"iceCandidates":[{"foundation":"udpcandidate","priority":1076302079,"ip":"*************","address":"*************","protocol":"udp","port":10093,"type":"host"},{"foundation":"tcpcandidate","priority":1076276479,"ip":"*************","address":"*************","protocol":"tcp","port":10059,"type":"host","tcpType":"passive"}],"dtlsParameters":{"fingerprints":[{"algorithm":"sha-256","value":"34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98"},{"algorithm":"sha-384","value":"C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82"},{"algorithm":"sha-224","value":"E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67"},{"algorithm":"sha-1","value":"BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31"},{"algorithm":"sha-512","value":"C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77"}],"role":"auto"},"transportId":"11105311-d454-482f-aca1-adb0a3e33acb"}
2025-06-19 00:44:26.158 [https-jsse-nio-8080-exec-81] INFO  o.m.controller.MediaController - 转换后的传输数据: {id=11105311-d454-482f-aca1-adb0a3e33acb, iceParameters={usernameFragment=ebxb9spi7y2uxfmdfgecudmrcn7rusvu, password=8t7tbij0pwcorlk3h286wbg1m1l50i98, iceLite=true}, iceCandidates=[{foundation=udpcandidate, priority=1076302079, ip=*************, address=*************, protocol=udp, port=10093, type=host}, {foundation=tcpcandidate, priority=1076276479, ip=*************, address=*************, protocol=tcp, port=10059, type=host, tcpType=passive}], dtlsParameters={fingerprints=[{algorithm=sha-256, value=34:FA:15:82:B8:73:64:88:25:78:B5:3A:18:24:5D:3F:C1:02:B8:E1:08:86:0C:C2:21:5A:97:5F:0A:BD:3C:98}, {algorithm=sha-384, value=C2:B9:43:DB:46:30:CA:46:5F:15:47:3A:87:7F:B3:D0:D0:76:40:92:1C:13:B2:68:17:F7:26:56:AE:93:1E:A8:D2:B1:44:D9:5D:8A:C8:10:89:CB:F2:FC:E4:90:9D:82}, {algorithm=sha-224, value=E9:09:87:2C:43:71:B4:F3:30:EC:D3:6D:15:78:A6:60:28:E1:C1:C9:27:E0:94:C0:33:F5:8A:67}, {algorithm=sha-1, value=BC:ED:CD:B6:DA:93:F2:B3:58:B3:A0:C1:16:FB:EC:F8:20:5B:70:31}, {algorithm=sha-512, value=C2:46:FB:23:57:A0:EC:45:A3:EA:A8:04:6B:01:1F:EE:A1:28:A7:BA:27:51:26:4A:AD:C4:75:0B:02:33:4F:23:EC:B0:6A:9C:F4:69:60:CF:27:3A:2A:E1:BF:55:6D:EB:96:A9:90:46:3C:9D:82:4C:EE:92:EA:7D:CE:82:46:77}], role=auto}, transportId=11105311-d454-482f-aca1-adb0a3e33acb}
2025-06-19 00:44:26.164 [https-jsse-nio-8080-exec-79] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接建立: 9cf30906-8b8c-24db-320b-952595b3bbf2
2025-06-19 00:44:26.168 [https-jsse-nio-8080-exec-79] INFO  o.m.websocket.ClientWebSocketHandler - 用户 admin 加入房间 UD94D1 的WebSocket连接
2025-06-19 00:44:26.188 [https-jsse-nio-8080-exec-36] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接建立: b376c0e3-a0dc-3d04-56bf-a62688486353
2025-06-19 00:44:26.194 [https-jsse-nio-8080-exec-36] INFO  o.m.websocket.ClientWebSocketHandler - 用户 admin 加入房间 UD94D1 的WebSocket连接
2025-06-19 00:49:17.554 [https-jsse-nio-8080-exec-109] ERROR o.m.w.MediaServerWebSocketHandler - 媒体服务器连接传输错误
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at org.apache.tomcat.util.net.SecureNioChannel.read(SecureNioChannel.java:601)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1294)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1247)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.read(NioEndpoint.java:1191)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:74)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-19 00:49:17.555 [https-jsse-nio-8080-exec-109] INFO  o.m.w.MediaServerWebSocketHandler - 媒体服务器连接关闭: 1006 - Connection reset
2025-06-19 00:49:17.556 [https-jsse-nio-8080-exec-109] INFO  o.meetsync.service.MediaProxyService - 媒体服务器连接状态更新: 已断开
2025-06-19 00:49:24.289 [https-jsse-nio-8080-exec-36] ERROR org.meetsync.util.JwtUtil - Error parsing JWT token: Invalid compact JWT string: Compact JWSs must contain exactly 2 period characters, and compact JWEs must contain exactly 4.  Found: 0
2025-06-19 00:49:24.289 [https-jsse-nio-8080-exec-36] ERROR org.meetsync.util.JwtUtil - Token validation failed: Invalid compact JWT string: Compact JWSs must contain exactly 2 period characters, and compact JWEs must contain exactly 4.  Found: 0
2025-06-19 00:49:24.294 [https-jsse-nio-8080-exec-36] INFO  o.m.w.MediaServerWebSocketHandler - 媒体服务器连接建立: ce7df3b4-5c90-e48c-6201-a8ebfb106690
2025-06-19 00:49:24.294 [https-jsse-nio-8080-exec-36] INFO  o.meetsync.service.MediaProxyService - 媒体服务器连接状态更新: 已连接
2025-06-19 00:49:24.294 [https-jsse-nio-8080-exec-36] INFO  o.m.w.MediaServerWebSocketHandler - ✅ 媒体服务器认证成功并连接
2025-06-19 00:49:27.190 [https-jsse-nio-8080-exec-66] ERROR o.m.websocket.ClientWebSocketHandler - WebSocket传输错误: sessionId=b376c0e3-a0dc-3d04-56bf-a62688486353
java.io.EOFException: null
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1296)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1247)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.read(NioEndpoint.java:1191)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:74)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-19 00:49:27.190 [https-jsse-nio-8080-exec-66] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接关闭: sessionId=b376c0e3-a0dc-3d04-56bf-a62688486353, userId=2, username=admin, roomId=UD94D1, status=CloseStatus[code=1006, reason=null]
2025-06-19 00:49:27.190 [https-jsse-nio-8080-exec-66] INFO  o.m.websocket.ClientWebSocketHandler - 清理用户媒体生产者: userId=2, roomId=UD94D1
2025-06-19 00:49:28.199 [https-jsse-nio-8080-exec-45] ERROR o.m.websocket.ClientWebSocketHandler - WebSocket传输错误: sessionId=c0438f62-4bbf-b7ba-fefb-9e6d5c7b0ddc
java.io.EOFException: null
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1296)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1247)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.read(NioEndpoint.java:1191)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:74)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-19 00:49:28.199 [https-jsse-nio-8080-exec-29] ERROR o.m.websocket.ClientWebSocketHandler - WebSocket传输错误: sessionId=c25c521b-cdcb-d3c9-78da-12f5ef958c90
java.io.EOFException: null
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1296)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1247)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.read(NioEndpoint.java:1191)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:74)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-19 00:49:28.199 [https-jsse-nio-8080-exec-109] ERROR o.m.websocket.ClientWebSocketHandler - WebSocket传输错误: sessionId=cfe71358-0c61-7309-d0c4-cfbe3ba2e09a
java.io.EOFException: null
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1296)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1247)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.read(NioEndpoint.java:1191)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:74)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-19 00:49:28.199 [https-jsse-nio-8080-exec-81] ERROR o.m.websocket.ClientWebSocketHandler - WebSocket传输错误: sessionId=e51141bb-8052-d19b-6b56-992e1256d33d
java.io.EOFException: null
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1296)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1247)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.read(NioEndpoint.java:1191)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:74)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-19 00:49:28.200 [https-jsse-nio-8080-exec-45] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接关闭: sessionId=c0438f62-4bbf-b7ba-fefb-9e6d5c7b0ddc, userId=2, username=admin, roomId=UD94D1, status=CloseStatus[code=1006, reason=null]
2025-06-19 00:49:28.200 [https-jsse-nio-8080-exec-29] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接关闭: sessionId=c25c521b-cdcb-d3c9-78da-12f5ef958c90, userId=2, username=admin, roomId=UD94D1, status=CloseStatus[code=1006, reason=null]
2025-06-19 00:49:28.200 [https-jsse-nio-8080-exec-45] INFO  o.m.websocket.ClientWebSocketHandler - 清理用户媒体生产者: userId=2, roomId=UD94D1
2025-06-19 00:49:28.200 [https-jsse-nio-8080-exec-109] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接关闭: sessionId=cfe71358-0c61-7309-d0c4-cfbe3ba2e09a, userId=2, username=admin, roomId=UD94D1, status=CloseStatus[code=1006, reason=null]
2025-06-19 00:49:28.199 [https-jsse-nio-8080-exec-106] ERROR o.m.websocket.ClientWebSocketHandler - WebSocket传输错误: sessionId=9cf30906-8b8c-24db-320b-952595b3bbf2
java.io.EOFException: null
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1296)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1247)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.read(NioEndpoint.java:1191)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:74)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-19 00:49:28.200 [https-jsse-nio-8080-exec-29] INFO  o.m.websocket.ClientWebSocketHandler - 清理用户媒体生产者: userId=2, roomId=UD94D1
2025-06-19 00:49:28.200 [https-jsse-nio-8080-exec-109] INFO  o.m.websocket.ClientWebSocketHandler - 清理用户媒体生产者: userId=2, roomId=UD94D1
2025-06-19 00:49:28.200 [https-jsse-nio-8080-exec-81] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接关闭: sessionId=e51141bb-8052-d19b-6b56-992e1256d33d, userId=2, username=admin, roomId=UD94D1, status=CloseStatus[code=1006, reason=null]
2025-06-19 00:49:28.200 [https-jsse-nio-8080-exec-81] INFO  o.m.websocket.ClientWebSocketHandler - 清理用户媒体生产者: userId=2, roomId=UD94D1
2025-06-19 00:49:28.200 [https-jsse-nio-8080-exec-106] INFO  o.m.websocket.ClientWebSocketHandler - WebSocket连接关闭: sessionId=9cf30906-8b8c-24db-320b-952595b3bbf2, userId=2, username=admin, roomId=UD94D1, status=CloseStatus[code=1006, reason=null]
2025-06-19 00:49:28.201 [https-jsse-nio-8080-exec-106] INFO  o.m.websocket.ClientWebSocketHandler - 清理用户媒体生产者: userId=2, roomId=UD94D1
2025-06-19 00:49:28.201 [https-jsse-nio-8080-exec-45] WARN  o.s.w.s.h.ExceptionWebSocketHandlerDecorator - Unhandled exception after connection closed for ExceptionWebSocketHandlerDecorator [delegate=LoggingWebSocketHandlerDecorator [delegate=org.meetsync.websocket.ClientWebSocketHandler@401f189e]]
java.lang.IllegalStateException: Message will not be sent because the WebSocket session has been closed
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.writeMessagePart(WsRemoteEndpointImplBase.java:450)
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.sendMessageBlock(WsRemoteEndpointImplBase.java:308)
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.sendMessageBlock(WsRemoteEndpointImplBase.java:250)
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.sendPartialString(WsRemoteEndpointImplBase.java:223)
	at org.apache.tomcat.websocket.WsRemoteEndpointBasic.sendText(WsRemoteEndpointBasic.java:48)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketSession.sendTextMessage(StandardWebSocketSession.java:215)
	at org.springframework.web.socket.adapter.AbstractWebSocketSession.sendMessage(AbstractWebSocketSession.java:108)
	at org.meetsync.websocket.ClientWebSocketHandler.lambda$broadcastToRoom$2(ClientWebSocketHandler.java:267)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.concurrent.ConcurrentHashMap$ValueSpliterator.forEachRemaining(ConcurrentHashMap.java:3612)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.meetsync.websocket.ClientWebSocketHandler.broadcastToRoom(ClientWebSocketHandler.java:264)
	at org.meetsync.websocket.ClientWebSocketHandler.afterConnectionClosed(ClientWebSocketHandler.java:197)
	at org.springframework.web.socket.handler.WebSocketHandlerDecorator.afterConnectionClosed(WebSocketHandlerDecorator.java:85)
	at org.springframework.web.socket.handler.LoggingWebSocketHandlerDecorator.afterConnectionClosed(LoggingWebSocketHandlerDecorator.java:72)
	at org.springframework.web.socket.handler.ExceptionWebSocketHandlerDecorator.afterConnectionClosed(ExceptionWebSocketHandlerDecorator.java:78)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter.onClose(StandardWebSocketHandlerAdapter.java:144)
	at org.apache.tomcat.websocket.WsSession.fireEndpointOnClose(WsSession.java:662)
	at org.apache.tomcat.websocket.WsSession.onClose(WsSession.java:629)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.close(WsHttpUpgradeHandler.java:256)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:158)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-19 00:49:28.203 [https-jsse-nio-8080-exec-29] WARN  o.s.w.s.h.ExceptionWebSocketHandlerDecorator - Unhandled exception after connection closed for ExceptionWebSocketHandlerDecorator [delegate=LoggingWebSocketHandlerDecorator [delegate=org.meetsync.websocket.ClientWebSocketHandler@401f189e]]
java.lang.IllegalStateException: The remote endpoint was in state [TEXT_PARTIAL_WRITING] which is an invalid state for called method
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase$StateMachine.checkState(WsRemoteEndpointImplBase.java:1245)
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase$StateMachine.textPartialStart(WsRemoteEndpointImplBase.java:1210)
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.sendPartialString(WsRemoteEndpointImplBase.java:222)
	at org.apache.tomcat.websocket.WsRemoteEndpointBasic.sendText(WsRemoteEndpointBasic.java:48)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketSession.sendTextMessage(StandardWebSocketSession.java:215)
	at org.springframework.web.socket.adapter.AbstractWebSocketSession.sendMessage(AbstractWebSocketSession.java:108)
	at org.meetsync.websocket.ClientWebSocketHandler.lambda$broadcastToRoom$2(ClientWebSocketHandler.java:267)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.concurrent.ConcurrentHashMap$ValueSpliterator.forEachRemaining(ConcurrentHashMap.java:3612)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.meetsync.websocket.ClientWebSocketHandler.broadcastToRoom(ClientWebSocketHandler.java:264)
	at org.meetsync.websocket.ClientWebSocketHandler.afterConnectionClosed(ClientWebSocketHandler.java:197)
	at org.springframework.web.socket.handler.WebSocketHandlerDecorator.afterConnectionClosed(WebSocketHandlerDecorator.java:85)
	at org.springframework.web.socket.handler.LoggingWebSocketHandlerDecorator.afterConnectionClosed(LoggingWebSocketHandlerDecorator.java:72)
	at org.springframework.web.socket.handler.ExceptionWebSocketHandlerDecorator.afterConnectionClosed(ExceptionWebSocketHandlerDecorator.java:78)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter.onClose(StandardWebSocketHandlerAdapter.java:144)
	at org.apache.tomcat.websocket.WsSession.fireEndpointOnClose(WsSession.java:662)
	at org.apache.tomcat.websocket.WsSession.onClose(WsSession.java:629)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.close(WsHttpUpgradeHandler.java:256)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:158)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-19 00:49:39.582 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-19 00:49:39.602 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-19 00:49:39.607 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-19 00:49:39.614 [SpringApplicationShutdownHook] ERROR o.m.w.MediaServerWebSocketHandler - 媒体服务器连接传输错误
java.io.IOException: java.nio.channels.ClosedChannelException
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.sendMessageBlock(WsRemoteEndpointImplBase.java:321)
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.sendMessageBlock(WsRemoteEndpointImplBase.java:257)
	at org.apache.tomcat.websocket.WsSession.sendCloseMessage(WsSession.java:718)
	at org.apache.tomcat.websocket.WsSession.doClose(WsSession.java:587)
	at org.apache.tomcat.websocket.WsSession.doClose(WsSession.java:554)
	at org.apache.tomcat.websocket.WsSession.close(WsSession.java:542)
	at org.apache.tomcat.websocket.WsWebSocketContainer.destroy(WsWebSocketContainer.java:1019)
	at org.apache.tomcat.websocket.server.WsContextListener.contextDestroyed(WsContextListener.java:46)
	at org.apache.catalina.core.StandardContext.listenerStop(StandardContext.java:4466)
	at org.apache.catalina.core.StandardContext.stopInternal(StandardContext.java:5093)
	at org.apache.catalina.util.LifecycleBase.stop(LifecycleBase.java:242)
	at org.apache.catalina.core.ContainerBase$StopChild.call(ContainerBase.java:1348)
	at org.apache.catalina.core.ContainerBase$StopChild.call(ContainerBase.java:1337)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.stopInternal(ContainerBase.java:931)
	at org.apache.catalina.util.LifecycleBase.stop(LifecycleBase.java:242)
	at org.apache.catalina.core.ContainerBase$StopChild.call(ContainerBase.java:1348)
	at org.apache.catalina.core.ContainerBase$StopChild.call(ContainerBase.java:1337)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145)
	at org.apache.catalina.core.ContainerBase.stopInternal(ContainerBase.java:931)
	at org.apache.catalina.util.LifecycleBase.stop(LifecycleBase.java:242)
	at org.apache.catalina.core.StandardService.stopInternal(StandardService.java:496)
	at org.apache.catalina.util.LifecycleBase.stop(LifecycleBase.java:242)
	at org.apache.catalina.core.StandardServer.stopInternal(StandardServer.java:969)
	at org.apache.catalina.util.LifecycleBase.stop(LifecycleBase.java:242)
	at org.apache.catalina.startup.Tomcat.stop(Tomcat.java:498)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.stopTomcat(TomcatWebServer.java:277)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.destroy(TomcatWebServer.java:350)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:177)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1037)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:145)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:114)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: java.nio.channels.ClosedChannelException: null
	at org.apache.tomcat.util.net.NioChannel$1.write(NioChannel.java:269)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper$NioOperationState.run(NioEndpoint.java:1635)
	at org.apache.tomcat.util.net.SocketWrapperBase$OperationState.start(SocketWrapperBase.java:1051)
	at org.apache.tomcat.util.net.SocketWrapperBase.vectoredOperation(SocketWrapperBase.java:1440)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:1366)
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:1337)
	at org.apache.tomcat.websocket.server.WsRemoteEndpointImplServer.doWrite(WsRemoteEndpointImplServer.java:165)
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.writeMessagePart(WsRemoteEndpointImplBase.java:516)
	at org.apache.tomcat.websocket.WsRemoteEndpointImplBase.sendMessageBlock(WsRemoteEndpointImplBase.java:308)
	... 39 common frames omitted
2025-06-19 00:49:39.617 [SpringApplicationShutdownHook] INFO  o.m.w.MediaServerWebSocketHandler - 媒体服务器连接关闭: 1001 - The web application is stopping
2025-06-19 00:49:39.656 [SpringApplicationShutdownHook] INFO  o.meetsync.service.MediaProxyService - 媒体服务器连接状态更新: 已断开
