# ===========================================
# MeetSync 服务端环境配置模板
# ===========================================
# 复制此文件为 .env 并修改相应配置

# MySQL 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_root_password_here
MYSQL_DATABASE=mediasoup_rooms
MYSQL_USER=meetsync
MYSQL_PASSWORD=your_secure_user_password_here

# 时区设置
TZ=Asia/Shanghai

# MeetSync 服务器配置
DB_HOST=mysql
DB_PORT=3306
DB_USER=meetsync
DB_PASSWORD=your_secure_user_password_here
DB_NAME=mediasoup_rooms
NODE_ENV=production

# JWT 密钥 (请使用强密钥，建议64位随机字符串)
JWT_SECRET=your_jwt_secret_key_here_please_use_strong_random_string

# 服务器配置
SERVER_PORT=3016
WEBRTC_PORT_RANGE_START=10000
WEBRTC_PORT_RANGE_END=10100

# 日志配置
LOG_LEVEL=info
LOG_FILE_ENABLED=true

# ===========================================
# 安全提示:
# 1. 请修改所有默认密码
# 2. JWT_SECRET 应使用强随机字符串
# 3. 生产环境请使用 HTTPS
# 4. 定期备份数据库
# ===========================================
