{"version": 3, "file": "Safari11.d.ts", "sourceRoot": "", "sources": ["../../src/handlers/Safari11.ts"], "names": [], "mappings": "AAMA,OAAO,EACN,KAAK,cAAc,EACnB,gBAAgB,EAChB,KAAK,iBAAiB,EACtB,KAAK,kBAAkB,EACvB,KAAK,iBAAiB,EACtB,KAAK,qBAAqB,EAC1B,KAAK,oBAAoB,EACzB,KAAK,6BAA6B,EAClC,KAAK,4BAA4B,EACjC,KAAK,gCAAgC,EACrC,KAAK,+BAA+B,EACpC,MAAM,oBAAoB,CAAC;AAE5B,OAAO,KAAK,EAAE,aAAa,EAAY,MAAM,cAAc,CAAC;AAC5D,OAAO,KAAK,EAAE,eAAe,EAAiB,MAAM,kBAAkB,CAAC;AACvE,OAAO,KAAK,EAAE,gBAAgB,EAAwB,MAAM,mBAAmB,CAAC;AAOhF,qBAAa,QAAS,SAAQ,gBAAgB;IAE7C,OAAO,CAAC,UAAU,CAAC,CAAkB;IAErC,OAAO,CAAC,UAAU,CAAC,CAAY;IAE/B,OAAO,CAAC,2BAA2B,CAAC,CAAmC;IAGvE,OAAO,CAAC,iCAAiC,CAAC,CAAmC;IAG7E,OAAO,CAAC,oBAAoB,CAAC,CAAW;IAExC,OAAO,CAAC,GAAG,CAAM;IAEjB,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAqB;IAEjD,OAAO,CAAC,QAAQ,CAAC,wBAAwB,CAC9B;IAEX,OAAO,CAAC,gBAAgB,CAAK;IAG7B,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAOtB;IAEd,OAAO,CAAC,2BAA2B,CAAS;IAE5C,OAAO,CAAC,qBAAqB,CAAK;IAElC,OAAO,CAAC,eAAe,CAAS;IAEhC;;OAEG;IACH,MAAM,CAAC,aAAa,IAAI,cAAc;;IAQtC,IAAI,IAAI,IAAI,MAAM,CAEjB;IAED,KAAK,IAAI,IAAI;IAaP,wBAAwB,IAAI,OAAO,CAAC,eAAe,CAAC;IAoCpD,yBAAyB,IAAI,OAAO,CAAC,gBAAgB,CAAC;IAQ5D,GAAG,CAAC,EACH,SAAS,EACT,aAAa,EACb,aAAa,EACb,cAAc,EACd,cAAc,EACd,UAAU,EACV,kBAAkB,EAClB,kBAAkB,EAClB,sBAAsB,EACtB,uBAAuB,GACvB,EAAE,iBAAiB,GAAG,IAAI;IAsGrB,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAU3D,UAAU,CAAC,aAAa,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC;IAiDvD,iBAAiB,IAAI,OAAO,CAAC,cAAc,CAAC;IAI5C,IAAI,CAAC,EACV,KAAK,EACL,SAAS,EACT,YAAY,EACZ,KAAK,GACL,EAAE,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;IA2I5C,WAAW,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAsD3C,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAK5C,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAI7C,YAAY,CACjB,OAAO,EAAE,MAAM,EACf,KAAK,EAAE,gBAAgB,GAAG,IAAI,GAC5B,OAAO,CAAC,IAAI,CAAC;IAkCV,kBAAkB,CACvB,OAAO,EAAE,MAAM,EACf,YAAY,EAAE,MAAM,GAClB,OAAO,CAAC,IAAI,CAAC;IA8BV,wBAAwB,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IA0BrE,cAAc,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC;IAYxD,eAAe,CAAC,EACrB,OAAO,EACP,iBAAiB,EACjB,cAAc,EACd,KAAK,EACL,QAAQ,GACR,EAAE,6BAA6B,GAAG,OAAO,CAAC,4BAA4B,CAAC;IAmElE,OAAO,CACZ,WAAW,EAAE,qBAAqB,EAAE,GAClC,OAAO,CAAC,oBAAoB,EAAE,CAAC;IA8F5B,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAqChD,gBAAgB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC;IAY1D,cAAc,CAEnB,QAAQ,EAAE,MAAM,EAAE,GAChB,OAAO,CAAC,IAAI,CAAC;IAIV,eAAe,CAEpB,QAAQ,EAAE,MAAM,EAAE,GAChB,OAAO,CAAC,IAAI,CAAC;IAIV,kBAAkB,CAAC,EACxB,oBAAoB,EACpB,KAAK,EACL,QAAQ,GACR,EAAE,gCAAgC,GAAG,OAAO,CAAC,+BAA+B,CAAC;YAyDhE,cAAc;IAgC5B,OAAO,CAAC,mBAAmB;IAQ3B,OAAO,CAAC,mBAAmB;CAO3B"}