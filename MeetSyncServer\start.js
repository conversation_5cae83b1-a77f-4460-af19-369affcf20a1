#!/usr/bin/env node

const { spawn } = require('child_process')
const path = require('path')
const fs = require('fs')

console.log('? MediaSoup Video Rooms - Enhanced Version')
console.log('==========================================')

// Check if .env file exists
const envPath = path.join(__dirname, '.env')
if (!fs.existsSync(envPath)) {
    console.log('??  .env file not found. Creating from template...')
    const envExample = path.join(__dirname, '.env.example')
    if (fs.existsSync(envExample)) {
        fs.copyFileSync(envExample, envPath)
        console.log('? .env file created. Please edit it with your database credentials.')
        console.log('? Edit .env file and run this script again.')
        process.exit(0)
    }
}

// Load environment variables
require('dotenv').config()

console.log('? Configuration:')
console.log(`   Database: ${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME}`)
console.log(`   Server: https://localhost:${process.env.PORT || 3016}`)
console.log('')

// Check if node_modules exists
if (!fs.existsSync(path.join(__dirname, 'node_modules'))) {
    console.log('? Installing dependencies...')
    const install = spawn('npm', ['install'], { stdio: 'inherit', shell: true })
    
    install.on('close', (code) => {
        if (code === 0) {
            console.log('? Dependencies installed successfully')
            initializeDatabase()
        } else {
            console.error('? Failed to install dependencies')
            process.exit(1)
        }
    })
} else {
    initializeDatabase()
}

function initializeDatabase() {
    console.log('??  Initializing database...')
    
    const initDb = spawn('npm', ['run', 'init-db'], { stdio: 'inherit', shell: true })
    
    initDb.on('close', (code) => {
        if (code === 0) {
            console.log('? Database initialized successfully')
            startServer()
        } else {
            console.log('??  Database initialization failed or already exists')
            console.log('? Attempting to start server anyway...')
            startServer()
        }
    })
}

function startServer() {
    console.log('? Starting server...')
    console.log('')
    console.log('? Access the application at:')
    console.log(`   ? https://localhost:${process.env.PORT || 3016}`)
    console.log('')
    console.log('? Default accounts:')
    console.log('   Admin: admin / admin123')
    console.log('   User: user1 / admin123')
    console.log('   Premium: premium1 / admin123')
    console.log('')
    console.log('? Press Ctrl+C to stop the server')
    console.log('==========================================')
    
    const server = spawn('npm', ['run', 'dev'], { stdio: 'inherit', shell: true })
    
    server.on('close', (code) => {
        console.log(`\n? Server stopped with code ${code}`)
    })
    
    // Handle Ctrl+C
    process.on('SIGINT', () => {
        console.log('\n? Shutting down server...')
        server.kill('SIGINT')
        process.exit(0)
    })
}
