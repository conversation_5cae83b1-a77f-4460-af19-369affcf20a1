/**
 * MeetSync Media Server
 * 专门处理 Mediasoup WebRTC 媒体流的服务
 * 与 Java 服务端通过 WebSocket 通信
 */

require('dotenv').config()
if (process.env.NODE_ENV === 'dev') {
  require('dotenv').config({ path: '.env.dev' })
  console.log('Using .env.dev');
} else if (process.env.NODE_ENV === 'prod') {
  require('dotenv').config({ path: '.env.prod' })
  console.log('Using .env.prod');
} else {
  require('dotenv').config()
  console.log('Using .env');
}


const mediasoup = require('mediasoup')
const WebSocket = require('ws')
const config = require('./config')
const MediaRoom = require('./media/MediaRoom')
const JavaServerConnector = require('./connectors/JavaServerConnector')

class MediaServer {
  constructor() {
    this.workers = []
    this.nextWorkerIdx = 0
    this.rooms = new Map() // roomId -> MediaRoom
    this.javaConnector = null
    this.isReady = false
  }

  async initialize() {
    try {
      console.log('🚀 启动 MeetSync Media Server...')
      
      // 1. 创建 Mediasoup Workers
      await this.createWorkers()
      
      // 2. 连接到 Java 服务端
      await this.connectToJavaServer()
      
      // 3. 启动健康检查服务
      this.startHealthCheck()
      
      this.isReady = true
      console.log('✅ Media Server 启动完成')
      
    } catch (error) {
      console.error('❌ Media Server 启动失败:', error)
      process.exit(1)
    }
  }

  async createWorkers() {
    const { numWorkers } = config.mediasoup
    console.log(`📦 创建 ${numWorkers} 个 Mediasoup Workers...`)

    for (let i = 0; i < numWorkers; i++) {
      const worker = await mediasoup.createWorker({
        logLevel: config.mediasoup.worker.logLevel,
        logTags: config.mediasoup.worker.logTags,
        rtcMinPort: config.mediasoup.worker.rtcMinPort,
        rtcMaxPort: config.mediasoup.worker.rtcMaxPort
      })

      worker.on('died', () => {
        console.error(`💀 Mediasoup worker died [pid:${worker.pid}], exiting...`)
        setTimeout(() => process.exit(1), 2000)
      })

      this.workers.push(worker)
      console.log(`✅ Worker ${i + 1} created [pid:${worker.pid}]`)
    }
  }

  async connectToJavaServer() {
    console.log('🔗 连接到 Java 服务端...')
    
    this.javaConnector = new JavaServerConnector(this)
    await this.javaConnector.connect()
    
    console.log('✅ 已连接到 Java 服务端')
  }

  startHealthCheck() {
    const express = require('express')
    const app = express()
    
    app.get('/health', (req, res) => {
      const health = {
        status: this.isReady ? 'healthy' : 'starting',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        workers: this.workers.length,
        rooms: this.rooms.size,
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
        }
      }
      
      res.json(health)
    })
    
    const port = config.healthCheckPort || 3018
    app.listen(port, () => {
      console.log(`🏥 健康检查服务启动: http://localhost:${port}/health`)
    })
  }

  getNextWorker() {
    const worker = this.workers[this.nextWorkerIdx]
    this.nextWorkerIdx = (this.nextWorkerIdx + 1) % this.workers.length
    return worker
  }

  // 房间管理
  async createRoom(roomId) {
    if (this.rooms.has(roomId)) {
      console.log(`📺 房间 ${roomId} 已存在，返回现有房间`)
      return this.rooms.get(roomId)
    }

    const worker = this.getNextWorker()
    const room = new MediaRoom(roomId, worker, this.javaConnector)
    await room.initialize()

    this.rooms.set(roomId, room)
    console.log(`📺 创建媒体房间: ${roomId}`)

    return room
  }

  getRoom(roomId) {
    return this.rooms.get(roomId)
  }

  async closeRoom(roomId) {
    const room = this.rooms.get(roomId)
    if (room) {
      await room.close()
      this.rooms.delete(roomId)
      console.log(`🗑️ 关闭媒体房间: ${roomId}`)
    }
  }

  // 处理来自 Java 服务端的请求
  async handleJavaRequest(type, _requestId, data) {
    try {
      let response = null

      switch (type) {
        case 'create_room':
          response = await this.handleCreateRoom(data)
          break
          
        case 'get_rtp_capabilities':
          response = await this.handleGetRtpCapabilities(data)
          break
          
        case 'create_transport':
          response = await this.handleCreateTransport(data)
          break
          
        case 'connect_transport':
          response = await this.handleConnectTransport(data)
          break
          
        case 'create_producer':
          response = await this.handleCreateProducer(data)
          break
          
        case 'create_consumer':
          response = await this.handleCreateConsumer(data)
          break
          
        case 'close_producer':
          response = await this.handleCloseProducer(data)
          break
          
        case 'close_consumer':
          response = await this.handleCloseConsumer(data)
          break

        case 'resume_consumer':
          response = await this.handleResumeConsumer(data)
          break

        case 'get_producers':
          response = await this.handleGetProducers(data)
          break

        case 'cleanup_user_producers':
          response = await this.handleCleanupUserProducers(data)
          break

        default:
          throw new Error(`Unknown request type: ${type}`)
      }

      return { success: true, data: response }
      
    } catch (error) {
      console.error(`处理请求失败 [${type}]:`, error)
      return { success: false, error: error.message }
    }
  }

  async handleCreateRoom(data) {
    const { roomId } = data
    const room = await this.createRoom(roomId)
    
    return {
      roomId,
      rtpCapabilities: room.router.rtpCapabilities
    }
  }

  async handleGetRtpCapabilities(data) {
    const { roomId } = data
    let room = this.getRoom(roomId)

    // 如果房间不存在，先创建房间
    if (!room) {
      console.log(`🏠 房间 ${roomId} 不存在，正在创建...`)
      room = await this.createRoom(roomId)
    }

    // 确保房间的路由器已初始化
    if (!room.router) {
      console.log(`🔄 房间 ${roomId} 路由器未初始化，正在初始化...`)
      await room.initialize()
    }

    return {
      rtpCapabilities: room.router.rtpCapabilities
    }
  }

  async handleCreateTransport(data) {
    const { roomId, userId, direction } = data
    let room = this.getRoom(roomId)

    // 如果房间不存在，先创建房间
    if (!room) {
      console.log(`🏠 房间 ${roomId} 不存在，正在创建...`)
      room = await this.createRoom(roomId)
    }

    const transport = await room.createTransport(userId, direction)

    console.log(`🚛 传输创建成功，ID: ${transport.id}`)
    console.log(`🔍 传输参数:`, {
      id: transport.id,
      iceParameters: !!transport.iceParameters,
      iceCandidates: !!transport.iceCandidates,
      dtlsParameters: !!transport.dtlsParameters
    })

    const result = {
      id: transport.id,
      iceParameters: transport.iceParameters,
      iceCandidates: transport.iceCandidates,
      dtlsParameters: transport.dtlsParameters,
      transportId: transport.id  // 保持向后兼容
    }

    console.log(`📤 返回传输数据:`, result)
    return result
  }

  async handleConnectTransport(data) {
    const { roomId, userId, transportId, dtlsParameters } = data
    console.log(`🔗 连接传输请求: roomId=${roomId}, userId=${userId}, transportId=${transportId}`)
    console.log(`🔍 原始 dtlsParameters:`, JSON.stringify(dtlsParameters, null, 2))

    const room = this.getRoom(roomId)

    if (!room) {
      throw new Error(`Room ${roomId} not found`)
    }

    // 修复数据结构问题 - Java端发送的是嵌套结构
    let actualDtlsParameters = dtlsParameters
    if (dtlsParameters.dtlsParameters) {
      console.log(`🔧 检测到嵌套的 dtlsParameters 结构，进行修复`)
      actualDtlsParameters = dtlsParameters.dtlsParameters
    }

    console.log(`🔍 实际使用的 dtlsParameters:`, JSON.stringify(actualDtlsParameters, null, 2))
    console.log(`🔍 fingerprints 类型:`, typeof actualDtlsParameters.fingerprints)
    console.log(`🔍 fingerprints 是否为数组:`, Array.isArray(actualDtlsParameters.fingerprints))

    await room.connectTransport(userId, transportId, actualDtlsParameters)

    return { connected: true }
  }

  async handleCreateProducer(data) {
    const { roomId, userId, transportId, kind, rtpParameters } = data
    console.log(`🎬 创建生产者请求: roomId=${roomId}, userId=${userId}, transportId=${transportId}, kind=${kind}`)

    const room = this.getRoom(roomId)

    if (!room) {
      throw new Error(`Room ${roomId} not found`)
    }

    const producer = await room.createProducer(userId, transportId, kind, rtpParameters)

    console.log(`🎬 生产者创建完成:`)
    console.log(`  - Producer对象:`, producer)
    console.log(`  - Producer.id:`, producer.id)
    console.log(`  - Producer.kind:`, producer.kind)
    console.log(`  - Producer属性:`, Object.getOwnPropertyNames(producer))

    const result = {
      producerId: producer.id,
      kind: producer.kind
    }

    console.log(`🎬 返回结果:`, result)
    return result
  }

  async handleCreateConsumer(data) {
    const { roomId, userId, transportId, producerId, rtpCapabilities } = data
    console.log(`🎭 创建消费者请求: roomId=${roomId}, userId=${userId}, transportId=${transportId}, producerId=${producerId}`)

    const room = this.getRoom(roomId)

    if (!room) {
      throw new Error(`Room ${roomId} not found`)
    }

    const consumer = await room.createConsumer(userId, transportId, producerId, rtpCapabilities)

    const result = {
      consumerId: consumer.id,
      kind: consumer.kind,
      rtpParameters: consumer.rtpParameters,
      producerPaused: consumer.producerPaused
    }

    console.log(`🎭 消费者创建成功:`, result)
    return result
  }

  async handleCloseProducer(data) {
    const { roomId, userId, producerId } = data
    console.log(`🗑️ 关闭生产者请求: roomId=${roomId}, userId=${userId}, producerId=${producerId}`)

    // 验证生产者ID
    if (!producerId || producerId === 'undefined' || producerId.trim() === '') {
      throw new Error(`Invalid producer ID: ${producerId}`)
    }

    const room = this.getRoom(roomId)

    if (!room) {
      throw new Error(`Room ${roomId} not found`)
    }

    await room.closeProducer(userId, producerId)

    console.log(`🗑️ 生产者关闭成功: ${producerId}`)
    return { closed: true }
  }

  async handleCloseConsumer(data) {
    const { roomId, userId, consumerId } = data
    const room = this.getRoom(roomId)

    if (!room) {
      throw new Error(`Room ${roomId} not found`)
    }

    await room.closeConsumer(userId, consumerId)

    return { closed: true }
  }

  async handleResumeConsumer(data) {
    const { roomId, userId, consumerId } = data
    const room = this.getRoom(roomId)

    if (!room) {
      throw new Error(`Room ${roomId} not found`)
    }

    await room.resumeConsumer(userId, consumerId)

    return { resumed: true }
  }

  async handleGetProducers(data) {
    const { roomId, userId } = data
    let room = this.getRoom(roomId)

    // 如果房间不存在，先创建房间
    if (!room) {
      console.log(`🏠 房间 ${roomId} 不存在，正在创建...`)
      room = await this.createRoom(roomId)
    }

    const producers = room.getProducersForUser(userId)

    return { producers }
  }

  async handleCleanupUserProducers(data) {
    const { roomId, userId } = data
    console.log(`🧹 清理用户生产者请求: roomId=${roomId}, userId=${userId}`)

    const room = this.getRoom(roomId)

    if (!room) {
      console.log(`🏠 房间 ${roomId} 不存在，无需清理`)
      return { cleaned: true, count: 0 }
    }

    const cleanedCount = await room.cleanupUserProducers(userId)

    console.log(`🧹 用户生产者清理完成: userId=${userId}, 清理数量=${cleanedCount}`)
    return { cleaned: true, count: cleanedCount }
  }
}

// 启动服务
const mediaServer = new MediaServer()
mediaServer.initialize().catch(error => {
  console.error('启动失败:', error)
  process.exit(1)
})

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('🛑 正在关闭 Media Server...')
  
  // 关闭所有房间
  for (const [, room] of mediaServer.rooms) {
    await room.close()
  }
  
  // 关闭所有 workers
  for (const worker of mediaServer.workers) {
    worker.close()
  }
  
  console.log('✅ Media Server 已关闭')
  process.exit(0)
})

module.exports = MediaServer
