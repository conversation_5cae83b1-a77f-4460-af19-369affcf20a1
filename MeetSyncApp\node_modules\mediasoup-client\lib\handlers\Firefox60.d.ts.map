{"version": 3, "file": "Firefox60.d.ts", "sourceRoot": "", "sources": ["../../src/handlers/Firefox60.ts"], "names": [], "mappings": "AAOA,OAAO,EACN,KAAK,cAAc,EACnB,gBAAgB,EAChB,KAAK,iBAAiB,EACtB,KAAK,kBAAkB,EACvB,KAAK,iBAAiB,EACtB,KAAK,qBAAqB,EAC1B,KAAK,oBAAoB,EACzB,KAAK,6BAA6B,EAClC,KAAK,4BAA4B,EACjC,KAAK,gCAAgC,EACrC,KAAK,+BAA+B,EACpC,MAAM,oBAAoB,CAAC;AAG5B,OAAO,KAAK,EAAE,aAAa,EAAY,MAAM,cAAc,CAAC;AAC5D,OAAO,KAAK,EACX,eAAe,EAGf,MAAM,kBAAkB,CAAC;AAC1B,OAAO,KAAK,EAAE,gBAAgB,EAAwB,MAAM,mBAAmB,CAAC;AAOhF,qBAAa,SAAU,SAAQ,gBAAgB;IAE9C,OAAO,CAAC,OAAO,CAAS;IAExB,OAAO,CAAC,UAAU,CAAC,CAAkB;IAErC,OAAO,CAAC,UAAU,CAAC,CAAY;IAE/B,OAAO,CAAC,2BAA2B,CAAC,CAAmC;IAGvE,OAAO,CAAC,iCAAiC,CAAC,CAAmC;IAE7E,OAAO,CAAC,GAAG,CAAM;IAEjB,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CACxB;IAEX,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAqB;IAEjD,OAAO,CAAC,2BAA2B,CAAS;IAE5C,OAAO,CAAC,qBAAqB,CAAK;IAElC,OAAO,CAAC,eAAe,CAAS;IAEhC;;OAEG;IACH,MAAM,CAAC,aAAa,IAAI,cAAc;;IAQtC,IAAI,IAAI,IAAI,MAAM,CAEjB;IAED,KAAK,IAAI,IAAI;IAmBP,wBAAwB,IAAI,OAAO,CAAC,eAAe,CAAC;IAuEpD,yBAAyB,IAAI,OAAO,CAAC,gBAAgB,CAAC;IAQ5D,GAAG,CAAC,EACH,SAAS,EACT,aAAa,EACb,aAAa,EACb,cAAc,EACd,cAAc,EACd,UAAU,EACV,kBAAkB,EAClB,kBAAkB,EAClB,sBAAsB,EACtB,uBAAuB,GACvB,EAAE,iBAAiB,GAAG,IAAI;IAmGrB,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAO3D,UAAU,CAAC,aAAa,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC;IAmDvD,iBAAiB,IAAI,OAAO,CAAC,cAAc,CAAC;IAM5C,IAAI,CAAC,EACV,KAAK,EACL,SAAS,EACT,YAAY,EACZ,KAAK,GACL,EAAE,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAiK5C,WAAW,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAqD3C,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAkC5C,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAkC7C,YAAY,CACjB,OAAO,EAAE,MAAM,EACf,KAAK,EAAE,gBAAgB,GAAG,IAAI,GAC5B,OAAO,CAAC,IAAI,CAAC;IAuBV,kBAAkB,CACvB,OAAO,EAAE,MAAM,EACf,YAAY,EAAE,MAAM,GAClB,OAAO,CAAC,IAAI,CAAC;IAuDV,wBAAwB,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IA+CrE,cAAc,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC;IAaxD,eAAe,CAAC,EACrB,OAAO,EACP,iBAAiB,EACjB,cAAc,EACd,KAAK,EACL,QAAQ,GACR,EAAE,6BAA6B,GAAG,OAAO,CAAC,4BAA4B,CAAC;IAiElE,OAAO,CACZ,WAAW,EAAE,qBAAqB,EAAE,GAClC,OAAO,CAAC,oBAAoB,EAAE,CAAC;IAyF5B,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IA0ChD,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAoCjD,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAoClD,gBAAgB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC;IAY1D,kBAAkB,CAAC,EACxB,oBAAoB,EACpB,KAAK,EACL,QAAQ,GACR,EAAE,gCAAgC,GAAG,OAAO,CAAC,+BAA+B,CAAC;YA2DhE,cAAc;IAgC5B,OAAO,CAAC,eAAe;IAMvB,OAAO,CAAC,mBAAmB;IAQ3B,OAAO,CAAC,mBAAmB;CAO3B"}