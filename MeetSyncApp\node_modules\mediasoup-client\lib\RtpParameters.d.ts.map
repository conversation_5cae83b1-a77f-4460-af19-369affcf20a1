{"version": 3, "file": "RtpParameters.d.ts", "sourceRoot": "", "sources": ["../src/RtpParameters.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,MAAM,MAAM,eAAe,GAAG;IAC7B;;OAEG;IACH,MAAM,CAAC,EAAE,kBAAkB,EAAE,CAAC;IAC9B;;OAEG;IACH,gBAAgB,CAAC,EAAE,kBAAkB,EAAE,CAAC;CACxC,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,SAAS,GAAG,OAAO,GAAG,OAAO,CAAC;AAE1C;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,MAAM,kBAAkB,GAAG;IAChC;;OAEG;IACH,IAAI,EAAE,SAAS,CAAC;IAChB;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAClB;;;OAGG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;;;OAIG;IACH,UAAU,CAAC,EAAE,GAAG,CAAC;IACjB;;OAEG;IACH,YAAY,CAAC,EAAE,YAAY,EAAE,CAAC;CAC9B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,2BAA2B,GACpC,UAAU,GACV,UAAU,GACV,UAAU,GACV,UAAU,CAAC;AAEd;;;;;;;;;GASG;AACH,MAAM,MAAM,kBAAkB,GAAG;IAChC;;OAEG;IACH,IAAI,EAAE,SAAS,CAAC;IAIhB,GAAG,EAAE,qBAAqB,CAAC;IAC3B;;;OAGG;IACH,WAAW,EAAE,MAAM,CAAC;IACpB;;;OAGG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B;;;;OAIG;IACH,SAAS,CAAC,EAAE,2BAA2B,CAAC;CACxC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,MAAM,MAAM,aAAa,GAAG;IAC3B;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,MAAM,EAAE,kBAAkB,EAAE,CAAC;IAC7B;;OAEG;IACH,gBAAgB,CAAC,EAAE,4BAA4B,EAAE,CAAC;IAClD;;OAEG;IACH,SAAS,CAAC,EAAE,qBAAqB,EAAE,CAAC;IACpC;;OAEG;IACH,IAAI,CAAC,EAAE,cAAc,CAAC;CACtB,CAAC;AAEF;;;;GAIG;AACH,MAAM,MAAM,kBAAkB,GAAG;IAChC;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;IACpB;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAClB;;;OAGG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;;;OAIG;IACH,UAAU,CAAC,EAAE,GAAG,CAAC;IACjB;;OAEG;IACH,YAAY,CAAC,EAAE,YAAY,EAAE,CAAC;CAC9B,CAAC;AAEF;;;;;GAKG;AACH,MAAM,MAAM,YAAY,GAAG;IAC1B;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,qBAAqB,GAAG;IACnC;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb;;;OAGG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B;;;OAGG;IACH,GAAG,CAAC,EAAE;QAAE,IAAI,EAAE,MAAM,CAAA;KAAE,CAAC;IACvB;;;;;OAKG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;IACd;;;OAGG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,QAAQ,CAAC,EAAE,UAAU,GAAG,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;IAClD,eAAe,CAAC,EAAE,UAAU,GAAG,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;CACzD,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,qBAAqB,GAC9B,qCAAqC,GACrC,+CAA+C,GAC/C,wDAAwD,GACxD,8DAA8D,GAC9D,yCAAyC,GACzC,6CAA6C,GAC7C,4BAA4B,GAC5B,oCAAoC,GACpC,2EAA2E,GAC3E,4DAA4D,GAC5D,+DAA+D,GAC/D,4DAA4D,CAAC;AAEhE;;;;;;;GAOG;AACH,MAAM,MAAM,4BAA4B,GAAG;IAC1C;;OAEG;IACH,GAAG,EAAE,qBAAqB,CAAC;IAC3B;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IACX;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;OAEG;IACH,UAAU,CAAC,EAAE,GAAG,CAAC;CACjB,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAM,MAAM,cAAc,GAAG;IAC5B;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;;OAGG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB;;OAEG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;CACd,CAAC"}