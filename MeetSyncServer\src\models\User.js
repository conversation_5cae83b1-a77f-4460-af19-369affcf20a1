const bcrypt = require('bcryptjs')
const db = require('../database/connection')

class User {
  constructor(data) {
    this.id = data.id
    this.username = data.username
    this.email = data.email
    this.role = data.role
    this.avatar_url = data.avatar_url
    this.is_active = data.is_active
    this.created_at = data.created_at
    this.updated_at = data.updated_at
    this.last_login = data.last_login
  }

  // Create new user
  static async create(userData) {
    const { username, email, password, role = 'user' } = userData
    
    // Hash password
    const saltRounds = 10
    const password_hash = await bcrypt.hash(password, saltRounds)
    
    const sql = `
      INSERT INTO users (username, email, password_hash, role) 
      VALUES (?, ?, ?, ?)
    `
    
    try {
      const result = await db.query(sql, [username, email, password_hash, role])
      return await User.findById(result.insertId)
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('Username or email already exists')
      }
      throw error
    }
  }

  // Find user by ID
  static async findById(id) {
    const sql = 'SELECT * FROM users WHERE id = ? AND is_active = TRUE'
    const users = await db.query(sql, [id])
    return users.length > 0 ? new User(users[0]) : null
  }

  // Find user by username
  static async findByUsername(username) {
    const sql = 'SELECT * FROM users WHERE username = ? AND is_active = TRUE'
    const users = await db.query(sql, [username])
    return users.length > 0 ? new User(users[0]) : null
  }

  // Find user by email
  static async findByEmail(email) {
    const sql = 'SELECT * FROM users WHERE email = ? AND is_active = TRUE'
    const users = await db.query(sql, [email])
    return users.length > 0 ? new User(users[0]) : null
  }

  // Authenticate user
  static async authenticate(username, password) {
    const sql = 'SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = TRUE'
    const users = await db.query(sql, [username, username])
    
    if (users.length === 0) {
      return null
    }
    
    const user = users[0]
    const isValidPassword = await bcrypt.compare(password, user.password_hash)
    
    if (!isValidPassword) {
      return null
    }
    
    // Update last login
    await db.query('UPDATE users SET last_login = NOW() WHERE id = ?', [user.id])
    
    return new User(user)
  }

  // Update user
  async update(updateData) {
    const allowedFields = ['username', 'email', 'role', 'avatar_url']
    const updates = []
    const values = []
    
    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key)) {
        updates.push(`${key} = ?`)
        values.push(value)
      }
    }
    
    if (updates.length === 0) {
      return this
    }
    
    values.push(this.id)
    const sql = `UPDATE users SET ${updates.join(', ')}, updated_at = NOW() WHERE id = ?`
    
    await db.query(sql, values)
    return await User.findById(this.id)
  }

  // Change password
  async changePassword(newPassword) {
    const saltRounds = 10
    const password_hash = await bcrypt.hash(newPassword, saltRounds)
    
    const sql = 'UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?'
    await db.query(sql, [password_hash, this.id])
  }

  // Deactivate user
  async deactivate() {
    const sql = 'UPDATE users SET is_active = FALSE, updated_at = NOW() WHERE id = ?'
    await db.query(sql, [this.id])
  }

  // Get user permissions for a room
  async getRoomPermissions(roomId) {
    const sql = `
      SELECT urp.permission, urp.expires_at
      FROM user_room_permissions urp
      JOIN rooms r ON urp.room_id = r.id
      WHERE urp.user_id = ? AND r.room_id = ? 
      AND (urp.expires_at IS NULL OR urp.expires_at > NOW())
    `
    
    const permissions = await db.query(sql, [this.id, roomId])
    return permissions.length > 0 ? permissions[0] : null
  }

  // Check if user can access room
  async canAccessRoom(roomId) {
    const sql = `
      SELECT r.*, urp.permission
      FROM rooms r
      LEFT JOIN user_room_permissions urp ON r.id = urp.room_id AND urp.user_id = ?
      WHERE r.room_id = ? AND r.is_active = TRUE
    `
    
    const rooms = await db.query(sql, [this.id, roomId])
    if (rooms.length === 0) {
      return { canAccess: false, reason: 'Room not found' }
    }
    
    const room = rooms[0]
    
    // Check if user meets minimum role requirement
    const roleHierarchy = { guest: 0, user: 1, premium: 2, admin: 3 }
    const userRoleLevel = roleHierarchy[this.role] || 0
    const requiredRoleLevel = roleHierarchy[room.required_role] || 0
    
    if (userRoleLevel < requiredRoleLevel) {
      return { canAccess: false, reason: 'Insufficient role' }
    }
    
    return { 
      canAccess: true, 
      room: room,
      permission: room.permission || this.getDefaultPermission()
    }
  }

  // Get default permission based on user role
  getDefaultPermission() {
    const defaultPermissions = {
      guest: 'view_only',
      user: 'audio_only',
      premium: 'video_audio',
      admin: 'full_access'
    }
    
    return defaultPermissions[this.role] || 'view_only'
  }

  // Convert to JSON (exclude sensitive data)
  toJSON() {
    return {
      id: this.id,
      username: this.username,
      email: this.email,
      role: this.role,
      avatar_url: this.avatar_url,
      created_at: this.created_at,
      last_login: this.last_login
    }
  }
}

module.exports = User
