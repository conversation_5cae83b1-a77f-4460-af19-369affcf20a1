# MeetSync 服务端 Docker Compose 配置
# 包含 MySQL 数据库和 MeetSync 服务器
version: '3.8'

services:
  # MeetSync 服务器
  meetsync-server:
    build:
      context: ./MeetSyncServer
      dockerfile: Dockerfile
    container_name: meetsync-server
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    ports:
      - "3016:3016"                    # HTTP/WebSocket 端口
      - "10000-10100:10000-10100/udp"  # WebRTC 媒体端口范围
    environment:
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}
      DB_USER: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_NAME: ${DB_NAME}
      NODE_ENV: ${NODE_ENV}
      JWT_SECRET: ${JWT_SECRET}
    volumes:
      - server-logs:/app/logs
    networks:
      - meetsync-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3016/health", "||", "exit", "1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

# 数据卷定义
volumes:
  mysql-data:
    driver: local
    name: meetsync-mysql-data
  mysql-logs:
    driver: local
    name: meetsync-mysql-logs
  server-logs:
    driver: local
    name: meetsync-server-logs

# 网络定义
networks:
  meetsync-network:
    driver: bridge
    name: meetsync-network

