<!DOCTYPE html>
<html>
<head>
    <title>生成Base64 PNG图标</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #1A1A1A; 
            color: #E5E5E5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
        }
        .icon-section { 
            margin: 20px 0; 
            padding: 20px; 
            background: #2D2D2D; 
            border-radius: 8px; 
        }
        canvas { 
            border: 1px solid #444; 
            margin: 10px 0; 
            background: white;
        }
        button { 
            background: #FFD700; 
            color: #1A1A1A; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 5px; 
        }
        button:hover { 
            background: #DAA520; 
        }
        .base64-output {
            background: #1A1A1A;
            border: 1px solid #444;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .download-btn {
            background: #32CD32;
            color: white;
        }
        .download-btn:hover {
            background: #228B22;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 MeetSync PNG图标生成器</h1>
        <p>这个工具将生成符合黑金主题的PNG图标文件</p>
        
        <div class="icon-section">
            <h3>📊 普通状态图标 (dashboard.png)</h3>
            <canvas id="normalCanvas" width="48" height="48"></canvas>
            <br>
            <button onclick="generateNormalIcon()">🎨 生成普通图标</button>
            <button id="downloadNormal" class="download-btn" onclick="downloadNormalIcon()" style="display:none;">💾 下载 dashboard.png</button>
            <div id="normalBase64" class="base64-output" style="display:none;"></div>
        </div>
        
        <div class="icon-section">
            <h3>⭐ 激活状态图标 (dashboard-active.png)</h3>
            <canvas id="activeCanvas" width="48" height="48"></canvas>
            <br>
            <button onclick="generateActiveIcon()">🎨 生成激活图标</button>
            <button id="downloadActive" class="download-btn" onclick="downloadActiveIcon()" style="display:none;">💾 下载 dashboard-active.png</button>
            <div id="activeBase64" class="base64-output" style="display:none;"></div>
        </div>
        
        <div class="icon-section">
            <h3>📋 使用说明</h3>
            <ol>
                <li>点击"生成普通图标"和"生成激活图标"按钮</li>
                <li>点击对应的"下载"按钮保存PNG文件</li>
                <li>将下载的文件重命名并放到 <code>MeetSyncApp/static/icons/</code> 目录下</li>
                <li>确保文件名为 <code>dashboard.png</code> 和 <code>dashboard-active.png</code></li>
            </ol>
            
            <h4>🎨 设计规范</h4>
            <ul>
                <li><strong>普通状态颜色</strong>: #B8860B (暗金色)</li>
                <li><strong>激活状态颜色</strong>: #FFD700 (金色)</li>
                <li><strong>尺寸</strong>: 48x48px</li>
                <li><strong>背景</strong>: 透明</li>
            </ul>
        </div>
    </div>

    <script>
        let normalIconData = null;
        let activeIconData = null;

        function drawDashboardIcon(ctx, color, filled = false) {
            const size = 48;
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 设置样式
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            // 如果是激活状态，设置填充
            if (filled) {
                ctx.fillStyle = color;
            }
            
            // 计算缩放和偏移，使图标居中
            const scale = 1.8;
            const offsetX = 12;
            const offsetY = 10;
            
            // 绘制主矩形 (仪表板外框)
            ctx.beginPath();
            ctx.roundRect(offsetX, offsetY, 24, 16, 2);
            if (filled) {
                ctx.globalAlpha = 0.3;
                ctx.fill();
                ctx.globalAlpha = 1;
            }
            ctx.stroke();
            
            // 绘制垂直分割线
            ctx.beginPath();
            ctx.moveTo(offsetX + 6, offsetY + 4);
            ctx.lineTo(offsetX + 6, offsetY + 12);
            ctx.stroke();
            
            // 绘制上方水平线
            ctx.beginPath();
            ctx.moveTo(offsetX + 10, offsetY + 4);
            ctx.lineTo(offsetX + 20, offsetY + 4);
            ctx.stroke();
            
            // 绘制下方水平线
            ctx.beginPath();
            ctx.moveTo(offsetX + 10, offsetY + 8);
            ctx.lineTo(offsetX + 20, offsetY + 8);
            ctx.stroke();
            
            // 绘制底部水平线
            ctx.beginPath();
            ctx.moveTo(offsetX + 10, offsetY + 12);
            ctx.lineTo(offsetX + 20, offsetY + 12);
            ctx.stroke();
        }
        
        function generateNormalIcon() {
            const canvas = document.getElementById('normalCanvas');
            const ctx = canvas.getContext('2d');
            
            drawDashboardIcon(ctx, '#B8860B', false);
            
            // 获取图像数据
            normalIconData = canvas.toDataURL('image/png');
            
            // 显示下载按钮
            document.getElementById('downloadNormal').style.display = 'inline-block';
            
            // 显示Base64数据（可选）
            const base64Div = document.getElementById('normalBase64');
            base64Div.textContent = normalIconData;
            base64Div.style.display = 'block';
        }
        
        function generateActiveIcon() {
            const canvas = document.getElementById('activeCanvas');
            const ctx = canvas.getContext('2d');
            
            drawDashboardIcon(ctx, '#FFD700', true);
            
            // 获取图像数据
            activeIconData = canvas.toDataURL('image/png');
            
            // 显示下载按钮
            document.getElementById('downloadActive').style.display = 'inline-block';
            
            // 显示Base64数据（可选）
            const base64Div = document.getElementById('activeBase64');
            base64Div.textContent = activeIconData;
            base64Div.style.display = 'block';
        }
        
        function downloadNormalIcon() {
            if (normalIconData) {
                const link = document.createElement('a');
                link.download = 'dashboard.png';
                link.href = normalIconData;
                link.click();
            }
        }
        
        function downloadActiveIcon() {
            if (activeIconData) {
                const link = document.createElement('a');
                link.download = 'dashboard-active.png';
                link.href = activeIconData;
                link.click();
            }
        }
        
        // 页面加载时自动生成预览
        window.onload = function() {
            generateNormalIcon();
            generateActiveIcon();
        };
    </script>
</body>
</html>
