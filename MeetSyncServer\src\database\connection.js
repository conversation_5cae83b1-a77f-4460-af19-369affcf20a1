const mysql = require('mysql2/promise')
const config = require('../config')

class Database {
  constructor() {
    this.pool = null
  }

  async connect() {
    try {
      this.pool = mysql.createPool({
        host: config.database.host,
        port: config.database.port,
        user: config.database.user,
        password: config.database.password,
        database: config.database.database,
        connectionLimit: config.database.connectionLimit,
        acquireTimeout: config.database.acquireTimeout,
        timeout: config.database.timeout,
        reconnect: true,
        charset: 'utf8mb4'
      })

      // Test connection
      const connection = await this.pool.getConnection()
      console.log('? Database connected successfully')
      connection.release()
      
      return this.pool
    } catch (error) {
      console.error('? Database connection failed:', error.message)
      throw error
    }
  }

  async query(sql, params = []) {
    try {
      const processedParams = params.map(param => {
        // 如果参数应该是数字但是字符串，转换它
        if (typeof param === 'string' && !isNaN(param) && param.trim() !== '') {
          if (param.includes('.')) {
            return parseFloat(param);
          } else {
            return parseInt(param, 10);
          }
        }
        return param;
      });

      const [rows] = await this.pool.execute(sql, processedParams)
      return rows
    } catch (error) {
      console.error('Database query error:', error.message)
      throw error
    }
  }

  async close() {
    if (this.pool) {
      await this.pool.end()
      console.log('Database connection closed')
    }
  }
}

module.exports = new Database()
