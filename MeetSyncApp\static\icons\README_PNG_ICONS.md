# MeetSync 图标生成指南

## 问题说明
项目中的 `pages.json` 引用了 PNG 格式的图标文件，但目录中只有 SVG 格式的图标。

## 解决方案

### 方案1：使用SVG图标（推荐）
已经更新了 `pages.json` 使用 SVG 图标：
```json
"iconPath": "static/icons/dashboard.svg",
"selectedIconPath": "static/icons/dashboard-active.svg"
```

SVG图标已更新为黑金主题：
- 普通状态：`#B8860B`（暗金色）
- 激活状态：`#FFD700`（金色）

### 方案2：生成PNG图标

#### 选项A：使用在线工具
1. 打开 `create_png_icons.html` 文件（在浏览器中）
2. 点击生成按钮
3. 下载生成的PNG文件
4. 将文件放到 `static/icons/` 目录下

#### 选项B：使用Node.js脚本
```bash
# 安装依赖
npm install canvas

# 运行脚本
node generate_icons.js
```

#### 选项C：手动创建
使用任何图像编辑软件（如Photoshop、GIMP、Figma等）：

1. **创建 dashboard.png**：
   - 尺寸：48x48px
   - 背景：透明
   - 图标颜色：`#B8860B`
   - 绘制仪表板图标（矩形+线条）

2. **创建 dashboard-active.png**：
   - 尺寸：48x48px
   - 背景：透明
   - 图标颜色：`#FFD700`
   - 可以添加填充效果

#### 选项D：使用SVG转PNG工具
在线工具：
- https://convertio.co/svg-png/
- https://cloudconvert.com/svg-to-png

步骤：
1. 上传 `dashboard.svg` 和 `dashboard-active.svg`
2. 设置输出尺寸为 48x48px
3. 下载转换后的PNG文件

## 图标设计规范

### 颜色规范
- **普通状态**：`#B8860B`（暗金色）
- **激活状态**：`#FFD700`（金色）
- **背景**：透明

### 尺寸规范
- **标准尺寸**：48x48px
- **线条粗细**：2px
- **圆角半径**：1px

### 图标内容
仪表板图标包含：
- 主矩形框架（18x12，圆角1px）
- 左侧垂直分割线
- 右侧两条水平线

## 当前状态
✅ SVG图标已更新为黑金主题
✅ pages.json已配置使用SVG图标
⚠️ 如需PNG图标，请使用上述任一方案生成

## 测试建议
1. 在不同平台测试SVG图标显示效果
2. 如果SVG不兼容，再使用PNG方案
3. 确保图标在不同屏幕密度下显示清晰
