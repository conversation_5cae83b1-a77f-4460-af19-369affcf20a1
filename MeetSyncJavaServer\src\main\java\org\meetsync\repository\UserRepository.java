package org.meetsync.repository;

import org.meetsync.entity.User;
import org.meetsync.entity.UserRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * Find user by username
     */
    Optional<User> findByUsername(String username);

    /**
     * Check if username exists
     */
    boolean existsByUsername(String username);


    /**
     * Find all active users
     */
    List<User> findByIsActiveTrue();

    /**
     * Find users by role
     */
    List<User> findByRoleAndIsActiveTrue(UserRole role);

    /**
     * Update last login time
     */
    @Modifying
    @Query("UPDATE User u SET u.lastLogin = :lastLogin WHERE u.id = :userId")
    void updateLastLogin(@Param("userId") Long userId, @Param("lastLogin") LocalDateTime lastLogin);

    /**
     * Deactivate user
     */
    @Modifying
    @Query("UPDATE User u SET u.isActive = false WHERE u.id = :userId")
    void deactivateUser(@Param("userId") Long userId);

    /**
     * Find users created after a specific date
     */
    List<User> findByCreatedAtAfterAndIsActiveTrue(LocalDateTime date);

    /**
     * Count users by role
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.role = :role AND u.isActive = true")
    long countByRole(@Param("role") UserRole role);

    /**
     * Find users with specific role and created after date
     */
    @Query("SELECT u FROM User u WHERE u.role = :role AND u.createdAt >= :fromDate AND u.isActive = true ORDER BY u.createdAt DESC")
    List<User> findByRoleAndCreatedAtAfter(@Param("role") UserRole role, @Param("fromDate") LocalDateTime fromDate);
}
