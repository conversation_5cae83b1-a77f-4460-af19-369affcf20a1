/**
 * Supported profiles.
 */
export declare enum Profile {
    ConstrainedBaseline = 1,
    Baseline = 2,
    Main = 3,
    ConstrainedHigh = 4,
    High = 5,
    PredictiveHigh444 = 6
}
/**
 * Supported levels.
 */
export declare enum Level {
    L1_b = 0,
    L1 = 10,
    L1_1 = 11,
    L1_2 = 12,
    L1_3 = 13,
    L2 = 20,
    L2_1 = 21,
    L2_2 = 22,
    L3 = 30,
    L3_1 = 31,
    L3_2 = 32,
    L4 = 40,
    L4_1 = 41,
    L4_2 = 42,
    L5 = 50,
    L5_1 = 51,
    L5_2 = 52
}
/**
 * Represents a parsed h264 profile-level-id value.
 */
export declare class ProfileLevelId {
    readonly profile: Profile;
    readonly level: Level;
    constructor(profile: Profile, level: Level);
}
/**
 * Parse profile level id that is represented as a string of 3 hex bytes.
 * Nothing will be returned if the string is not a recognized H264 profile
 * level id.
 */
export declare function parseProfileLevelId(str: string): ProfileLevelId | undefined;
/**
 * Returns canonical string representation as three hex bytes of the profile
 * level id, or returns nothing for invalid profile level ids.
 */
export declare function profileLevelIdToString(profile_level_id: ProfileLevelId): string | undefined;
/**
 * Returns a human friendly name for the given profile.
 */
export declare function profileToString(profile: Profile): string | undefined;
/**
 * Returns a human friendly name for the given level.
 */
export declare function levelToString(level: Level): string | undefined;
/**
 * Parse profile level id that is represented as a string of 3 hex bytes
 * contained in an SDP key-value map. A default profile level id will be
 * returned if the profile-level-id key is missing. Nothing will be returned
 * if the key is present but the string is invalid.
 */
export declare function parseSdpProfileLevelId(params?: any): ProfileLevelId | undefined;
/**
 * Returns true if the codec parameters have the same H264 profile, i.e. the
 * same H264 profile (Baseline, High, etc).
 */
export declare function isSameProfile(params1?: any, params2?: any): boolean;
/**
 * Returns true if the codec parameters have the same H264 profile, i.e. the
 * same H264 profile (Baseline, High, etc) and same level.
 */
export declare function isSameProfileAndLevel(params1?: any, params2?: any): boolean;
/**
 * Generate codec parameters that will be used as answer in an SDP negotiation
 * based on local supported parameters and remote offered parameters. Both
 * local_supported_params and remote_offered_params represent sendrecv media
 * descriptions, i.e they are a mix of both encode and decode capabilities. In
 * theory, when the profile in local_supported_params represent a strict
 * superset of the profile in remote_offered_params, we could limit the profile
 * in the answer to the profile in remote_offered_params.
 *
 * However, to simplify the code, each supported H264 profile should be listed
 * explicitly in the list of local supported codecs, even if they are redundant.
 * Then each local codec in the list should be tested one at a time against the
 * remote codec, and only when the profiles are equal should this function be
 * called. Therefore, this function does not need to handle profile intersection,
 * and the profile of local_supported_params and remote_offered_params must be
 * equal before calling this function. The parameters that are used when
 * negotiating are the level part of profile-level-id and
 * level-asymmetry-allowed.
 */
export declare function generateProfileLevelIdStringForAnswer(local_supported_params?: any, remote_offered_params?: any): string | undefined;
/**
 * Given that a decoder supports up to a given frame size (in pixels) at up to
 * a given number of frames per second, return the highest H264 level where it
 * can guarantee that it will be able to support all valid encoded streams that
 * are within that level.
 */
export declare function supportedLevel(max_frame_pixel_count: number, max_fps: number): Level | undefined;
//# sourceMappingURL=index.d.ts.map