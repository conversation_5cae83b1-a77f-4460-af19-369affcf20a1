<template>
  <view class="dashboard-container">
    <!-- 头部用户信息 -->
    <view class="header">
      <view class="user-info">
        <text class="username">{{ user.username }}</text>
        <text class="user-role" :class="user.role">{{ getRoleText(user.role) }}</text>
      </view>
      <view class="header-actions">
        <button class="test-btn" @click="goToTest">
          <text class="test-icon">🔧</text>
        </button>
        <button class="logout-btn" @click="handleLogout">
          <text class="logout-icon">🚪</text>
        </button>
      </view>
    </view>

    <!-- 标签页 -->
    <view class="tabs">
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'public' }"
        @click="switchTab('public')"
      >
        公共房间
      </view>
      <view
        class="tab-item"
        :class="{ active: activeTab === 'my' }"
        @click="switchTab('my')"
      >
        我的房间
      </view>
    </view>

    <!-- 创建房间按钮 -->
    <view class="create-room-section">
      <button
        class="btn btn-primary create-btn"
        @click="showCreateModal = true"
      >
        <text class="create-icon">➕</text>
        创建房间
      </button>
    </view>

    <!-- 房间列表 -->
    <view class="room-list">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading">
        <text>加载中...</text>
      </view>

      <!-- 空状态 -->
      <view v-else-if="currentRooms.length === 0" class="empty-state">
        <text class="empty-icon">📭</text>
        <text class="empty-text">{{ activeTab === 'public' ? '暂无公共房间' : '您还没有创建房间' }}</text>
      </view>

      <!-- 房间卡片 -->
      <view v-else class="rooms">
        <view 
          v-for="room in currentRooms" 
          :key="room.id" 
          class="room-card"
          @click="joinRoom(room)"
        >
          <view class="room-header">
            <text class="room-name">{{ room.name }}</text>
            <view class="room-status" :class="{ active: room.isActive }">
              <text class="status-dot"></text>
              <text class="status-text">{{ room.isActive ? '活跃' : '空闲' }}</text>
            </view>
          </view>
          
          <view class="room-info">
            <text class="room-id">房间ID: {{ room.roomId }}</text>
            <text class="room-participants">👥 {{ room.participantCount || 0 }} 人</text>
          </view>

          <view class="room-meta">
            <text class="room-creator">创建者: {{ room.creator ? room.creator.username : '未知' }}</text>
            <text class="room-time">{{ formatTime(room.createdAt) }}</text>
          </view>

          <view v-if="room.hasPassword" class="room-password">
            <text class="password-icon">🔒</text>
            <text>需要密码</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 创建房间模态框 -->
    <view v-if="showCreateModal" class="modal-overlay" @click="closeCreateModal">
      <view class="modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">创建房间</text>
          <button class="close-btn" @click="closeCreateModal">✕</button>
        </view>
        
        <view class="modal-body">
          <view class="form-group">
            <text class="label">房间名称</text>
            <input 
              class="form-input" 
              type="text" 
              placeholder="请输入房间名称"
              v-model="createForm.name"
              maxlength="50"
            />
          </view>
          
          <view class="form-group">
            <text class="label">房间描述</text>
            <textarea 
              class="form-textarea" 
              placeholder="请输入房间描述（可选）"
              v-model="createForm.description"
              maxlength="200"
            />
          </view>
          
          <view class="form-group">
            <view class="checkbox-group">
              <checkbox
                :checked="createForm.hasPassword"
                @change="onPasswordCheckboxChange"
                color="#007bff"
              />
              <text class="checkbox-label">设置房间密码</text>
            </view>
          </view>
          
          <view v-if="createForm.hasPassword" class="form-group">
            <input 
              class="form-input" 
              type="password" 
              placeholder="请输入房间密码"
              v-model="createForm.password"
              maxlength="20"
            />
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="btn btn-secondary" @click="closeCreateModal">取消</button>
          <button 
            class="btn btn-primary" 
            @click="handleCreateRoom"
            :disabled="!canCreateRoom || creating"
          >
            {{ creating ? '创建中...' : '创建' }}
          </button>
        </view>
      </view>
    </view>

    <!-- 加入房间密码模态框 -->
    <view v-if="showPasswordModal" class="modal-overlay" @click="closePasswordModal">
      <view class="modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">输入房间密码</text>
          <button class="close-btn" @click="closePasswordModal">✕</button>
        </view>
        
        <view class="modal-body">
          <text class="password-hint">房间 "{{ (selectedRoom && selectedRoom.name) || '未知' }}" 需要密码</text>
          <view class="form-group">
            <input 
              class="form-input" 
              type="password" 
              placeholder="请输入房间密码"
              v-model="roomPassword"
            />
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="btn btn-secondary" @click="closePasswordModal">取消</button>
          <button 
            class="btn btn-primary" 
            @click="confirmJoinRoom"
            :disabled="!roomPassword.trim() || joining"
          >
            {{ joining ? '加入中...' : '加入' }}
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import authManager from '../../utils/auth.js'
import apiManager from '../../utils/api.js'

export default {
  name: 'Dashboard',
  data() {
    return {
      user: null,
      activeTab: 'public',
      loading: false,
      creating: false,
      joining: false,
      
      // 房间数据
      publicRooms: [],
      myRooms: [],
      
      // 模态框状态
      showCreateModal: false,
      showPasswordModal: false,
      selectedRoom: null,
      roomPassword: '',
      
      // 创建房间表单
      createForm: {
        name: '',
        description: '',
        hasPassword: false,
        password: ''
      }
    }
  },
  computed: {
    currentRooms() {
      return this.activeTab === 'public' ? this.publicRooms : this.myRooms
    },
    canCreateRoom() {
      return this.createForm.name.trim() && 
             (!this.createForm.hasPassword || this.createForm.password.trim())
    }
  },
  onLoad() {
    this.user = authManager.getCurrentUser()
    
    if (!this.user) {
      uni.reLaunch({
        url: '/pages/auth/login'
      })
      return
    }
    
    this.loadRooms()
  },
  onShow() {
    // 页面显示时刷新房间列表
    this.loadRooms()
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab
      this.loadRooms()
    },
    
    async loadRooms() {
      this.loading = true

      try {
        if (this.activeTab === 'public') {
          const response = await apiManager.getPublicRooms()
          // Java后端直接返回房间数组，不是包装在rooms字段中
          this.publicRooms = Array.isArray(response) ? response : (response.rooms || [])
        } else {
          const response = await apiManager.getMyRooms()
          // Java后端直接返回房间数组，不是包装在rooms字段中
          this.myRooms = Array.isArray(response) ? response : (response.rooms || [])
        }
      } catch (error) {
        console.error('加载房间失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },
    
    joinRoom(room) {
      if (room.hasPassword) {
        this.selectedRoom = room
        this.roomPassword = ''
        this.showPasswordModal = true
      } else {
        this.navigateToRoom(room.roomId)
      }
    },
    
    async confirmJoinRoom() {
      if (!this.roomPassword.trim()) return
      
      this.joining = true
      
      try {
        // 验证密码并加入房间
        this.navigateToRoom(this.selectedRoom.roomId, this.roomPassword)
      } catch (error) {
        uni.showToast({
          title: '密码错误',
          icon: 'error'
        })
      } finally {
        this.joining = false
        this.closePasswordModal()
      }
    },
    
    navigateToRoom(roomId, password = null) {
      // 存储房间信息到本地，供房间页面使用
      uni.setStorageSync('joinRoomId', roomId)
      if (password) {
        uni.setStorageSync('joinRoomPassword', password)
      }
      
      uni.navigateTo({
        url: `/pages/room/index?roomId=${roomId}`
      })
    },
    
    async handleCreateRoom() {
      if (!this.canCreateRoom) return

      this.creating = true

      try {
        // 生成房间ID
        const roomId = this.generateRoomId()

        const roomData = {
          roomId: roomId,
          name: this.createForm.name.trim(),
          description: this.createForm.description.trim(),
          maxParticipants: 10,
          isPublic: true,
          password: this.createForm.hasPassword ? this.createForm.password.trim() : null,
          requiredRole: 'USER'
        }

        const response = await apiManager.createRoom(roomData)

        uni.showToast({
          title: '创建成功',
          icon: 'success'
        })

        this.closeCreateModal()
        this.resetCreateForm()

        // 刷新房间列表
        this.loadRooms()

        // 自动加入创建的房间
        setTimeout(() => {
          this.navigateToRoom(response.roomId || roomId, roomData.password)
        }, 1000)

      } catch (error) {
        console.error('创建房间失败:', error)
        uni.showToast({
          title: error.message || '创建失败',
          icon: 'error'
        })
      } finally {
        this.creating = false
      }
    },

    generateRoomId() {
      // 生成6位随机房间ID
      const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'
      let result = ''
      for (let i = 0; i < 6; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      return result
    },

    onPasswordCheckboxChange(e) {
      this.createForm.hasPassword = e.detail.value
    },

    closeCreateModal() {
      this.showCreateModal = false
    },
    
    closePasswordModal() {
      this.showPasswordModal = false
      this.selectedRoom = null
      this.roomPassword = ''
    },
    
    resetCreateForm() {
      this.createForm = {
        name: '',
        description: '',
        hasPassword: false,
        password: ''
      }
    },
    
    goToTest() {
      uni.navigateTo({
        url: '/pages/test/java-backend'
      })
    },

    handleLogout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            authManager.logout()
          }
        }
      })
    },
    
    getRoleText(role) {
      const roleMap = {
        'admin': '管理员',
        'user': '用户',
        'guest': '游客'
      }
      return roleMap[role] || role
    },
    
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) { // 1天内
        return `${Math.floor(diff / 3600000)}小时前`
      } else {
        return date.toLocaleDateString()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
  background: #1A1A1A;
  display: flex;
  flex-direction: column;
}

.header {
  background: linear-gradient(135deg, #2D2D2D 0%, #1A1A1A 100%);
  border-bottom: 2rpx solid #FFD700;
  padding: 30rpx 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #FFD700;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.user-role {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.2);
  align-self: flex-start;
}

.user-role.admin {
  background: #FF6B6B;
}

.user-role.user {
  background: #32CD32;
}

.user-role.guest {
  background: #B8860B;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.test-btn, .logout-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-icon, .logout-icon {
  font-size: 32rpx;
}

.tabs {
  display: flex;
  background: #2D2D2D;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.2);
  border: 1rpx solid #444444;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #B8860B;
  transition: all 0.3s;
}

.tab-item.active {
  background: #FFD700;
  color: #1A1A1A;
}

.create-room-section {
  padding: 0 20rpx 20rpx;
}

.create-btn {
  width: 100%;
  background: linear-gradient(135deg, #FFD700 0%, #DAA520 100%);
  color: #1A1A1A;
  border: none;
  border-radius: 12rpx;
  padding: 25rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
}

.create-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

.room-list {
  flex: 1;
  padding: 0 20rpx;
  overflow-y: auto;
  max-height: calc(100vh - 200rpx);
}

.loading, .empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #B8860B;
}

.empty-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
}

.rooms {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.room-card {
  background: #2D2D2D;
  border: 1rpx solid #444444;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.1);
  transition: all 0.2s;
}

.room-card:active {
  transform: scale(0.98);
  border-color: #FFD700;
  box-shadow: 0 6rpx 16rpx rgba(255, 215, 0, 0.2);
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.room-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #FFD700;
  flex: 1;
}

.room-status {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #B8860B;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #666;
  margin-right: 8rpx;
}

.room-status.active .status-dot {
  background: #32CD32;
}

.room-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
  font-size: 26rpx;
  color: #E5E5E5;
}

.room-meta {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #B8860B;
}

.room-password {
  margin-top: 15rpx;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #FFA500;
}

.password-icon {
  margin-right: 8rpx;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: #2D2D2D;
  border: 2rpx solid #FFD700;
  border-radius: 16rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  border-bottom: 2rpx solid #444444;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #FFD700;
}

.close-btn {
  background: none;
  border: none;
  font-size: 32rpx;
  color: #B8860B;
  padding: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #FFD700;
  margin-bottom: 12rpx;
}

.form-input, .form-textarea {
  width: 100%;
  height: auto;
  padding: 20rpx;
  border: 2rpx solid #444444;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background: #1A1A1A;
  color: #E5E5E5;
}

.form-textarea {
  height: 120rpx;
  resize: none;
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-label {
  margin-left: 12rpx;
  font-size: 28rpx;
  color: #E5E5E5;
}

.password-hint {
  font-size: 28rpx;
  color: #B8860B;
  margin-bottom: 20rpx;
  text-align: center;
}

.modal-footer {
  padding: 30rpx;
  border-top: 2rpx solid #444444;
  display: flex;
  gap: 20rpx;
}

.modal-footer .btn {
  flex: 1;
  padding: 20rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.btn-secondary {
  background: #B8860B;
  color: white;
}

.btn-primary {
  background: #FFD700;
  color: #1A1A1A;
  font-weight: bold;
}

.btn:disabled {
  opacity: 0.6;
}
</style>
