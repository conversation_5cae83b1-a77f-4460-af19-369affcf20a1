package org.meetsync.dto;


import org.meetsync.ExceptionHandler.EnumBusinessError;

public class CommonReturnType<T> {
    private int status;
    private T data;
    private Object info;

    /**
     * 成功状态的无信息返回
     *
     * @param result 返回信息
     */
    public static <T> CommonReturnType<T> create(T result) {
        return CommonReturnType.create(0, result, "SUCCESS");
    }

    public static <T> CommonReturnType<T> create(T result, String info) {
        return CommonReturnType.create(0, result, info);
    }

    public static <T> CommonReturnType<T> error(Object info) {
        return CommonReturnType.create(5000, null, info);
    }

    public static <T> CommonReturnType<T> error(Object info, int errorCode) {
        return CommonReturnType.create(5000, null, info);
    }

    public static <T> CommonReturnType<T> error(EnumBusinessError error) {
        return CommonReturnType.create(error.getErrorCode(), null, error.getErrorMsg());
    }

    /**
     * 自定义返回
     *
     * @param result 返回信息
     * @param status 状态码
     * @param info   返回数据
     */
    public static <T> CommonReturnType<T> create(int status, T result, Object info) {
        CommonReturnType<T> commonReturnType = new CommonReturnType<T>();
        commonReturnType.setData(result);
        commonReturnType.setStatus(status);
        commonReturnType.setInfo(info);
        return commonReturnType;
    }

    public static <T> CommonReturnType<String> create(EnumBusinessError error) {
        return create(error.getErrorCode(), null, error.getErrorMsg());
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Object getInfo() {
        return info;
    }

    public void setInfo(Object info) {
        this.info = info;
    }
}
