package org.meetsync.service;

import org.meetsync.dto.RoomCreateDto;
import org.meetsync.dto.RoomResponseDto;
import org.meetsync.entity.*;
import org.meetsync.repository.RoomRepository;
import org.meetsync.repository.UserRoomPermissionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class RoomService {

    private static final Logger logger = LoggerFactory.getLogger(RoomService.class);

    @Autowired
    private RoomRepository roomRepository;

    @Autowired
    private UserRoomPermissionRepository permissionRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * Create a new room
     */
    public RoomResponseDto createRoom(RoomCreateDto createDto, Long creatorId) {
        logger.info("Creating room: {} by user ID: {}", createDto.getRoomId(), creatorId);

        // Check if room ID already exists
        if (roomRepository.existsByRoomId(createDto.getRoomId())) {
            throw new RuntimeException("房间ID已存在");
        }

        // Get creator user
        User creator = userService.findUserById(creatorId)
                .orElseThrow(() -> new RuntimeException("创建者用户不存在"));

        // Create room entity
        Room room = new Room();
        room.setRoomId(createDto.getRoomId());
        room.setName(createDto.getName());
        room.setDescription(createDto.getDescription());
        room.setCreator(creator);
        room.setMaxParticipants(createDto.getMaxParticipants());
        room.setIsPublic(createDto.getIsPublic());
        
        // Set password hash if password provided
        if (createDto.getPassword() != null && !createDto.getPassword().trim().isEmpty()) {
            room.setPasswordHash(passwordEncoder.encode(createDto.getPassword()));
        }

        // Set required role
        try {
            room.setRequiredRole(UserRole.fromString(createDto.getRequiredRole()));
        } catch (IllegalArgumentException e) {
            room.setRequiredRole(UserRole.USER); // Default role
        }

        room.setIsActive(true);

        Room savedRoom = roomRepository.save(room);

        // Grant full access to creator
        grantPermission(savedRoom.getId(), creatorId, PermissionType.FULL_ACCESS, creatorId);

        logger.info("Room created successfully: {}", savedRoom.getRoomId());

        return new RoomResponseDto(savedRoom, PermissionType.FULL_ACCESS.getValue());
    }

    /**
     * Find room by room ID
     */
    @Transactional(readOnly = true)
    public Optional<RoomResponseDto> findByRoomId(String roomId, Long userId) {
        Optional<Room> roomOpt = roomRepository.findByRoomIdAndIsActiveTrue(roomId);
        
        if (roomOpt.isPresent()) {
            Room room = roomOpt.get();
            String permission = getUserPermissionForRoom(userId, roomId);
            return Optional.of(new RoomResponseDto(room, permission));
        }
        
        return Optional.empty();
    }

    /**
     * Get room entity by room ID
     */
    @Transactional(readOnly = true)
    public Optional<Room> findRoomByRoomId(String roomId) {
        return roomRepository.findByRoomIdAndIsActiveTrue(roomId);
    }

    /**
     * Get all public rooms
     */
    @Transactional(readOnly = true)
    public List<RoomResponseDto> getPublicRooms(Long userId) {
        return roomRepository.findPublicRooms()
                .stream()
                .map(room -> {
                    String permission = getUserPermissionForRoom(userId, room.getRoomId());
                    return new RoomResponseDto(room, permission);
                })
                .collect(Collectors.toList());
    }

    /**
     * Get accessible public rooms for user based on role
     */
    @Transactional(readOnly = true)
    public List<RoomResponseDto> getAccessiblePublicRooms(Long userId) {
        User user = userService.findUserById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        return roomRepository.findAccessiblePublicRooms(user.getRole().name())
                .stream()
                .map(room -> {
                    String permission = getUserPermissionForRoom(userId, room.getRoomId());
                    return new RoomResponseDto(room, permission);
                })
                .collect(Collectors.toList());
    }

    /**
     * Get rooms created by user
     */
    @Transactional(readOnly = true)
    public List<RoomResponseDto> getUserCreatedRooms(Long userId) {
        User user = userService.findUserById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        return roomRepository.findByCreatorAndIsActiveTrueOrderByCreatedAtDesc(user)
                .stream()
                .map(room -> new RoomResponseDto(room, PermissionType.FULL_ACCESS.getValue()))
                .collect(Collectors.toList());
    }

    /**
     * Get rooms user has access to
     */
    @Transactional(readOnly = true)
    public List<RoomResponseDto> getUserAccessibleRooms(Long userId) {
        User user = userService.findUserById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        return roomRepository.findAccessibleRooms(userId, user.getRole().name())
                .stream()
                .map(room -> {
                    String permission = getUserPermissionForRoom(userId, room.getRoomId());
                    return new RoomResponseDto(room, permission);
                })
                .collect(Collectors.toList());
    }

    /**
     * Update room
     */
    public RoomResponseDto updateRoom(String roomId, RoomCreateDto updateDto, Long userId) {
        logger.info("Updating room: {} by user ID: {}", roomId, userId);

        Room room = roomRepository.findByRoomIdAndIsActiveTrue(roomId)
                .orElseThrow(() -> new RuntimeException("房间不存在"));

        // Check if user has permission to update room (creator or admin)
        User user = userService.findUserById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        if (!room.getCreator().getId().equals(userId) && user.getRole() != UserRole.ADMIN) {
            throw new RuntimeException("权限不足，无法更新房间");
        }

        // Update room properties
        room.setName(updateDto.getName());
        room.setDescription(updateDto.getDescription());
        room.setMaxParticipants(updateDto.getMaxParticipants());
        room.setIsPublic(updateDto.getIsPublic());

        // Update password if provided
        if (updateDto.getPassword() != null && !updateDto.getPassword().trim().isEmpty()) {
            room.setPasswordHash(passwordEncoder.encode(updateDto.getPassword()));
        } else if (updateDto.getPassword() != null && updateDto.getPassword().trim().isEmpty()) {
            room.setPasswordHash(null); // Remove password
        }

        // Update required role
        try {
            room.setRequiredRole(UserRole.fromString(updateDto.getRequiredRole()));
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid role provided: {}, keeping current role", updateDto.getRequiredRole());
        }

        Room savedRoom = roomRepository.save(room);
        logger.info("Room updated successfully: {}", savedRoom.getRoomId());

        String permission = getUserPermissionForRoom(userId, roomId);
        return new RoomResponseDto(savedRoom, permission);
    }

    /**
     * Delete room
     */
    public void deleteRoom(String roomId, Long userId) {
        logger.info("Deleting room: {} by user ID: {}", roomId, userId);

        Room room = roomRepository.findByRoomIdAndIsActiveTrue(roomId)
                .orElseThrow(() -> new RuntimeException("房间不存在"));

        // Check if user has permission to delete room (creator or admin)
        User user = userService.findUserById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        if (!room.getCreator().getId().equals(userId) && user.getRole() != UserRole.ADMIN) {
            throw new RuntimeException("权限不足，无法删除房间");
        }

        // Deactivate room and all its permissions
        roomRepository.deactivateRoomByRoomId(roomId);
        permissionRepository.deactivateRoomPermissions(room.getId());

        logger.info("Room deleted successfully: {}", roomId);
    }

    /**
     * Check if user can access room
     */
    @Transactional(readOnly = true)
    public boolean canUserAccessRoom(Long userId, String roomId, String password) {
        Room room = roomRepository.findByRoomIdAndIsActiveTrue(roomId)
                .orElse(null);

        if (room == null) {
            return false;
        }

        User user = userService.findUserById(userId)
                .orElse(null);

        if (user == null) {
            return false;
        }

        // Check if user is creator
        if (room.getCreator().getId().equals(userId)) {
            return true;
        }

        // Check if user has explicit permission
        Optional<UserRoomPermission> permission = permissionRepository
                .findValidPermission(userId, roomId, LocalDateTime.now());
        if (permission.isPresent()) {
            return true;
        }

        // Check if room is public and user meets role requirement
        if (room.getIsPublic()) {
            // Check role requirement
            boolean roleMatches = switch (room.getRequiredRole()) {
                case GUEST -> true;
                case USER -> user.getRole() == UserRole.USER || user.getRole() == UserRole.PREMIUM || user.getRole() == UserRole.ADMIN;
                case PREMIUM -> user.getRole() == UserRole.PREMIUM || user.getRole() == UserRole.ADMIN;
                case ADMIN -> user.getRole() == UserRole.ADMIN;
            };

            if (!roleMatches) {
                return false;
            }

            // Check password if required
            if (room.getPasswordHash() != null && !room.getPasswordHash().isEmpty()) {
                return password != null && passwordEncoder.matches(password, room.getPasswordHash());
            }

            return true;
        }

        return false;
    }

    /**
     * Get user's permission for a room
     */
    @Transactional(readOnly = true)
    public String getUserPermissionForRoom(Long userId, String roomId) {
        Room room = roomRepository.findByRoomIdAndIsActiveTrue(roomId)
                .orElse(null);

        if (room == null) {
            return null;
        }

        User user = userService.findUserById(userId)
                .orElse(null);

        if (user == null) {
            return null;
        }

        // Check if user is creator
        if (room.getCreator().getId().equals(userId)) {
            return PermissionType.FULL_ACCESS.getValue();
        }

        // Check explicit permission
        Optional<UserRoomPermission> permission = permissionRepository
                .findValidPermission(userId, roomId, LocalDateTime.now());
        if (permission.isPresent()) {
            return permission.get().getPermissionType().getValue();
        }

        // Return default permission based on user role
        return user.getDefaultPermission();
    }

    /**
     * Grant permission to user for room
     */
    public void grantPermission(Long roomId, Long userId, PermissionType permissionType, Long grantedById) {
        logger.info("Granting permission {} to user {} for room {} by user {}", 
                   permissionType, userId, roomId, grantedById);

        Room room = roomRepository.findById(roomId)
                .orElseThrow(() -> new RuntimeException("房间不存在"));

        User user = userService.findUserById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        User grantedBy = userService.findUserById(grantedById)
                .orElseThrow(() -> new RuntimeException("授权用户不存在"));

        // Deactivate existing permissions for this user in this room
        permissionRepository.deactivateUserRoomPermissions(userId, roomId);

        // Create new permission
        UserRoomPermission permission = new UserRoomPermission();
        permission.setUser(user);
        permission.setRoom(room);
        permission.setPermissionType(permissionType);
        permission.setGrantedBy(grantedBy);
        permission.setIsActive(true);

        permissionRepository.save(permission);

        logger.info("Permission granted successfully");
    }

    /**
     * Search rooms by name
     */
    @Transactional(readOnly = true)
    public List<RoomResponseDto> searchRoomsByName(String name, Long userId) {
        return roomRepository.findByNameContainingIgnoreCase(name)
                .stream()
                .map(room -> {
                    String permission = getUserPermissionForRoom(userId, room.getRoomId());
                    return new RoomResponseDto(room, permission);
                })
                .collect(Collectors.toList());
    }

    /**
     * Count rooms by creator
     */
    @Transactional(readOnly = true)
    public long countRoomsByCreator(Long creatorId) {
        return roomRepository.countByCreatorId(creatorId);
    }

    /**
     * Count public rooms
     */
    @Transactional(readOnly = true)
    public long countPublicRooms() {
        return roomRepository.countPublicRooms();
    }

    public boolean roomExists(String roomId) {
        return roomRepository.existsByRoomId(roomId);
    }
}
