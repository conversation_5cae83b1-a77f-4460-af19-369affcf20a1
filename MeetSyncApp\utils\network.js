/**
 * 网络兼容性工具类
 * 处理 SSL/TLS 兼容性问题
 */

import config from '../config/index.js'

class NetworkManager {
  constructor() {
    this.config = config.server
    this.retryCount = 1
    this.retryDelay = 1000
  }

  /**
   * 获取网络请求的基础配置
   */
  getRequestConfig(options = {}) {
    const baseConfig = {
      timeout: 30000,
      // SSL 兼容性配置
      sslVerify: this.config.sslVerify || false,
      enableHttp2: this.config.enableHttp2 || false,
      enableQuic: this.config.enableQuic || false,
      enableCache: false,
      enableHttpDNS: false,
      // 重试配置
      retry: true,
      retryTimes: this.retryCount,
      ...options
    }

    // 开发环境额外配置
    if (config.env === 'development') {
      baseConfig.sslVerify = false
      baseConfig.enableHttp2 = false
      baseConfig.enableQuic = false
    }

    return baseConfig
  }

  /**
   * 带重试的网络请求
   */
  async requestWithRetry(requestOptions, retryCount = this.retryCount) {
    const config = this.getRequestConfig(requestOptions)
    
    for (let i = 0; i < retryCount; i++) {
      try {
        console.log(`[Network] 尝试请求 (${i + 1}/${retryCount}):`, config.url)
        
        const result = await this.makeRequest(config)
        console.log(`[Network] 请求成功:`, config.url)
        return result
        
      } catch (error) {
        console.error(`[Network] 请求失败 (${i + 1}/${retryCount}):`, error.message)
        
        // 如果是 SSL 相关错误，尝试降级配置
        if (this.isSSLError(error) && i < retryCount - 1) {
          console.log(`[Network] 检测到 SSL 错误，尝试降级配置`)
          config.sslVerify = false
          config.enableHttp2 = false
          config.enableQuic = false
        }
        
        // 最后一次重试失败
        if (i === retryCount - 1) {
          throw this.createNetworkError(error)
        }
        
        // 等待后重试
        await this.delay(this.retryDelay * (i + 1))
      }
    }
  }

  /**
   * 执行实际的网络请求
   */
  makeRequest(config) {
    return new Promise((resolve, reject) => {
      uni.request({
        ...config,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data)
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.data?.error || '请求失败'}`))
          }
        },
        fail: (error) => {
          reject(new Error(error.errMsg || '网络请求失败'))
        }
      })
    })
  }

  /**
   * 判断是否为 SSL 相关错误
   */
  isSSLError(error) {
    const sslErrorKeywords = [
      'SSL',
      'TLS',
      'certificate',
      'handshake',
      'protocol version',
      'cipher suite',
      'CERT_',
      'ERR_SSL_',
      'ERR_CERT_',
      'SSL_ERROR_',
      'SEC_ERROR_'
    ]
    
    const errorMessage = error.message || error.errMsg || ''
    return sslErrorKeywords.some(keyword => 
      errorMessage.toLowerCase().includes(keyword.toLowerCase())
    )
  }

  /**
   * 创建标准化的网络错误
   */
  createNetworkError(originalError) {
    const errorMessage = originalError.message || originalError.errMsg || '网络请求失败'
    
    if (this.isSSLError(originalError)) {
      return new Error('SSL连接失败，请检查服务器证书配置或联系管理员')
    }
    
    if (errorMessage.includes('timeout')) {
      return new Error('网络请求超时，请检查网络连接')
    }
    
    if (errorMessage.includes('Network Error')) {
      return new Error('网络连接失败，请检查网络设置')
    }
    
    return new Error(`网络请求失败: ${errorMessage}`)
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 检测网络连接状态
   */
  async checkNetworkStatus() {
    return new Promise((resolve) => {
      uni.getNetworkType({
        success: (res) => {
          const networkType = res.networkType
          const isConnected = networkType !== 'none'
          
          resolve({
            isConnected,
            networkType,
            isWifi: networkType === 'wifi',
            isMobile: ['2g', '3g', '4g', '5g'].includes(networkType)
          })
        },
        fail: () => {
          resolve({
            isConnected: false,
            networkType: 'unknown',
            isWifi: false,
            isMobile: false
          })
        }
      })
    })
  }

  /**
   * 测试服务器连接
   */
  async testServerConnection(baseUrl = this.config.baseUrl) {
    try {
      console.log(`[Network] 测试服务器连接: ${baseUrl}`)
      
      const testConfig = {
        url: `${baseUrl}/api/health`,
        method: 'GET',
        timeout: 10000,
        sslVerify: false,
        enableHttp2: false,
        enableQuic: false
      }
      
      await this.makeRequest(testConfig)
      console.log(`[Network] 服务器连接正常`)
      return true
      
    } catch (error) {
      console.error(`[Network] 服务器连接失败:`, error.message)
      return false
    }
  }

  /**
   * 获取推荐的网络配置
   */
  getRecommendedConfig() {
    return {
      // 基础配置
      timeout: 30000,
      sslVerify: false, // 开发环境建议关闭
      enableHttp2: false, // 兼容性考虑
      enableQuic: false, // 兼容性考虑
      enableCache: false,
      enableHttpDNS: false,
      
      // 重试配置
      retry: true,
      retryTimes: 3,
      retryDelay: 1000,
      
      // 请求头
      header: {
        'Content-Type': 'application/json',
        'User-Agent': 'MeetSync-UniApp/1.0.0',
        'Accept': 'application/json',
        'Cache-Control': 'no-cache'
      }
    }
  }

  /**
   * 诊断网络问题
   */
  async diagnoseNetworkIssue(error) {
    const diagnosis = {
      error: error.message,
      suggestions: [],
      canRetry: false
    }

    // 检查网络状态
    const networkStatus = await this.checkNetworkStatus()
    if (!networkStatus.isConnected) {
      diagnosis.suggestions.push('请检查网络连接')
      return diagnosis
    }

    // SSL 相关问题
    if (this.isSSLError(error)) {
      diagnosis.suggestions.push(
        '检测到 SSL/TLS 连接问题',
        '1. 确认服务器证书配置正确',
        '2. 检查服务器是否支持 TLS 1.2 或更高版本',
        '3. 开发环境可以尝试关闭 SSL 验证',
        '4. 联系服务器管理员检查加密套件配置'
      )
      diagnosis.canRetry = true
    }

    // 超时问题
    if (error.message.includes('timeout')) {
      diagnosis.suggestions.push(
        '网络请求超时',
        '1. 检查网络连接速度',
        '2. 尝试增加请求超时时间',
        '3. 检查服务器响应速度'
      )
      diagnosis.canRetry = true
    }

    // 服务器错误
    if (error.message.includes('500') || error.message.includes('502') || error.message.includes('503')) {
      diagnosis.suggestions.push(
        '服务器内部错误',
        '1. 检查服务器是否正常运行',
        '2. 查看服务器日志',
        '3. 稍后重试'
      )
      diagnosis.canRetry = true
    }

    return diagnosis
  }
}

// 创建全局实例
const networkManager = new NetworkManager()

export default networkManager
