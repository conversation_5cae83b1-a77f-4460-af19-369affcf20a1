FROM mysql:5.7

# 设置工作目录
WORKDIR /docker-entrypoint-initdb.d

# 复制初始化脚本
COPY ./init-scripts/ /docker-entrypoint-initdb.d/

# 复制自定义配置文件
COPY ./my.cnf /etc/mysql/conf.d/my.cnf

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建日志目录
RUN mkdir -p /var/log/mysql && \
    chown -R mysql:mysql /var/log/mysql

# 暴露端口
EXPOSE 3306 33060

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
  CMD mysqladmin ping -h localhost -u root -p${MYSQL_ROOT_PASSWORD} || exit 1

# 设置数据卷
VOLUME /var/lib/mysql

# 设置字符集和排序规则
CMD ["mysqld", "--character-set-server=utf8mb4", "--collation-server=utf8mb4_unicode_ci"]

