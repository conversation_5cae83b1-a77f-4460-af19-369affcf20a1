package org.meetsync.dto;

import org.meetsync.entity.Room;
import org.meetsync.entity.UserRole;

import java.time.LocalDateTime;

public class RoomResponseDto {

    private Long id;
    private String roomId;
    private String name;
    private String description;
    private UserResponseDto creator;
    private Integer maxParticipants;
    private Boolean isPublic;
    private Boolean hasPassword;
    private UserRole requiredRole;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String permission; // User's permission for this room

    // Constructors
    public RoomResponseDto() {}

    public RoomResponseDto(Room room) {
        this.id = room.getId();
        this.roomId = room.getRoomId();
        this.name = room.getName();
        this.description = room.getDescription();
        this.creator = room.getCreator() != null ? new UserResponseDto(room.getCreator()) : null;
        this.maxParticipants = room.getMaxParticipants();
        this.isPublic = room.getIsPublic();
        this.hasPassword = room.getPasswordHash() != null && !room.getPasswordHash().isEmpty();
        this.requiredRole = room.getRequiredRole();
        this.isActive = room.getIsActive();
        this.createdAt = room.getCreatedAt();
        this.updatedAt = room.getUpdatedAt();
    }

    public RoomResponseDto(Room room, String permission) {
        this(room);
        this.permission = permission;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public UserResponseDto getCreator() {
        return creator;
    }

    public void setCreator(UserResponseDto creator) {
        this.creator = creator;
    }

    public Integer getMaxParticipants() {
        return maxParticipants;
    }

    public void setMaxParticipants(Integer maxParticipants) {
        this.maxParticipants = maxParticipants;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Boolean getHasPassword() {
        return hasPassword;
    }

    public void setHasPassword(Boolean hasPassword) {
        this.hasPassword = hasPassword;
    }

    public UserRole getRequiredRole() {
        return requiredRole;
    }

    public void setRequiredRole(UserRole requiredRole) {
        this.requiredRole = requiredRole;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    @Override
    public String toString() {
        return "RoomResponseDto{" +
                "id=" + id +
                ", roomId='" + roomId + '\'' +
                ", name='" + name + '\'' +
                ", creator=" + (creator != null ? creator.getUsername() : null) +
                ", isPublic=" + isPublic +
                ", hasPassword=" + hasPassword +
                ", requiredRole=" + requiredRole +
                ", permission='" + permission + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
