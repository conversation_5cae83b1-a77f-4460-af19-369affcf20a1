/**
 * Service Worker for MeetSync PWA
 * 提供离线支持和缓存管理
 *
 * 注意：此文件需要复制到项目根目录才能正常工作
 */

const CACHE_NAME = 'meetsync-v1.0.0'
const RUNTIME_CACHE = 'meetsync-runtime'

// 需要缓存的静态资源
const STATIC_CACHE_URLS = [
  '/',
  '/index.html',
  '/static/css/uni.css',
  '/static/js/chunk-vendors.js',
  '/static/js/index.js',
  '/static/manifest.json',
  '/static/icons/icon-192x192.png',
  '/static/icons/icon-512x512.png'
]

// 运行时缓存的 URL 模式
const RUNTIME_CACHE_PATTERNS = [
  /^https:\/\/fonts\.googleapis\.com/,
  /^https:\/\/fonts\.gstatic\.com/,
  /^https:\/\/cdnjs\.cloudflare\.com/
]

// 不缓存的 URL 模式
const NO_CACHE_PATTERNS = [
  /\/api\//,
  /\/socket\.io\//,
  /\.hot-update\./
]

/**
 * 安装事件 - 缓存静态资源
 */
self.addEventListener('install', event => {
  console.log('[SW] Install event')
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('[SW] Caching static resources')
        return cache.addAll(STATIC_CACHE_URLS)
      })
      .then(() => {
        console.log('[SW] Static resources cached')
        return self.skipWaiting()
      })
      .catch(error => {
        console.error('[SW] Failed to cache static resources:', error)
      })
  )
})

/**
 * 激活事件 - 清理旧缓存
 */
self.addEventListener('activate', event => {
  console.log('[SW] Activate event')
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME && cacheName !== RUNTIME_CACHE) {
              console.log('[SW] Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        console.log('[SW] Old caches cleaned')
        return self.clients.claim()
      })
  )
})

/**
 * 获取事件 - 处理网络请求
 */
self.addEventListener('fetch', event => {
  const { request } = event
  const url = new URL(request.url)
  
  // 跳过非 GET 请求
  if (request.method !== 'GET') {
    return
  }
  
  // 跳过不需要缓存的请求
  if (NO_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname))) {
    return
  }
  
  // 处理导航请求
  if (request.mode === 'navigate') {
    event.respondWith(handleNavigate(request))
    return
  }
  
  // 处理静态资源请求
  if (STATIC_CACHE_URLS.includes(url.pathname) || url.pathname === '/') {
    event.respondWith(handleStatic(request))
    return
  }
  
  // 处理运行时缓存
  if (RUNTIME_CACHE_PATTERNS.some(pattern => pattern.test(request.url))) {
    event.respondWith(handleRuntime(request))
    return
  }
})

/**
 * 处理导航请求
 */
async function handleNavigate(request) {
  try {
    // 尝试网络请求
    const response = await fetch(request)
    return response
  } catch (error) {
    console.log('[SW] Network failed for navigation, serving cached index.html')
    // 网络失败时返回缓存的 index.html
    const cache = await caches.open(CACHE_NAME)
    return cache.match('/index.html') || cache.match('/')
  }
}

/**
 * 处理静态资源请求 - 缓存优先策略
 */
async function handleStatic(request) {
  const cache = await caches.open(CACHE_NAME)
  const cached = await cache.match(request)
  
  if (cached) {
    console.log('[SW] Serving from cache:', request.url)
    return cached
  }
  
  try {
    console.log('[SW] Fetching from network:', request.url)
    const response = await fetch(request)
    
    if (response.status === 200) {
      cache.put(request, response.clone())
    }
    
    return response
  } catch (error) {
    console.error('[SW] Failed to fetch static resource:', error)
    throw error
  }
}

/**
 * 处理运行时缓存 - 网络优先策略
 */
async function handleRuntime(request) {
  const cache = await caches.open(RUNTIME_CACHE)
  
  try {
    console.log('[SW] Fetching runtime resource:', request.url)
    const response = await fetch(request)
    
    if (response.status === 200) {
      cache.put(request, response.clone())
    }
    
    return response
  } catch (error) {
    console.log('[SW] Network failed, serving from runtime cache:', request.url)
    const cached = await cache.match(request)
    
    if (cached) {
      return cached
    }
    
    throw error
  }
}

/**
 * 消息事件 - 处理来自主线程的消息
 */
self.addEventListener('message', event => {
  const { type, payload } = event.data
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
      
    case 'GET_VERSION':
      event.ports[0].postMessage({ version: CACHE_NAME })
      break
      
    case 'CLEAR_CACHE':
      clearAllCaches().then(() => {
        event.ports[0].postMessage({ success: true })
      }).catch(error => {
        event.ports[0].postMessage({ success: false, error: error.message })
      })
      break
      
    default:
      console.log('[SW] Unknown message type:', type)
  }
})

/**
 * 清理所有缓存
 */
async function clearAllCaches() {
  const cacheNames = await caches.keys()
  await Promise.all(
    cacheNames.map(cacheName => caches.delete(cacheName))
  )
  console.log('[SW] All caches cleared')
}

/**
 * 推送事件 - 处理推送通知
 */
self.addEventListener('push', event => {
  console.log('[SW] Push event received')
  
  if (!event.data) {
    return
  }
  
  const data = event.data.json()
  const options = {
    body: data.body || '您有新的会议邀请',
    icon: '/static/icons/icon-192x192.png',
    badge: '/static/icons/badge-72x72.png',
    tag: data.tag || 'meeting-notification',
    requireInteraction: true,
    actions: [
      {
        action: 'join',
        title: '加入会议',
        icon: '/static/icons/action-join.png'
      },
      {
        action: 'dismiss',
        title: '稍后处理',
        icon: '/static/icons/action-dismiss.png'
      }
    ],
    data: data
  }
  
  event.waitUntil(
    self.registration.showNotification(data.title || 'MeetSync', options)
  )
})

/**
 * 通知点击事件
 */
self.addEventListener('notificationclick', event => {
  console.log('[SW] Notification click event')
  
  event.notification.close()
  
  const { action, data } = event
  
  if (action === 'join' && data?.roomId) {
    // 打开会议室
    event.waitUntil(
      clients.openWindow(`/pages/room/index?roomId=${data.roomId}`)
    )
  } else if (action === 'dismiss') {
    // 不做任何操作，只关闭通知
    return
  } else {
    // 默认行为：打开应用
    event.waitUntil(
      clients.openWindow('/')
    )
  }
})

console.log('[SW] Service Worker loaded')
