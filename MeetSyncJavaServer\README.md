# MeetSync Java Server

MeetSync Java后端服务，使用Spring Boot + JPA + MySQL实现用户管理和房间管理功能。

## 功能特性

### 用户管理
- 用户注册和登录
- JWT令牌认证
- 基于角色的权限控制 (Guest, User, Premium, Admin)
- 用户资料管理
- 密码修改

### 房间管理
- 房间创建、查询、更新、删除
- 公开/私有房间
- 房间密码保护
- 基于角色的房间访问控制
- 用户房间权限管理

### 权限系统
- **Guest**: 仅观看 (view_only)
- **User**: 音频通话 (audio_only)
- **Premium**: 音频+视频通话 (video_audio)
- **Admin**: 完全访问权限 (full_access)

## 技术栈

- **Spring Boot 3.2.0** - 主框架
- **Spring Security** - 安全认证
- **Spring Data JPA** - 数据访问层
- **MySQL 5.7** - 数据库 (已优化适配)
- **JWT** - 令牌认证
- **BCrypt** - 密码加密
- **Lombok** - 简化Java代码，减少样板代码
- **Swagger/OpenAPI 3** - API文档和在线测试
- **Maven** - 依赖管理

## 环境要求

- Java 17+
- Maven 3.6+ (或使用内置的Maven Wrapper)
- MySQL 5.7+ (已针对MySQL 5.7和中国时区优化)
- Git 2.0+ (用于版本控制)

## 快速开始

### 0. Git仓库初始化（首次使用）

如果这是一个新的项目副本，首先初始化Git仓库：

```bash
# 初始化Git仓库
.\init-git.bat

# 或手动初始化
git init
git add .
git commit -m "初始提交"
```

详细的Git使用指南请参考：[GIT_GUIDE.md](./GIT_GUIDE.md)

### 1. 环境准备

**重要：** 如果系统中没有安装Java和Maven，请先参考 [SETUP_GUIDE.md](./SETUP_GUIDE.md) 安装Java开发环境。

检查环境：
```bash
java -version    # 需要Java 17+
mvn -version     # 需要Maven 3.6+
```

### 1. 数据库准备

确保MySQL服务正在运行，并且已经创建了数据库：

```sql
CREATE DATABASE IF NOT EXISTS mediasoup_rooms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

数据库表结构会在应用启动时自动创建（通过现有的Node.js服务端已创建的表）。

### 3. 配置环境变量

可以通过环境变量配置数据库连接：

```bash
# Linux/Mac
export DB_HOST=localhost
export DB_PORT=3306
export DB_NAME=mediasoup_rooms
export DB_USER=root
export DB_PASSWORD=qwertyui
export JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
```

```powershell
# Windows PowerShell
$env:DB_HOST="localhost"
$env:DB_PORT="3306"
$env:DB_NAME="mediasoup_rooms"
$env:DB_USER="root"
$env:DB_PASSWORD="qwertyui"
$env:JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
```

### 4. 编译和运行

#### 方法一：使用Maven（需要预先安装）
```bash
# 进入项目目录
cd MeetSyncJavaServer

# 编译项目
mvn clean compile

# 运行应用
mvn spring-boot:run
```

#### 方法二：使用Maven Wrapper（推荐，无需预安装Maven）
```bash
# Windows
.\mvnw.cmd clean compile
.\mvnw.cmd spring-boot:run

# Linux/Mac
./mvnw clean compile
./mvnw spring-boot:run

# 使用不同环境配置
.\mvnw.cmd spring-boot:run -Dspring.profiles.active=dev    # 开发环境
.\mvnw.cmd spring-boot:run -Dspring.profiles.active=prod   # 生产环境
```

#### 方法三：打包后运行
```bash
# 打包
mvn clean package
# 或使用wrapper
.\mvnw.cmd clean package

# 运行jar文件
java -jar target/MeetSyncJavaServer-1.0-SNAPSHOT.jar
```

### 5. 验证服务

服务启动后，可以通过以下方式验证：

#### API测试
```bash
# 健康检查
curl http://localhost:8080/api/health

# Ping测试
curl http://localhost:8080/api/ping
```

#### Swagger API文档
访问在线API文档和测试界面：
- **Swagger UI**: http://localhost:8080/api/swagger-ui.html
- **API文档**: http://localhost:8080/api/v3/api-docs

详细使用说明请参考：[SWAGGER_GUIDE.md](./SWAGGER_GUIDE.md)

## API 端点

### 认证 API

- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/profile` - 获取用户信息
- `PUT /api/auth/profile` - 更新用户信息
- `PUT /api/auth/change-password` - 修改密码
- `POST /api/auth/validate` - 验证令牌

### 房间 API

- `GET /api/rooms/public` - 获取公开房间列表
- `GET /api/rooms/my-rooms` - 获取我的房间列表
- `GET /api/rooms/accessible` - 获取可访问的房间列表
- `GET /api/rooms/{roomId}` - 获取房间详情
- `POST /api/rooms` - 创建房间
- `PUT /api/rooms/{roomId}` - 更新房间
- `DELETE /api/rooms/{roomId}` - 删除房间
- `POST /api/rooms/{roomId}/check-access` - 检查房间访问权限
- `GET /api/rooms/search?name={name}` - 搜索房间

### 健康检查 API

- `GET /api/health` - 健康检查
- `GET /api/ping` - Ping测试
- `GET /api/` - 服务信息

## 配置说明

所有配置都整合在 `src/main/resources/application.yml` 中，支持环境变量覆盖：

### 核心配置
```yaml
# 服务器配置
server:
  port: ${SERVER_PORT:8080}

# 数据库配置 (MySQL 5.7优化)
spring:
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:mediasoup_rooms}
    username: ${DB_USER:root}
    password: ${DB_PASSWORD:qwertyui}

# JWT配置
jwt:
  secret: ${JWT_SECRET:your-jwt-secret}
  expiration: ${JWT_EXPIRES_IN:86400000}
```

### 环境变量支持
所有配置都支持环境变量覆盖，主要变量：

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `SERVER_PORT` | 8080 | 服务端口 |
| `DB_HOST` | localhost | 数据库主机 |
| `DB_PORT` | 3306 | 数据库端口 |
| `DB_NAME` | mediasoup_rooms | 数据库名 |
| `DB_USER` | root | 数据库用户 |
| `DB_PASSWORD` | qwertyui | 数据库密码 |
| `JWT_SECRET` | (默认值) | JWT密钥 |
| `LOG_LEVEL_APP` | INFO | 应用日志级别 |

### 环境配置文件
支持多环境配置：
- **默认**: 生产就绪的MySQL 5.7配置
- **dev**: 开发环境 (`-Dspring.profiles.active=dev`)
- **prod**: 生产环境 (`-Dspring.profiles.active=prod`)
- **test**: 测试环境 (使用H2内存数据库)

## 开发说明

### Git工作流程

本项目使用Git进行版本控制，推荐的工作流程：

```bash
# 1. 克隆仓库
git clone [仓库URL]
cd MeetSyncJavaServer

# 2. 创建功能分支
git checkout -b feature/your-feature-name

# 3. 开发和提交
git add .
git commit -m "feat: 添加新功能"

# 4. 推送分支
git push origin feature/your-feature-name

# 5. 创建Pull Request
```

详细的Git使用指南请参考：[GIT_GUIDE.md](./GIT_GUIDE.md)

### 项目结构

```
src/main/java/org/meetsync/
├── MeetSyncApplication.java     # 主应用类
├── config/                      # 配置类
│   └── SecurityConfig.java      # 安全配置
├── controller/                  # 控制器层
│   ├── AuthController.java      # 认证控制器
│   ├── RoomController.java      # 房间控制器
│   └── HealthController.java    # 健康检查控制器
├── dto/                         # 数据传输对象
├── entity/                      # 实体类
├── repository/                  # 数据访问层
├── service/                     # 业务逻辑层
├── security/                    # 安全相关
└── util/                        # 工具类
```

### 运行测试

```bash
mvn test
```

## 与Node.js服务端的关系

- **Node.js服务端**: 作为信令服务器，处理WebRTC相关的实时通信
- **Java服务端**: 处理用户管理、房间管理、权限控制等业务逻辑
- 两个服务端共享同一个MySQL数据库
- 前端可以同时与两个服务端通信

## 注意事项

1. 确保MySQL数据库已经通过Node.js服务端初始化了表结构
2. JWT密钥在生产环境中必须更改
3. 数据库连接信息请根据实际环境配置
4. 服务默认运行在8080端口，确保端口未被占用

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否运行
   - 验证数据库连接信息
   - 确保数据库已创建

2. **端口冲突**
   - 修改application.yml中的server.port配置

3. **JWT令牌问题**
   - 检查JWT密钥配置
   - 验证令牌格式和有效期

### 日志查看

应用日志会输出到控制台，可以通过日志级别控制详细程度：

```yaml
logging:
  level:
    org.meetsync: DEBUG
```
