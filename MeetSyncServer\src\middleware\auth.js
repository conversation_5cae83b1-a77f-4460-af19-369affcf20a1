const jwt = require('jsonwebtoken')
const config = require('../config')
const User = require('../models/User')

// Verify JWT token
const verifyToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization
    const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

    if (!token) {
      return res.status(401).json({ error: 'Access token required' })
    }

    const decoded = jwt.verify(token, config.jwt.secret)
    const user = await User.findById(decoded.userId)

    if (!user) {
      return res.status(401).json({ error: 'Invalid token - user not found' })
    }

    req.user = user
    next()
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ error: 'Invalid token' })
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired' })
    }
    
    console.error('Auth middleware error:', error)
    return res.status(500).json({ error: 'Authentication error' })
  }
}

// Verify Socket.IO token
const verifySocketToken = async (socket, next) => {
  try {
    const token = socket.handshake.auth.token || socket.handshake.query.token

    if (!token) {
      return next(new Error('Authentication token required'))
    }

    const decoded = jwt.verify(token, config.jwt.secret)
    const user = await User.findById(decoded.userId)

    if (!user) {
      return next(new Error('Invalid token - user not found'))
    }

    socket.user = user
    next()
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return next(new Error('Invalid token'))
    }
    if (error.name === 'TokenExpiredError') {
      return next(new Error('Token expired'))
    }
    
    console.error('Socket auth error:', error)
    return next(new Error('Authentication error'))
  }
}

// Check if user has required role
const requireRole = (requiredRole) => {
  const roleHierarchy = { guest: 0, user: 1, premium: 2, admin: 3 }
  
  return (req, res, next) => {
    const userRoleLevel = roleHierarchy[req.user.role] || 0
    const requiredRoleLevel = roleHierarchy[requiredRole] || 0
    
    if (userRoleLevel < requiredRoleLevel) {
      return res.status(403).json({ 
        error: `Access denied. Required role: ${requiredRole}, your role: ${req.user.role}` 
      })
    }
    
    next()
  }
}

// Check if user has permission for specific action
const requirePermission = (action) => {
  return async (req, res, next) => {
    try {
      const roomId = req.params.roomId || req.body.room_id
      
      if (!roomId) {
        return res.status(400).json({ error: 'Room ID required' })
      }

      const accessInfo = await req.user.canAccessRoom(roomId)
      
      if (!accessInfo.canAccess) {
        return res.status(403).json({ error: accessInfo.reason })
      }

      const permissionLevels = {
        view_only: ['view'],
        audio_only: ['view', 'audio'],
        video_audio: ['view', 'audio', 'video'],
        full_access: ['view', 'audio', 'video', 'screen', 'manage']
      }

      const userPermissions = permissionLevels[accessInfo.permission] || []
      
      if (!userPermissions.includes(action)) {
        return res.status(403).json({ 
          error: `Permission denied. Required: ${action}, your permission: ${accessInfo.permission}` 
        })
      }

      req.roomAccess = accessInfo
      next()
    } catch (error) {
      console.error('Permission check error:', error)
      return res.status(500).json({ error: 'Permission check failed' })
    }
  }
}

// Optional authentication (for public endpoints that can work with or without auth)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization
    const token = authHeader && authHeader.split(' ')[1]

    if (token) {
      const decoded = jwt.verify(token, config.jwt.secret)
      const user = await User.findById(decoded.userId)
      req.user = user
    }

    next()
  } catch (error) {
    // Ignore auth errors for optional auth
    next()
  }
}

// Generate JWT token
const generateToken = (user) => {
  return jwt.sign(
    { 
      userId: user.id, 
      username: user.username, 
      role: user.role 
    },
    config.jwt.secret,
    { expiresIn: config.jwt.expiresIn }
  )
}

// Refresh token
const refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body

    if (!refreshToken) {
      return res.status(401).json({ error: 'Refresh token required' })
    }

    const decoded = jwt.verify(refreshToken, config.jwt.secret)
    const user = await User.findById(decoded.userId)

    if (!user) {
      return res.status(401).json({ error: 'Invalid refresh token' })
    }

    const newToken = generateToken(user)
    
    res.json({
      token: newToken,
      user: user.toJSON()
    })
  } catch (error) {
    console.error('Token refresh error:', error)
    res.status(401).json({ error: 'Invalid refresh token' })
  }
}

module.exports = {
  verifyToken,
  verifySocketToken,
  requireRole,
  requirePermission,
  optionalAuth,
  generateToken,
  refreshToken
}
