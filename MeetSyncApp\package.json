{"name": "meetsync-app", "version": "1.0.0", "description": "MediaSoup 视频会议移动端应用", "main": "main.js", "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "dev": "node scripts/dev.js", "start": "npm run dev", "build:app-plus": "uni build --platform app-plus", "build:custom": "uni build --platform custom", "build:h5": "uni build --platform h5", "build:mp-alipay": "uni build --platform mp-alipay", "build:mp-baidu": "uni build --platform mp-baidu", "build:mp-weixin": "uni build --platform mp-weixin", "build:mp-toutiao": "uni build --platform mp-toutiao", "build:mp-qq": "uni build --platform mp-qq", "dev:app-plus": "uni --platform app-plus", "dev:custom": "uni --platform custom", "dev:h5": "uni --platform h5", "dev:mp-alipay": "uni --platform mp-alipay", "dev:mp-baidu": "uni --platform mp-baidu", "dev:mp-weixin": "uni --platform mp-weixin", "dev:mp-toutiao": "uni --platform mp-toutiao", "dev:mp-qq": "uni --platform mp-qq", "info": "uni info", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --ext .js,.vue src --fix", "generate-icons": "echo '请在浏览器中打开 scripts/generate-icons.html 生成图标'", "enable-pwa": "node scripts/enable-pwa.js", "test:backend": "echo '请在浏览器中访问测试页面: /pages/test/java-backend'", "start:java": "echo '请先启动Java后端服务 (端口8080) 和NodeJS媒体服务器 (端口3016)'"}, "dependencies": {"socket.io-client": "^4.7.5", "mediasoup-client": "^3.7.7"}, "devDependencies": {}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}