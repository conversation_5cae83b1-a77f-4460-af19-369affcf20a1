package org.meetsync.service;

import org.meetsync.dto.UserRegistrationDto;
import org.meetsync.dto.UserResponseDto;
import org.meetsync.entity.User;
import org.meetsync.entity.UserRole;
import org.meetsync.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * Register a new user
     */
    public UserResponseDto registerUser(UserRegistrationDto registrationDto) {
        logger.info("Registering new user: {}", registrationDto.getUsername());

        // Check if username already exists
        if (userRepository.existsByUsername(registrationDto.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }

        // Check if email already exists
        if (userRepository.existsByEmail(registrationDto.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }

        // Create new user
        User user = new User();
        user.setUsername(registrationDto.getUsername());
        user.setEmail(registrationDto.getEmail());
        user.setPasswordHash(passwordEncoder.encode(registrationDto.getPassword()));
        
        // 安全：强制使用默认角色，不允许前端指定角色
        user.setRole(UserRole.USER); // 所有新注册用户默认为USER角色

        user.setIsActive(true);

        User savedUser = userRepository.save(user);
        logger.info("User registered successfully: {}", savedUser.getUsername());

        return new UserResponseDto(savedUser);
    }

    /**
     * Authenticate user
     */
    public Optional<User> authenticateUser(String username, String password) {
        logger.debug("Authenticating user: {}", username);

        Optional<User> userOpt = userRepository.findByUsernameOrEmail(username);
        
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            if (passwordEncoder.matches(password, user.getPasswordHash())) {
                // Update last login
                updateLastLogin(user.getId());
                logger.info("User authenticated successfully: {}", user.getUsername());
                return Optional.of(user);
            }
        }

        logger.warn("Authentication failed for user: {}", username);
        return Optional.empty();
    }

    /**
     * Find user by ID
     */
    @Transactional(readOnly = true)
    public Optional<UserResponseDto> findById(Long id) {
        return userRepository.findById(id)
                .filter(User::getIsActive)
                .map(UserResponseDto::new);
    }

    /**
     * Find user entity by ID
     */
    @Transactional(readOnly = true)
    public Optional<User> findUserById(Long id) {
        return userRepository.findById(id)
                .filter(User::getIsActive);
    }

    /**
     * Find user by username
     */
    @Transactional(readOnly = true)
    public Optional<UserResponseDto> findByUsername(String username) {
        return userRepository.findByUsername(username)
                .filter(User::getIsActive)
                .map(UserResponseDto::new);
    }

    /**
     * Find user entity by username
     */
    @Transactional(readOnly = true)
    public Optional<User> findUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .filter(User::getIsActive);
    }

    /**
     * Find user by email
     */
    @Transactional(readOnly = true)
    public Optional<UserResponseDto> findByEmail(String email) {
        return userRepository.findByEmail(email)
                .filter(User::getIsActive)
                .map(UserResponseDto::new);
    }

    /**
     * Find user by username or email
     */
    @Transactional(readOnly = true)
    public Optional<User> findByUsernameOrEmail(String identifier) {
        return userRepository.findByUsernameOrEmail(identifier);
    }

    /**
     * Get all active users
     */
    @Transactional(readOnly = true)
    public List<UserResponseDto> getAllActiveUsers() {
        return userRepository.findByIsActiveTrue()
                .stream()
                .map(UserResponseDto::new)
                .collect(Collectors.toList());
    }

    /**
     * Get users by role
     */
    @Transactional(readOnly = true)
    public List<UserResponseDto> getUsersByRole(UserRole role) {
        return userRepository.findByRoleAndIsActiveTrue(role)
                .stream()
                .map(UserResponseDto::new)
                .collect(Collectors.toList());
    }

    /**
     * Update user profile
     */
    public UserResponseDto updateUserProfile(Long userId, UserRegistrationDto updateDto) {
        logger.info("Updating user profile for user ID: {}", userId);

        User user = userRepository.findById(userId)
                .filter(User::getIsActive)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // Check if new username is taken by another user
        if (!user.getUsername().equals(updateDto.getUsername()) &&
            userRepository.existsByUsername(updateDto.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }

        // Check if new email is taken by another user
        if (!user.getEmail().equals(updateDto.getEmail()) &&
            userRepository.existsByEmail(updateDto.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }

        user.setUsername(updateDto.getUsername());
        user.setEmail(updateDto.getEmail());

        // 安全：普通用户不能修改自己的角色，只有管理员可以通过专门的接口修改用户角色
        // 角色修改应该通过单独的管理员接口实现

        User savedUser = userRepository.save(user);
        logger.info("User profile updated successfully: {}", savedUser.getUsername());

        return new UserResponseDto(savedUser);
    }

    /**
     * Change user password
     */
    public void changePassword(Long userId, String currentPassword, String newPassword) {
        logger.info("Changing password for user ID: {}", userId);

        User user = userRepository.findById(userId)
                .filter(User::getIsActive)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        if (!passwordEncoder.matches(currentPassword, user.getPasswordHash())) {
            throw new RuntimeException("当前密码错误");
        }

        user.setPasswordHash(passwordEncoder.encode(newPassword));
        userRepository.save(user);

        logger.info("Password changed successfully for user: {}", user.getUsername());
    }

    /**
     * Update last login time
     */
    public void updateLastLogin(Long userId) {
        userRepository.updateLastLogin(userId, LocalDateTime.now());
    }

    /**
     * Deactivate user
     */
    public void deactivateUser(Long userId) {
        logger.info("Deactivating user ID: {}", userId);
        userRepository.deactivateUser(userId);
    }

    /**
     * Check if username exists
     */
    @Transactional(readOnly = true)
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    /**
     * Check if email exists
     */
    @Transactional(readOnly = true)
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    /**
     * Count users by role
     */
    @Transactional(readOnly = true)
    public long countByRole(UserRole role) {
        return userRepository.countByRole(role);
    }
}
