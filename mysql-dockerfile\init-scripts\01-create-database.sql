-- MeetSync 数据库初始化脚本
-- 此脚本与 MeetSyncServer/src/database/init.sql 保持同步

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS mediasoup_rooms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE mediasoup_rooms;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('guest', 'user', 'premium', 'admin') DEFAULT 'user',
    avatar_url VARCHAR(255) DEFAULT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMES<PERSON>MP NULL,

    UNIQUE KEY idx_username (username(50)),
    UNIQUE KEY idx_email (email(100)),
    INDEX idx_role (role)
);

-- 创建房间表
CREATE TABLE IF NOT EXISTS rooms (
    id INT AUTO_INCREMENT PRIMARY KEY,
    room_id VARCHAR(100) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    creator_id INT NOT NULL,
    max_participants INT DEFAULT 10,
    is_public BOOLEAN DEFAULT TRUE,
    password_hash VARCHAR(255) DEFAULT NULL,
    required_role ENUM('guest', 'user', 'premium', 'admin') DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY idx_room_id (room_id(100)),
    INDEX idx_creator (creator_id),
    INDEX idx_public (is_public)
);

-- 创建用户房间权限表
CREATE TABLE IF NOT EXISTS user_room_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    room_id INT NOT NULL,
    permission ENUM('view_only', 'audio_only', 'video_audio', 'full_access') DEFAULT 'audio_only',
    granted_by INT NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_room (user_id, room_id),
    INDEX idx_user_room (user_id, room_id)
);

-- 创建用户会话表（JWT令牌管理）
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_sessions (user_id),
    INDEX idx_token_hash (token_hash(191)),
    INDEX idx_expires (expires_at)
);

-- 创建房间活动日志表
CREATE TABLE IF NOT EXISTS room_activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    room_id INT NOT NULL,
    user_id INT,
    action ENUM('join', 'leave', 'start_audio', 'stop_audio', 'start_video', 'stop_video', 'start_screen', 'stop_screen') NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),

    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_room_activity (room_id, timestamp),
    INDEX idx_user_activity (user_id, timestamp)
);

-- 插入默认管理员用户 (密码: admin123)
INSERT IGNORE INTO users (username, email, password_hash, role) VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- 插入示例用户
INSERT IGNORE INTO users (username, email, password_hash, role) VALUES
('user1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user'),
('premium1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'premium');

-- 插入示例房间
INSERT IGNORE INTO rooms (room_id, name, description, creator_id, required_role) VALUES
('123', 'Default Room', 'Default video conference room', 1, 'user');