/**
 * 应用全局配置
 */

// 环境配置
const ENV = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production'
}

// 当前环境
const currentEnv = process.env.NODE_ENV || ENV.DEVELOPMENT

const { getLocalIp } = require('../utils/sysutils')



// 服务器配置
const serverConfig = {
  [ENV.DEVELOPMENT]: {
    baseUrl: `https://${getLocalIp()}:8080/api`,
    wsUrl: `wss://${getLocalIp()}:8080/api/media-ws`,
    mediaApiUrl: `https://${getLocalIp()}:8080/api/media`,
    secure: false,
    // SSL 兼容性配置
    sslVerify: false,
    rejectUnauthorized: false,
    enableHttp2: false,
    enableQuic: false
  },
  [ENV.PRODUCTION]: {
    baseUrl: "https://" + (typeof window !== 'undefined' ? window.location.host : 'localhost') + "/api",
    wsUrl: "wss://" + (typeof window !== 'undefined' ? window.location.host : 'localhost') + "/media-ws",
    mediaApiUrl: "https://" + (typeof window !== 'undefined' ? window.location.host : 'localhost') + "/api/media",
    secure: true,
    // 生产环境 SSL 配置
    sslVerify: true,
    rejectUnauthorized: true,
    enableHttp2: true,
    enableQuic: false
  }
}

// WebRTC 配置
const webrtcConfig = {
  // ICE 服务器配置
  iceServers: [
    {
      urls: 'stun:stun.l.google.com:19302'
    },
    // 如果需要 TURN 服务器，在这里添加
    // {
    //   urls: 'turn:your-turn-server.com:3478',
    //   username: 'username',
    //   credential: 'password'
    // }
  ],
  
  // 媒体约束
  mediaConstraints: {
    audio: {
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true,
      sampleRate: 48000,
      channelCount: 1
    },
    video: {
      width: { ideal: 1280, max: 1920 },
      height: { ideal: 720, max: 1080 },
      frameRate: { ideal: 30, max: 60 },
      facingMode: 'user' // 默认前置摄像头
    },
    screen: {
      width: { ideal: 1920 },
      height: { ideal: 1080 },
      frameRate: { ideal: 15, max: 30 }
    }
  },
  
  // 移动端优化配置
  mobile: {
    video: {
      width: { ideal: 720, max: 1280 },
      height: { ideal: 480, max: 720 },
      frameRate: { ideal: 24, max: 30 }
    },
    // 低功耗模式
    lowPower: {
      video: {
        width: { ideal: 480, max: 720 },
        height: { ideal: 320, max: 480 },
        frameRate: { ideal: 15, max: 24 }
      }
    }
  }
}

// UI 配置
const uiConfig = {
  // 主题色彩
  colors: {
    primary: '#007bff',
    secondary: '#6c757d',
    success: '#28a745',
    danger: '#dc3545',
    warning: '#ffc107',
    info: '#17a2b8',
    light: '#f8f9fa',
    dark: '#343a40'
  },
  
  // 动画配置
  animation: {
    duration: 300,
    easing: 'ease-in-out'
  },
  
  // 响应式断点
  breakpoints: {
    xs: 0,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200
  }
}

// 权限配置
const permissionConfig = {
  // 权限级别
  levels: {
    VIEW_ONLY: 'view_only',
    AUDIO_ONLY: 'audio_only',
    VIDEO_AUDIO: 'video_audio',
    FULL_ACCESS: 'full_access'
  },
  
  // 权限对应的功能
  capabilities: {
    view_only: {
      canViewVideo: true,
      canHearAudio: true,
      canProduceAudio: false,
      canProduceVideo: false,
      canShareScreen: false
    },
    audio_only: {
      canViewVideo: true,
      canHearAudio: true,
      canProduceAudio: true,
      canProduceVideo: false,
      canShareScreen: false
    },
    video_audio: {
      canViewVideo: true,
      canHearAudio: true,
      canProduceAudio: true,
      canProduceVideo: true,
      canShareScreen: false
    },
    full_access: {
      canViewVideo: true,
      canHearAudio: true,
      canProduceAudio: true,
      canProduceVideo: true,
      canShareScreen: true
    }
  }
}

// 存储配置
const storageConfig = {
  keys: {
    AUTH_TOKEN: 'auth_token',
    USER_INFO: 'user',
    ROOM_SETTINGS: 'room_settings',
    DEVICE_SETTINGS: 'device_settings',
    APP_SETTINGS: 'app_settings'
  },
  
  // 过期时间（毫秒）
  expiration: {
    AUTH_TOKEN: 7 * 24 * 60 * 60 * 1000, // 7天
    USER_INFO: 7 * 24 * 60 * 60 * 1000,   // 7天
    ROOM_SETTINGS: 30 * 24 * 60 * 60 * 1000, // 30天
    DEVICE_SETTINGS: 30 * 24 * 60 * 60 * 1000, // 30天
    APP_SETTINGS: 30 * 24 * 60 * 60 * 1000 // 30天
  }
}

// 错误配置
const errorConfig = {
  // 错误类型
  types: {
    NETWORK_ERROR: 'NETWORK_ERROR',
    AUTH_ERROR: 'AUTH_ERROR',
    PERMISSION_ERROR: 'PERMISSION_ERROR',
    MEDIA_ERROR: 'MEDIA_ERROR',
    WEBRTC_ERROR: 'WEBRTC_ERROR'
  },
  
  // 错误消息映射
  messages: {
    NETWORK_ERROR: '网络连接失败，请检查网络设置',
    AUTH_ERROR: '认证失败，请重新登录',
    PERMISSION_ERROR: '权限不足，无法执行此操作',
    MEDIA_ERROR: '媒体设备访问失败，请检查权限设置',
    WEBRTC_ERROR: 'WebRTC连接失败，请稍后重试'
  }
}

// 日志配置
const logConfig = {
  level: currentEnv === ENV.DEVELOPMENT ? 'debug' : 'error',
  enableConsole: currentEnv === ENV.DEVELOPMENT,
  enableRemote: currentEnv === ENV.PRODUCTION,
  maxLogSize: 1000 // 最大日志条数
}

// 性能配置
const performanceConfig = {
  // 网络检测
  networkCheck: {
    enabled: true,
    interval: 30000, // 30秒检测一次
    timeout: 5000    // 5秒超时
  },
  
  // 内存管理
  memory: {
    maxCacheSize: 50 * 1024 * 1024, // 50MB
    cleanupInterval: 5 * 60 * 1000   // 5分钟清理一次
  },
  
  // 视频质量自适应
  adaptiveQuality: {
    enabled: true,
    checkInterval: 10000, // 10秒检测一次
    thresholds: {
      excellent: { rtt: 50, packetLoss: 0.01 },
      good: { rtt: 150, packetLoss: 0.03 },
      poor: { rtt: 300, packetLoss: 0.05 }
    }
  }
}

// 导出配置
export default {
  env: currentEnv,
  server: serverConfig[currentEnv],
  webrtc: webrtcConfig,
  ui: uiConfig,
  permission: permissionConfig,
  storage: storageConfig,
  error: errorConfig,
  log: logConfig,
  performance: performanceConfig
}

// 导出常量
export {
  ENV,
  serverConfig,
  webrtcConfig,
  uiConfig,
  permissionConfig,
  storageConfig,
  errorConfig,
  logConfig,
  performanceConfig
}
