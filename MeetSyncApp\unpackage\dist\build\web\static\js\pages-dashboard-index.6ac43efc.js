(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-dashboard-index"],{"5b69":function(t,a,e){"use strict";e("6a54");var o=e("f5bd").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var n=o(e("2634")),s=o(e("2fdc"));e("0c26"),e("01a2"),e("e39c");var i=o(e("3822")),r=o(e("b013")),c={name:"Dashboard",data:function(){return{user:null,activeTab:"public",loading:!1,creating:!1,joining:!1,publicRooms:[],myRooms:[],showCreateModal:!1,showPasswordModal:!1,selectedRoom:null,roomPassword:"",createForm:{name:"",description:"",hasPassword:!1,password:""}}},computed:{currentRooms:function(){return"public"===this.activeTab?this.publicRooms:this.myRooms},canCreateRoom:function(){return this.createForm.name.trim()&&(!this.createForm.hasPassword||this.createForm.password.trim())}},onLoad:function(){this.user=i.default.getCurrentUser(),this.user?this.loadRooms():uni.reLaunch({url:"/pages/auth/login"})},onShow:function(){this.loadRooms()},methods:{switchTab:function(t){this.activeTab=t,this.loadRooms()},loadRooms:function(){var t=this;return(0,s.default)((0,n.default)().mark((function a(){var e,o;return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.loading=!0,a.prev=1,"public"!==t.activeTab){a.next=9;break}return a.next=5,r.default.getPublicRooms();case 5:e=a.sent,t.publicRooms=e.rooms||[],a.next=13;break;case 9:return a.next=11,r.default.getMyRooms();case 11:o=a.sent,t.myRooms=o.rooms||[];case 13:a.next=19;break;case 15:a.prev=15,a.t0=a["catch"](1),console.error("加载房间失败:",a.t0),uni.showToast({title:"加载失败",icon:"error"});case 19:return a.prev=19,t.loading=!1,a.finish(19);case 22:case"end":return a.stop()}}),a,null,[[1,15,19,22]])})))()},joinRoom:function(t){t.has_password?(this.selectedRoom=t,this.roomPassword="",this.showPasswordModal=!0):this.navigateToRoom(t.room_id)},confirmJoinRoom:function(){var t=this;return(0,s.default)((0,n.default)().mark((function a(){return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.roomPassword.trim()){a.next=2;break}return a.abrupt("return");case 2:t.joining=!0;try{t.navigateToRoom(t.selectedRoom.room_id,t.roomPassword)}catch(e){uni.showToast({title:"密码错误",icon:"error"})}finally{t.joining=!1,t.closePasswordModal()}case 4:case"end":return a.stop()}}),a)})))()},navigateToRoom:function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;uni.setStorageSync("joinRoomId",t),a&&uni.setStorageSync("joinRoomPassword",a),uni.navigateTo({url:"/pages/room/index?roomId=".concat(t)})},handleCreateRoom:function(){var t=this;return(0,s.default)((0,n.default)().mark((function a(){var e,o,s;return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.canCreateRoom){a.next=2;break}return a.abrupt("return");case 2:return t.creating=!0,a.prev=3,e=t.generateRoomId(),o={room_id:e,name:t.createForm.name.trim(),description:t.createForm.description.trim(),max_participants:10,is_public:!0,password:t.createForm.hasPassword?t.createForm.password.trim():null,required_role:"user"},a.next=8,r.default.createRoom(o);case 8:s=a.sent,uni.showToast({title:"创建成功",icon:"success"}),t.closeCreateModal(),t.resetCreateForm(),t.loadRooms(),setTimeout((function(){t.navigateToRoom(s.room.room_id,o.password)}),1e3),a.next=20;break;case 16:a.prev=16,a.t0=a["catch"](3),console.error("创建房间失败:",a.t0),uni.showToast({title:a.t0.message||"创建失败",icon:"error"});case 20:return a.prev=20,t.creating=!1,a.finish(20);case 23:case"end":return a.stop()}}),a,null,[[3,16,20,23]])})))()},generateRoomId:function(){for(var t="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ",a="",e=0;e<6;e++)a+=t.charAt(Math.floor(Math.random()*t.length));return a},onPasswordCheckboxChange:function(t){this.createForm.hasPassword=t.detail.value},closeCreateModal:function(){this.showCreateModal=!1},closePasswordModal:function(){this.showPasswordModal=!1,this.selectedRoom=null,this.roomPassword=""},resetCreateForm:function(){this.createForm={name:"",description:"",hasPassword:!1,password:""}},handleLogout:function(){uni.showModal({title:"确认退出",content:"确定要退出登录吗？",success:function(t){t.confirm&&i.default.logout()}})},getRoleText:function(t){return{admin:"管理员",user:"用户",guest:"游客"}[t]||t},formatTime:function(t){var a=new Date(t),e=new Date,o=e-a;return o<6e4?"刚刚":o<36e5?"".concat(Math.floor(o/6e4),"分钟前"):o<864e5?"".concat(Math.floor(o/36e5),"小时前"):a.toLocaleDateString()}}};a.default=c},"780a":function(t,a,e){"use strict";e.d(a,"b",(function(){return o})),e.d(a,"c",(function(){return n})),e.d(a,"a",(function(){}));var o=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{staticClass:"dashboard-container"},[e("v-uni-view",{staticClass:"header"},[e("v-uni-view",{staticClass:"user-info"},[e("v-uni-text",{staticClass:"username"},[t._v(t._s(t.user.username))]),e("v-uni-text",{staticClass:"user-role",class:t.user.role},[t._v(t._s(t.getRoleText(t.user.role)))])],1),e("v-uni-button",{staticClass:"logout-btn",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.handleLogout.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"logout-icon"},[t._v("🚪")])],1)],1),e("v-uni-view",{staticClass:"tabs"},[e("v-uni-view",{staticClass:"tab-item",class:{active:"public"===t.activeTab},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.switchTab("public")}}},[t._v("公共房间")]),t.user.isGuest?t._e():e("v-uni-view",{staticClass:"tab-item",class:{active:"my"===t.activeTab},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.switchTab("my")}}},[t._v("我的房间")])],1),t.user.isGuest?t._e():e("v-uni-view",{staticClass:"create-room-section"},[e("v-uni-button",{staticClass:"btn btn-primary create-btn",attrs:{disabled:"guest"===t.user.role},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.showCreateModal=!0}}},[e("v-uni-text",{staticClass:"create-icon"},[t._v("➕")]),t._v("创建房间")],1)],1),e("v-uni-view",{staticClass:"room-list"},[t.loading?e("v-uni-view",{staticClass:"loading"},[e("v-uni-text",[t._v("加载中...")])],1):0===t.currentRooms.length?e("v-uni-view",{staticClass:"empty-state"},[e("v-uni-text",{staticClass:"empty-icon"},[t._v("📭")]),e("v-uni-text",{staticClass:"empty-text"},[t._v(t._s("public"===t.activeTab?"暂无公共房间":"您还没有创建房间"))])],1):e("v-uni-view",{staticClass:"rooms"},t._l(t.currentRooms,(function(a){return e("v-uni-view",{key:a.id,staticClass:"room-card",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.joinRoom(a)}}},[e("v-uni-view",{staticClass:"room-header"},[e("v-uni-text",{staticClass:"room-name"},[t._v(t._s(a.name))]),e("v-uni-view",{staticClass:"room-status",class:{active:a.isActive}},[e("v-uni-text",{staticClass:"status-dot"}),e("v-uni-text",{staticClass:"status-text"},[t._v(t._s(a.isActive?"活跃":"空闲"))])],1)],1),e("v-uni-view",{staticClass:"room-info"},[e("v-uni-text",{staticClass:"room-id"},[t._v("房间ID: "+t._s(a.room_id))]),e("v-uni-text",{staticClass:"room-participants"},[t._v("👥 "+t._s(a.participantCount||0)+" 人")])],1),e("v-uni-view",{staticClass:"room-meta"},[e("v-uni-text",{staticClass:"room-creator"},[t._v("创建者: "+t._s(a.creator_name))]),e("v-uni-text",{staticClass:"room-time"},[t._v(t._s(t.formatTime(a.created_at)))])],1),a.has_password?e("v-uni-view",{staticClass:"room-password"},[e("v-uni-text",{staticClass:"password-icon"},[t._v("🔒")]),e("v-uni-text",[t._v("需要密码")])],1):t._e()],1)})),1)],1),t.showCreateModal?e("v-uni-view",{staticClass:"modal-overlay",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.closeCreateModal.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"modal",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a)}}},[e("v-uni-view",{staticClass:"modal-header"},[e("v-uni-text",{staticClass:"modal-title"},[t._v("创建房间")]),e("v-uni-button",{staticClass:"close-btn",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.closeCreateModal.apply(void 0,arguments)}}},[t._v("✕")])],1),e("v-uni-view",{staticClass:"modal-body"},[e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"label"},[t._v("房间名称")]),e("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入房间名称",maxlength:"50"},model:{value:t.createForm.name,callback:function(a){t.$set(t.createForm,"name",a)},expression:"createForm.name"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-text",{staticClass:"label"},[t._v("房间描述")]),e("v-uni-textarea",{staticClass:"form-textarea",attrs:{placeholder:"请输入房间描述（可选）",maxlength:"200"},model:{value:t.createForm.description,callback:function(a){t.$set(t.createForm,"description",a)},expression:"createForm.description"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-view",{staticClass:"checkbox-group"},[e("v-uni-checkbox",{attrs:{checked:t.createForm.hasPassword,color:"#007bff"},on:{change:function(a){arguments[0]=a=t.$handleEvent(a),t.onPasswordCheckboxChange.apply(void 0,arguments)}}}),e("v-uni-text",{staticClass:"checkbox-label"},[t._v("设置房间密码")])],1)],1),t.createForm.hasPassword?e("v-uni-view",{staticClass:"form-group"},[e("v-uni-input",{staticClass:"form-input",attrs:{type:"password",placeholder:"请输入房间密码",maxlength:"20"},model:{value:t.createForm.password,callback:function(a){t.$set(t.createForm,"password",a)},expression:"createForm.password"}})],1):t._e()],1),e("v-uni-view",{staticClass:"modal-footer"},[e("v-uni-button",{staticClass:"btn btn-secondary",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.closeCreateModal.apply(void 0,arguments)}}},[t._v("取消")]),e("v-uni-button",{staticClass:"btn btn-primary",attrs:{disabled:!t.canCreateRoom||t.creating},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.handleCreateRoom.apply(void 0,arguments)}}},[t._v(t._s(t.creating?"创建中...":"创建"))])],1)],1)],1):t._e(),t.showPasswordModal?e("v-uni-view",{staticClass:"modal-overlay",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.closePasswordModal.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"modal",on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a)}}},[e("v-uni-view",{staticClass:"modal-header"},[e("v-uni-text",{staticClass:"modal-title"},[t._v("输入房间密码")]),e("v-uni-button",{staticClass:"close-btn",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.closePasswordModal.apply(void 0,arguments)}}},[t._v("✕")])],1),e("v-uni-view",{staticClass:"modal-body"},[e("v-uni-text",{staticClass:"password-hint"},[t._v('房间 "'+t._s(t.selectedRoom&&t.selectedRoom.name||"未知")+'" 需要密码')]),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-input",{staticClass:"form-input",attrs:{type:"password",placeholder:"请输入房间密码"},model:{value:t.roomPassword,callback:function(a){t.roomPassword=a},expression:"roomPassword"}})],1)],1),e("v-uni-view",{staticClass:"modal-footer"},[e("v-uni-button",{staticClass:"btn btn-secondary",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.closePasswordModal.apply(void 0,arguments)}}},[t._v("取消")]),e("v-uni-button",{staticClass:"btn btn-primary",attrs:{disabled:!t.roomPassword.trim()||t.joining},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.confirmJoinRoom.apply(void 0,arguments)}}},[t._v(t._s(t.joining?"加入中...":"加入"))])],1)],1)],1):t._e()],1)},n=[]},"8ad2":function(t,a,e){var o=e("e64b");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=e("967d").default;n("67a11744",o,!0,{sourceMap:!1,shadowMode:!1})},"8d57":function(t,a,e){"use strict";e.r(a);var o=e("780a"),n=e("add8");for(var s in n)["default"].indexOf(s)<0&&function(t){e.d(a,t,(function(){return n[t]}))}(s);e("dd8d");var i=e("828b"),r=Object(i["a"])(n["default"],o["b"],o["c"],!1,null,"7504ae22",null,!1,o["a"],void 0);a["default"]=r.exports},add8:function(t,a,e){"use strict";e.r(a);var o=e("5b69"),n=e.n(o);for(var s in o)["default"].indexOf(s)<0&&function(t){e.d(a,t,(function(){return o[t]}))}(s);a["default"]=n.a},dd8d:function(t,a,e){"use strict";var o=e("8ad2"),n=e.n(o);n.a},e64b:function(t,a,e){var o=e("c86c");a=o(!1),a.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.dashboard-container[data-v-7504ae22]{min-height:100vh;background:#f8f9fa}.header[data-v-7504ae22]{background:#007bff;padding:%?30?% %?40?%;display:flex;justify-content:space-between;align-items:center;color:#fff}.user-info[data-v-7504ae22]{display:flex;flex-direction:column}.username[data-v-7504ae22]{font-size:%?36?%;font-weight:700;margin-bottom:%?8?%}.user-role[data-v-7504ae22]{font-size:%?24?%;padding:%?4?% %?12?%;border-radius:%?12?%;background:hsla(0,0%,100%,.2);align-self:flex-start}.user-role.admin[data-v-7504ae22]{background:#dc3545}.user-role.user[data-v-7504ae22]{background:#28a745}.user-role.guest[data-v-7504ae22]{background:#6c757d}.logout-btn[data-v-7504ae22]{background:hsla(0,0%,100%,.2);border:none;border-radius:50%;width:%?80?%;height:%?80?%;display:flex;align-items:center;justify-content:center}.logout-icon[data-v-7504ae22]{font-size:%?32?%}.tabs[data-v-7504ae22]{display:flex;background:#fff;margin:%?20?%;border-radius:%?12?%;padding:%?8?%;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.1)}.tab-item[data-v-7504ae22]{flex:1;text-align:center;padding:%?20?%;border-radius:%?8?%;font-size:%?28?%;color:#666;transition:all .3s}.tab-item.active[data-v-7504ae22]{background:#007bff;color:#fff}.create-room-section[data-v-7504ae22]{padding:0 %?20?% %?20?%}.create-btn[data-v-7504ae22]{width:100%;background:#28a745;color:#fff;border:none;border-radius:%?12?%;padding:%?25?%;font-size:%?32?%;display:flex;align-items:center;justify-content:center}.create-icon[data-v-7504ae22]{margin-right:%?12?%;font-size:%?28?%}.room-list[data-v-7504ae22]{padding:0 %?20?%}.loading[data-v-7504ae22], .empty-state[data-v-7504ae22]{text-align:center;padding:%?100?% %?40?%;color:#666}.empty-icon[data-v-7504ae22]{font-size:%?80?%;display:block;margin-bottom:%?20?%}.empty-text[data-v-7504ae22]{font-size:%?28?%}.rooms[data-v-7504ae22]{display:flex;flex-direction:column;gap:%?20?%}.room-card[data-v-7504ae22]{background:#fff;border-radius:%?16?%;padding:%?30?%;box-shadow:0 %?4?% %?12?% rgba(0,0,0,.1);transition:-webkit-transform .2s;transition:transform .2s;transition:transform .2s,-webkit-transform .2s}.room-card[data-v-7504ae22]:active{-webkit-transform:scale(.98);transform:scale(.98)}.room-header[data-v-7504ae22]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?20?%}.room-name[data-v-7504ae22]{font-size:%?32?%;font-weight:700;color:#333;flex:1}.room-status[data-v-7504ae22]{display:flex;align-items:center;font-size:%?24?%;color:#666}.status-dot[data-v-7504ae22]{width:%?12?%;height:%?12?%;border-radius:50%;background:#ccc;margin-right:%?8?%}.room-status.active .status-dot[data-v-7504ae22]{background:#28a745}.room-info[data-v-7504ae22]{display:flex;justify-content:space-between;margin-bottom:%?15?%;font-size:%?26?%;color:#666}.room-meta[data-v-7504ae22]{display:flex;justify-content:space-between;font-size:%?24?%;color:#999}.room-password[data-v-7504ae22]{margin-top:%?15?%;display:flex;align-items:center;font-size:%?24?%;color:#ffc107}.password-icon[data-v-7504ae22]{margin-right:%?8?%}\n/* 模态框样式 */.modal-overlay[data-v-7504ae22]{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.5);display:flex;align-items:center;justify-content:center;z-index:1000}.modal[data-v-7504ae22]{background:#fff;border-radius:%?16?%;margin:%?40?%;max-width:%?600?%;width:100%;max-height:80vh;overflow:hidden}.modal-header[data-v-7504ae22]{padding:%?30?%;border-bottom:%?2?% solid #eee;display:flex;justify-content:space-between;align-items:center}.modal-title[data-v-7504ae22]{font-size:%?32?%;font-weight:700}.close-btn[data-v-7504ae22]{background:none;border:none;font-size:%?32?%;color:#999;padding:0;width:%?60?%;height:%?60?%;display:flex;align-items:center;justify-content:center}.modal-body[data-v-7504ae22]{padding:%?30?%;max-height:60vh;overflow-y:auto}.form-group[data-v-7504ae22]{margin-bottom:%?30?%}.label[data-v-7504ae22]{display:block;font-size:%?28?%;color:#333;margin-bottom:%?12?%}.form-input[data-v-7504ae22], .form-textarea[data-v-7504ae22]{width:100%;height:auto;padding:%?20?%;border:%?2?% solid #ddd;border-radius:%?8?%;font-size:%?28?%;box-sizing:border-box}.form-textarea[data-v-7504ae22]{height:%?120?%;resize:none}.checkbox-group[data-v-7504ae22]{display:flex;align-items:center}.checkbox-label[data-v-7504ae22]{margin-left:%?12?%;font-size:%?28?%}.password-hint[data-v-7504ae22]{font-size:%?28?%;color:#666;margin-bottom:%?20?%;text-align:center}.modal-footer[data-v-7504ae22]{padding:%?30?%;border-top:%?2?% solid #eee;display:flex;gap:%?20?%}.modal-footer .btn[data-v-7504ae22]{flex:1;padding:%?20?%;border:none;border-radius:%?8?%;font-size:%?28?%}.btn-secondary[data-v-7504ae22]{background:#6c757d;color:#fff}.btn-primary[data-v-7504ae22]{background:#007bff;color:#fff}.btn[data-v-7504ae22]:disabled{opacity:.6}',""]),t.exports=a}}]);