{"version": 3, "file": "MediaSection.d.ts", "sourceRoot": "", "sources": ["../../../src/handlers/sdp/MediaSection.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EACX,aAAa,EACb,YAAY,EACZ,cAAc,EACd,QAAQ,EACR,kBAAkB,EAClB,MAAM,iBAAiB,CAAC;AACzB,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,gBAAgB,CAAC;AAC3D,OAAO,KAAK,EACX,SAAS,EACT,aAAa,EAIb,MAAM,qBAAqB,CAAC;AAC7B,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AAE3D,8BAAsB,YAAY;IAEjC,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,CAAC;IAErC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;gBAEvB,EACX,aAAa,EACb,aAAa,EACb,cAAc,EACd,KAAa,GACb,EAAE;QACF,aAAa,CAAC,EAAE,aAAa,CAAC;QAC9B,aAAa,CAAC,EAAE,YAAY,EAAE,CAAC;QAC/B,cAAc,CAAC,EAAE,cAAc,CAAC;QAChC,KAAK,EAAE,OAAO,CAAC;KACf;IA0CD,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,GAAG,IAAI;IAE1C,IAAI,GAAG,IAAI,MAAM,CAEhB;IAED,IAAI,MAAM,IAAI,OAAO,CAEpB;IAED,SAAS,IAAI,MAAM;IAInB,gBAAgB,CAAC,aAAa,EAAE,aAAa,GAAG,IAAI;IAKpD,KAAK,IAAI,IAAI;IAIb,QAAQ,CAAC,MAAM,IAAI,IAAI;IAEvB,OAAO,IAAI,IAAI;IAYf,KAAK,IAAI,IAAI;CAKb;AAED,qBAAa,kBAAmB,SAAQ,YAAY;gBACvC,EACX,aAAa,EACb,aAAa,EACb,cAAc,EACd,cAAc,EACd,kBAAkB,EAClB,KAAa,EACb,gBAAgB,EAChB,kBAAkB,EAClB,mBAAmB,EACnB,YAAY,GACZ,EAAE;QACF,aAAa,CAAC,EAAE,aAAa,CAAC;QAC9B,aAAa,CAAC,EAAE,YAAY,EAAE,CAAC;QAC/B,cAAc,CAAC,EAAE,cAAc,CAAC;QAChC,cAAc,CAAC,EAAE,cAAc,CAAC;QAChC,kBAAkB,CAAC,EAAE,kBAAkB,CAAC;QACxC,KAAK,CAAC,EAAE,OAAO,CAAC;QAChB,gBAAgB,EAAE,GAAG,CAAC;QACtB,kBAAkB,CAAC,EAAE,aAAa,CAAC;QACnC,mBAAmB,CAAC,EAAE,aAAa,CAAC;QACpC,YAAY,CAAC,EAAE,oBAAoB,CAAC;KACpC;IAkQD,WAAW,CAAC,IAAI,EAAE,QAAQ,GAAG,IAAI;IAsBjC,MAAM,IAAI,IAAI;IAId,mBAAmB,CAAC,SAAS,EAAE,wBAAwB,EAAE,GAAG,IAAI;CA6BhE;AAED,qBAAa,iBAAkB,SAAQ,YAAY;gBACtC,EACX,aAAa,EACb,aAAa,EACb,cAAc,EACd,cAAc,EACd,kBAAkB,EAClB,KAAa,EACb,GAAG,EACH,IAAI,EACJ,kBAAkB,EAClB,QAAQ,EACR,OAAO,EACP,kBAA0B,GAC1B,EAAE;QACF,aAAa,CAAC,EAAE,aAAa,CAAC;QAC9B,aAAa,CAAC,EAAE,YAAY,EAAE,CAAC;QAC/B,cAAc,CAAC,EAAE,cAAc,CAAC;QAChC,cAAc,CAAC,EAAE,cAAc,CAAC;QAChC,kBAAkB,CAAC,EAAE,kBAAkB,CAAC;QACxC,KAAK,CAAC,EAAE,OAAO,CAAC;QAChB,GAAG,EAAE,MAAM,CAAC;QACZ,IAAI,EAAE,SAAS,GAAG,aAAa,CAAC;QAChC,kBAAkB,CAAC,EAAE,aAAa,CAAC;QACnC,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,kBAAkB,CAAC,EAAE,OAAO,CAAC;KAC7B;IAyKD,WAAW,CAAC,IAAI,EAAE,QAAQ,GAAG,IAAI;IAKjC,MAAM,IAAI,IAAI;IAId,YAAY,CAAC,EACZ,kBAAkB,EAClB,QAAQ,EACR,OAAO,GACP,EAAE;QACF,kBAAkB,EAAE,aAAa,CAAC;QAClC,QAAQ,EAAE,MAAM,CAAC;QACjB,OAAO,EAAE,MAAM,CAAC;KAChB,GAAG,IAAI;IAgGR,kBAAkB,CAAC,EAClB,kBAAkB,GAClB,EAAE;QACF,kBAAkB,EAAE,aAAa,CAAC;KAClC,GAAG,IAAI;CAeR"}