<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MeetSync 图标生成器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007bff;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 2px solid #eee;
            border-radius: 8px;
            background: #fafafa;
        }
        .icon-item img {
            width: 64px;
            height: 64px;
            margin-bottom: 10px;
        }
        .icon-item .size {
            font-size: 12px;
            color: #666;
            font-weight: bold;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        .controls input, .controls select {
            margin: 5px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .controls button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .controls button:hover {
            background: #0056b3;
        }
        .download-section {
            margin-top: 30px;
            padding: 20px;
            background: #e7f3ff;
            border-radius: 8px;
        }
        .download-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .download-links a {
            display: block;
            padding: 8px 12px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            text-align: center;
            font-size: 14px;
        }
        .download-links a:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📹 MeetSync 图标生成器</h1>
        
        <div class="controls">
            <label>
                背景颜色: 
                <input type="color" id="bgColor" value="#007bff">
            </label>
            <label>
                图标颜色: 
                <input type="color" id="iconColor" value="#ffffff">
            </label>
            <label>
                图标类型: 
                <select id="iconType">
                    <option value="camera">📹 摄像头</option>
                    <option value="video">🎥 视频</option>
                    <option value="meeting">👥 会议</option>
                    <option value="phone">📞 电话</option>
                </select>
            </label>
            <button onclick="generateIcons()">生成图标</button>
            <button onclick="downloadAll()">下载全部</button>
        </div>

        <div class="icon-preview" id="iconPreview">
            <!-- 图标预览将在这里显示 -->
        </div>

        <div class="download-section">
            <h3>📥 下载说明</h3>
            <p>点击"生成图标"后，您可以右键点击任意图标选择"另存为"，或点击"下载全部"获取所有尺寸的图标。</p>
            <p><strong>使用方法：</strong></p>
            <ol>
                <li>将下载的图标文件放入 <code>MeetSyncApp/static/icons/</code> 目录</li>
                <li>取消注释 <code>index.html</code> 中的 PWA 相关代码</li>
                <li>重新启动开发服务器</li>
            </ol>
        </div>
    </div>

    <script>
        const iconSizes = [
            { size: 16, name: 'favicon-16x16.png' },
            { size: 32, name: 'favicon-32x32.png' },
            { size: 72, name: 'icon-72x72.png' },
            { size: 96, name: 'icon-96x96.png' },
            { size: 128, name: 'icon-128x128.png' },
            { size: 144, name: 'icon-144x144.png' },
            { size: 152, name: 'icon-152x152.png' },
            { size: 180, name: 'apple-touch-icon.png' },
            { size: 192, name: 'icon-192x192.png' },
            { size: 384, name: 'icon-384x384.png' },
            { size: 512, name: 'icon-512x512.png' }
        ];

        const iconTypes = {
            camera: '📹',
            video: '🎥', 
            meeting: '👥',
            phone: '📞'
        };

        function generateSVGIcon(size, bgColor, iconColor, iconType) {
            const emoji = iconTypes[iconType] || '📹';
            const fontSize = Math.floor(size * 0.6);
            
            return `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 ${size} ${size}">
                <rect width="${size}" height="${size}" rx="${size * 0.2}" fill="${bgColor}"/>
                <text x="50%" y="50%" font-size="${fontSize}" text-anchor="middle" dominant-baseline="central">${emoji}</text>
            </svg>`;
        }

        function generateIcons() {
            const bgColor = document.getElementById('bgColor').value;
            const iconColor = document.getElementById('iconColor').value;
            const iconType = document.getElementById('iconType').value;
            const preview = document.getElementById('iconPreview');
            
            preview.innerHTML = '';
            
            iconSizes.forEach(({ size, name }) => {
                const svg = generateSVGIcon(size, bgColor, iconColor, iconType);
                const blob = new Blob([svg], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(blob);
                
                const item = document.createElement('div');
                item.className = 'icon-item';
                item.innerHTML = `
                    <img src="${url}" alt="${name}" style="width: 64px; height: 64px;">
                    <div class="size">${size}x${size}</div>
                    <div style="font-size: 12px; margin-top: 5px;">${name}</div>
                    <button onclick="downloadIcon('${svg}', '${name}')" style="margin-top: 5px; padding: 4px 8px; font-size: 12px;">下载</button>
                `;
                
                preview.appendChild(item);
            });
        }

        function downloadIcon(svg, filename) {
            const blob = new Blob([svg], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename.replace('.png', '.svg');
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function downloadAll() {
            const bgColor = document.getElementById('bgColor').value;
            const iconColor = document.getElementById('iconColor').value;
            const iconType = document.getElementById('iconType').value;
            
            iconSizes.forEach(({ size, name }) => {
                const svg = generateSVGIcon(size, bgColor, iconColor, iconType);
                setTimeout(() => {
                    downloadIcon(svg, name);
                }, 100 * iconSizes.indexOf(iconSizes.find(item => item.name === name)));
            });
        }

        // 初始生成
        generateIcons();
    </script>
</body>
</html>
