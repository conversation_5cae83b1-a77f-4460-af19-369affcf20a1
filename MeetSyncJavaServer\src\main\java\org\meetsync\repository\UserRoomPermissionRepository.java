package org.meetsync.repository;

import org.meetsync.entity.PermissionType;
import org.meetsync.entity.Room;
import org.meetsync.entity.User;
import org.meetsync.entity.UserRoomPermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserRoomPermissionRepository extends JpaRepository<UserRoomPermission, Long> {

    /**
     * Find permission by user and room
     */
    @Query("SELECT urp FROM UserRoomPermission urp WHERE urp.user.id = :userId AND urp.room.id = :roomId AND urp.isActive = true")
    Optional<UserRoomPermission> findByUserIdAndRoomId(@Param("userId") Long userId, @Param("roomId") Long roomId);

    /**
     * Find permission by user and room ID string
     */
    @Query("SELECT urp FROM UserRoomPermission urp WHERE urp.user.id = :userId AND urp.room.roomId = :roomId AND urp.isActive = true")
    Optional<UserRoomPermission> findByUserIdAndRoomRoomId(@Param("userId") Long userId, @Param("roomId") String roomId);

    /**
     * Find all permissions for a user
     */
    @Query("SELECT urp FROM UserRoomPermission urp WHERE urp.user.id = :userId AND urp.isActive = true")
    List<UserRoomPermission> findByUserId(@Param("userId") Long userId);

    /**
     * Find all permissions for a room
     */
    @Query("SELECT urp FROM UserRoomPermission urp WHERE urp.room.id = :roomId AND urp.isActive = true")
    List<UserRoomPermission> findByRoomId(@Param("roomId") Long roomId);

    /**
     * Find permissions by room ID string
     */
    @Query("SELECT urp FROM UserRoomPermission urp WHERE urp.room.roomId = :roomId AND urp.isActive = true")
    List<UserRoomPermission> findByRoomRoomId(@Param("roomId") String roomId);

    /**
     * Find valid (active and not expired) permission
     */
    @Query("SELECT urp FROM UserRoomPermission urp WHERE urp.user.id = :userId AND urp.room.roomId = :roomId AND urp.isActive = true AND (urp.expiresAt IS NULL OR urp.expiresAt > :now)")
    Optional<UserRoomPermission> findValidPermission(@Param("userId") Long userId, @Param("roomId") String roomId, @Param("now") LocalDateTime now);

    /**
     * Find permissions by type
     */
    @Query("SELECT urp FROM UserRoomPermission urp WHERE urp.permissionType = :permissionType AND urp.isActive = true")
    List<UserRoomPermission> findByPermissionType(@Param("permissionType") PermissionType permissionType);

    /**
     * Find permissions granted by a specific user
     */
    @Query("SELECT urp FROM UserRoomPermission urp WHERE urp.grantedBy.id = :grantedById AND urp.isActive = true")
    List<UserRoomPermission> findByGrantedById(@Param("grantedById") Long grantedById);

    /**
     * Find expired permissions
     */
    @Query("SELECT urp FROM UserRoomPermission urp WHERE urp.expiresAt IS NOT NULL AND urp.expiresAt <= :now AND urp.isActive = true")
    List<UserRoomPermission> findExpiredPermissions(@Param("now") LocalDateTime now);

    /**
     * Deactivate permission
     */
    @Modifying
    @Query("UPDATE UserRoomPermission urp SET urp.isActive = false WHERE urp.id = :permissionId")
    void deactivatePermission(@Param("permissionId") Long permissionId);

    /**
     * Deactivate all permissions for a user in a room
     */
    @Modifying
    @Query("UPDATE UserRoomPermission urp SET urp.isActive = false WHERE urp.user.id = :userId AND urp.room.id = :roomId")
    void deactivateUserRoomPermissions(@Param("userId") Long userId, @Param("roomId") Long roomId);

    /**
     * Deactivate all permissions for a room
     */
    @Modifying
    @Query("UPDATE UserRoomPermission urp SET urp.isActive = false WHERE urp.room.id = :roomId")
    void deactivateRoomPermissions(@Param("roomId") Long roomId);

    /**
     * Deactivate expired permissions
     */
    @Modifying
    @Query("UPDATE UserRoomPermission urp SET urp.isActive = false WHERE urp.expiresAt IS NOT NULL AND urp.expiresAt <= :now")
    int deactivateExpiredPermissions(@Param("now") LocalDateTime now);

    /**
     * Check if user has specific permission type for room
     */
    @Query("SELECT COUNT(urp) > 0 FROM UserRoomPermission urp WHERE urp.user.id = :userId AND urp.room.roomId = :roomId AND urp.permissionType = :permissionType AND urp.isActive = true AND (urp.expiresAt IS NULL OR urp.expiresAt > :now)")
    boolean hasPermission(@Param("userId") Long userId, @Param("roomId") String roomId, @Param("permissionType") PermissionType permissionType, @Param("now") LocalDateTime now);

    /**
     * Count permissions for a room
     */
    @Query("SELECT COUNT(urp) FROM UserRoomPermission urp WHERE urp.room.id = :roomId AND urp.isActive = true")
    long countByRoomId(@Param("roomId") Long roomId);

    /**
     * Find users with permissions for a specific room
     */
    @Query("SELECT DISTINCT urp.user FROM UserRoomPermission urp WHERE urp.room.roomId = :roomId AND urp.isActive = true AND (urp.expiresAt IS NULL OR urp.expiresAt > :now)")
    List<User> findUsersWithPermissionsForRoom(@Param("roomId") String roomId, @Param("now") LocalDateTime now);

    /**
     * Find rooms user has permissions for
     */
    @Query("SELECT DISTINCT urp.room FROM UserRoomPermission urp WHERE urp.user.id = :userId AND urp.isActive = true AND (urp.expiresAt IS NULL OR urp.expiresAt > :now)")
    List<Room> findRoomsWithUserPermissions(@Param("userId") Long userId, @Param("now") LocalDateTime now);
}
