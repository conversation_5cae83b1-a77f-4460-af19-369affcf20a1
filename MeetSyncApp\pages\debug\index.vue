<template>
  <view class="debug-container">
    <view class="debug-header">
      <text class="debug-title">🐛 调试信息</text>
    </view>
    
    <view class="debug-content">
      <!-- 基础信息 -->
      <view class="debug-section">
        <text class="section-title">📱 基础信息</text>
        <view class="info-item">
          <text class="label">平台:</text>
          <text class="value">{{ systemInfo.platform }}</text>
        </view>
        <view class="info-item">
          <text class="label">版本:</text>
          <text class="value">{{ systemInfo.version }}</text>
        </view>
        <view class="info-item">
          <text class="label">当前页面:</text>
          <text class="value">{{ currentPage }}</text>
        </view>
      </view>

      <!-- 存储信息 -->
      <view class="debug-section">
        <text class="section-title">💾 存储信息</text>
        <view class="info-item">
          <text class="label">Token:</text>
          <text class="value">{{ storageInfo.token ? '✅ 存在' : '❌ 不存在' }}</text>
        </view>
        <view class="info-item">
          <text class="label">用户信息:</text>
          <text class="value">{{ storageInfo.user ? '✅ 存在' : '❌ 不存在' }}</text>
        </view>
        <view v-if="storageInfo.user" class="info-item">
          <text class="label">用户名:</text>
          <text class="value">{{ storageInfo.user.username }}</text>
        </view>
        <view v-if="storageInfo.user" class="info-item">
          <text class="label">角色:</text>
          <text class="value">{{ storageInfo.user.role }}</text>
        </view>
      </view>

      <!-- 页面栈信息 -->
      <view class="debug-section">
        <text class="section-title">📄 页面栈</text>
        <view v-for="(page, index) in pageStack" :key="index" class="page-item">
          <text class="page-route">{{ page.route }}</text>
          <text class="page-options">{{ JSON.stringify(page.options) }}</text>
        </view>
      </view>

      <!-- 日志信息 -->
      <view class="debug-section">
        <text class="section-title">📝 日志</text>
        <view class="log-container">
          <view v-for="(log, index) in logs" :key="index" class="log-item">
            <text class="log-time">{{ log.time }}</text>
            <text class="log-message">{{ log.message }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="debug-actions">
      <button class="debug-btn primary" @click="goToLogin">
        前往登录页
      </button>
      <button class="debug-btn secondary" @click="clearStorage">
        清除存储
      </button>
      <button class="debug-btn success" @click="testGuestLogin">
        测试游客登录
      </button>
      <button class="debug-btn warning" @click="refreshInfo">
        刷新信息
      </button>
    </view>
  </view>
</template>

<script>
import authManager from '../../utils/auth.js'

export default {
  name: 'Debug',
  data() {
    return {
      systemInfo: {},
      storageInfo: {},
      pageStack: [],
      currentPage: '',
      logs: []
    }
  },
  onLoad() {
    this.initDebugInfo()
    this.addLog('调试页面加载完成')
  },
  onShow() {
    this.refreshInfo()
    this.addLog('调试页面显示')
  },
  methods: {
    initDebugInfo() {
      // 获取系统信息
      this.systemInfo = uni.getSystemInfoSync()
      
      // 获取当前页面信息
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      this.currentPage = (currentPage && currentPage.route) || '未知'
      
      this.refreshInfo()
    },
    
    refreshInfo() {
      // 获取存储信息
      this.storageInfo = {
        token: uni.getStorageSync('auth_token'),
        user: uni.getStorageSync('user')
      }
      
      // 获取页面栈
      const pages = getCurrentPages()
      this.pageStack = pages.map(page => ({
        route: page.route,
        options: page.options || {}
      }))
      
      this.addLog('信息已刷新')
    },
    
    goToLogin() {
      this.addLog('跳转到登录页')
      uni.reLaunch({
        url: '/pages/auth/login'
      })
    },
    
    clearStorage() {
      uni.showModal({
        title: '确认清除',
        content: '确定要清除所有存储数据吗？',
        success: (res) => {
          if (res.confirm) {
            authManager.clearAuth()
            this.addLog('存储已清除')
            this.refreshInfo()
          }
        }
      })
    },
    
    testGuestLogin() {
      try {
        authManager.guestLogin('调试用户')
        this.addLog('游客登录成功')
        this.refreshInfo()
        
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/dashboard/index'
          })
        }, 1000)
      } catch (error) {
        this.addLog('游客登录失败: ' + error.message)
      }
    },
    
    addLog(message) {
      const time = new Date().toLocaleTimeString()
      this.logs.unshift({ time, message })
      
      // 限制日志数量
      if (this.logs.length > 20) {
        this.logs = this.logs.slice(0, 20)
      }
      
      console.log(`[Debug] ${time} ${message}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.debug-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}

.debug-header {
  background: #333;
  color: white;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.debug-title {
  font-size: 36rpx;
  font-weight: bold;
}

.debug-content {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.debug-section {
  margin-bottom: 40rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #eee;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
  font-size: 28rpx;
}

.label {
  color: #666;
  font-weight: 500;
}

.value {
  color: #333;
  font-weight: bold;
}

.page-item {
  margin-bottom: 15rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.page-route {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 8rpx;
}

.page-options {
  font-size: 24rpx;
  color: #666;
}

.log-container {
  max-height: 400rpx;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 15rpx;
}

.log-item {
  display: flex;
  gap: 15rpx;
  margin-bottom: 10rpx;
  font-size: 24rpx;
}

.log-time {
  color: #666;
  min-width: 120rpx;
}

.log-message {
  color: #333;
}

.debug-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
}

.debug-btn {
  padding: 20rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: white;
  font-weight: bold;
}

.debug-btn.primary {
  background: #007bff;
}

.debug-btn.secondary {
  background: #6c757d;
}

.debug-btn.success {
  background: #28a745;
}

.debug-btn.warning {
  background: #ffc107;
  color: #333;
}
</style>
