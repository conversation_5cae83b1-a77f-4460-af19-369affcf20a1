require('dotenv').config()

if (process.env.NODE_ENV === 'dev') {
  require('dotenv').config({ path: '.env.dev' })
  console.log('Using .env.dev');
} else if (process.env.NODE_ENV === 'prod') {
  require('dotenv').config({ path: '.env.prod' })
  console.log('Using .env.prod');
} else {
  require('dotenv').config()
  console.log('Using .env');
}

const express = require('express')
const helmet = require('helmet')
const cors = require('cors')

const app = express()
const https = require('httpolyglot')
const fs = require('fs')
const mediasoup = require('mediasoup')
const config = require('./config')
const path = require('path')
const Room = require('./Room')
const Peer = require('./Peer')

// Import database and models
const db = require('./database/connection')
const User = require('./models/User')
const RoomModel = require('./models/Room')

// Import middleware and routes
const { verifySocketToken } = require('./middleware/auth')
const authRoutes = require('./routes/auth')
const roomRoutes = require('./routes/rooms')

const options = {
  key: fs.readFileSync(path.join(__dirname, config.sslKey), 'utf-8'),
  cert: fs.readFileSync(path.join(__dirname, config.sslCrt), 'utf-8')
}

// Middleware
app.use(helmet({
  contentSecurityPolicy: false, // Disable for development
  crossOriginEmbedderPolicy: false
}))
app.use(cors({
  origin: process.env.NODE_ENV === 'production' ? false : true,
  credentials: true
}))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Health check endpoint
app.get('/health', async (_, res) => {
  try {
    // 检查数据库连接
    let dbStatus = 'unknown'
    try {
      await db.query('SELECT 1')
      dbStatus = 'connected'
    } catch (dbError) {
      dbStatus = 'disconnected'
    }

    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: require('../package.json').version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      database: dbStatus,
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
      }
    }

    // 如果数据库断开连接，返回503状态
    if (dbStatus === 'disconnected') {
      return res.status(503).json({
        ...healthData,
        status: 'unhealthy'
      })
    }

    res.status(200).json(healthData)
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    })
  }
})

// API health check endpoint (alias)
app.get('/api/health', async (_, res) => {
  try {
    // 检查数据库连接
    let dbStatus = 'unknown'
    try {
      await db.query('SELECT 1')
      dbStatus = 'connected'
    } catch (dbError) {
      dbStatus = 'disconnected'
    }

    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: require('../package.json').version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      database: dbStatus,
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
      },
      rooms: {
        active: roomList.size,
        workers: workers.length
      }
    }

    // 如果数据库断开连接，返回503状态
    if (dbStatus === 'disconnected') {
      return res.status(503).json({
        ...healthData,
        status: 'unhealthy'
      })
    }

    res.status(200).json(healthData)
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    })
  }
})

// API Routes
app.use('/api/auth', authRoutes)
app.use('/api/rooms', roomRoutes)

// Root route redirect
app.get('/', (_, res) => {
  res.redirect('/login.html')
})

// Static files
app.use(express.static(path.join(__dirname, '..', 'public')))

const httpsServer = https.createServer(options, app)
const io = require('socket.io')(httpsServer, {
  cors: {
    origin: process.env.NODE_ENV === 'production' ? false : true,
    credentials: true
  }
})

// Socket.IO authentication middleware
io.use(verifySocketToken)

// Initialize database and start server
async function startServer() {
  try {
    // 在Docker环境中，数据库已经由MySQL容器初始化
    // 只需要连接到数据库
    console.log('🔗 Connecting to database...')
    await db.connect()
    console.log('✅ Database connected')

    // 如果不是Docker环境，可能需要初始化数据库
    if (!process.env.SKIP_DB_INIT && process.env.NODE_ENV !== 'production') {
      console.log('🔧 Checking database initialization...')
      try {
        // 检查是否存在users表
        await db.query('SELECT 1 FROM users LIMIT 1')
        console.log('✅ Database tables exist')
      } catch (error) {
        console.log('⚠️  Database tables not found, may need initialization')
        console.log('💡 Run: npm run init-db')
      }
    }

    // Start server
    httpsServer.listen(config.listenPort, () => {
      console.log('🚀 Server listening on https://' + config.listenIp + ':' + config.listenPort)
      console.log('📊 Health check: http://' + config.listenIp + ':' + config.listenPort + '/health')
    })
  } catch (error) {
    console.error('❌ Failed to start server:', error)
    process.exit(1)
  }
}

startServer()

// all mediasoup workers
let workers = []
let nextMediasoupWorkerIdx = 0

/**
 * roomList
 * {
 *  room_id: Room {
 *      id:
 *      router:
 *      peers: {
 *          id:,
 *          name:,
 *          master: [boolean],
 *          transports: [Map],
 *          producers: [Map],
 *          consumers: [Map],
 *          rtpCapabilities:
 *      }
 *  }
 * }
 */
let roomList = new Map()

;(async () => {
  await createWorkers()
})()

async function createWorkers() {
  let { numWorkers } = config.mediasoup

  for (let i = 0; i < numWorkers; i++) {
    let worker = await mediasoup.createWorker({
      logLevel: config.mediasoup.worker.logLevel,
      logTags: config.mediasoup.worker.logTags,
      rtcMinPort: config.mediasoup.worker.rtcMinPort,
      rtcMaxPort: config.mediasoup.worker.rtcMaxPort
    })

    worker.on('died', () => {
      console.error('mediasoup worker died, exiting in 2 seconds... [pid:%d]', worker.pid)
      setTimeout(() => process.exit(1), 2000)
    })
    workers.push(worker)

    // log worker resource usage
    /*setInterval(async () => {
            const usage = await worker.getResourceUsage();

            console.info('mediasoup Worker resource usage [pid:%d]: %o', worker.pid, usage);
        }, 120000);*/
  }
}

io.on('connection', (socket) => {
  console.log('User connected:', socket.user.username)

  socket.on('createRoom', async ({ room_id }, callback) => {
    try {
      // 检查用户是否有创建房间的权限
      if (socket.user.role === 'guest') {
        return callback({ error: '游客无法创建房间' })
      }

      if (roomList.has(room_id)) {
        return callback({ error: '房间已在内存中存在' })
      }

      // 检查房间是否在数据库中存在
      const existingRoom = await RoomModel.findByRoomId(room_id)
      if (existingRoom) {
        // 房间在数据库中存在，创建 mediasoup 房间
        console.log('为现有数据库房间创建 mediasoup 房间:', room_id)
        let worker = await getMediasoupWorker()
        roomList.set(room_id, new Room(room_id, worker, io))
        return callback({ room_id })
      }

      // 在数据库中创建新房间
      const roomData = {
        room_id,
        name: `房间 ${room_id}`,
        description: '',
        creator_id: socket.user.id,
        required_role: 'guest', // 默认允许任何人加入
        is_public: true
      }

      try {
        const newRoom = await RoomModel.create(roomData)
        await newRoom.grantPermission(socket.user.id, 'full_access', socket.user.id)

        // 创建 mediasoup 房间
        console.log('已创建房间:', { room_id, user: socket.user.username })
        let worker = await getMediasoupWorker()
        roomList.set(room_id, new Room(room_id, worker, io))

        callback({ room_id })
      } catch (dbError) {
        if (dbError.message === 'Room ID already exists') {
          // 房间已被其他用户创建，只需创建 mediasoup 房间
          let worker = await getMediasoupWorker()
          roomList.set(room_id, new Room(room_id, worker, io))
          return callback({ room_id })
        }
        throw dbError
      }
    } catch (error) {
      console.error('创建房间错误:', error)
      callback({ error: '创建房间失败' })
    }
  })

  socket.on('join', async ({ room_id, password }, cb) => {
    try {
      // Check if room exists in database
      let accessInfo = await socket.user.canAccessRoom(room_id)
      // If room doesn't exist, create it automatically for backward compatibility
      if (!accessInfo.canAccess && accessInfo.reason === 'Room not found') {
        console.log('Room not found, creating automatically:', room_id)

        // Create room in database
        const roomData = {
          room_id,
          name: `Room ${room_id}`,
          description: 'Auto-created room',
          creator_id: socket.user.id,
          required_role: 'guest', // Allow anyone to join auto-created rooms
          is_public: true
        }

        try {
          const newRoom = await RoomModel.create(roomData)
          console.log('Auto-created room:', newRoom.room_id)

          // Grant full access to the creator
          await newRoom.grantPermission(socket.user.id, 'full_access', socket.user.id)

          // Re-check access after creating room
          accessInfo = await socket.user.canAccessRoom(room_id)
        } catch (createError) {
          console.error('Failed to auto-create room:', createError)
          return cb({ error: 'Failed to create room' })
        }
      }

      // Check access permissions again
      if (!accessInfo.canAccess) {
        return cb({ error: accessInfo.reason })
      }

      const room = accessInfo.room

      // Verify password if required
      if (room.password_hash && password) {
        const roomModel = await RoomModel.findByRoomId(room_id)
        const isValidPassword = await roomModel.verifyPassword(password)
        if (!isValidPassword) {
          return cb({ error: 'Invalid room password' })
        }
      } else if (room.password_hash && !password) {
        return cb({ error: 'Room password required' })
      }

      // Create mediasoup room if it doesn't exist
      if (!roomList.has(room_id)) {
        let worker = await getMediasoupWorker()
        roomList.set(room_id, new Room(room_id, worker, io))
      }

      // Add peer to room
      const peer = new Peer(socket.id, socket.user.username)
      peer.userId = socket.user.id
      peer.role = socket.user.role
      peer.permission = accessInfo.permission

      roomList.get(room_id).addPeer(peer)
      socket.room_id = room_id
      socket.permission = accessInfo.permission

      // 让socket加入房间（用于广播）
      socket.join(room_id)

      // Log room activity
      const roomModel = await RoomModel.findByRoomId(room_id)
      if (roomModel) {
        await roomModel.logActivity(socket.user.id, 'join', socket.handshake.address)
      }

      // 广播用户加入事件，更新参与人数
      const currentRoom = roomList.get(room_id)
      const participantCount = currentRoom.getPeers().size
      console.log(`广播用户加入事件: ${socket.user.username}, 房间人数: ${participantCount}`)

      // 向房间内所有用户（包括新加入的用户）广播人数更新
      io.to(room_id).emit('participantCountUpdated', {
        count: participantCount
      })

      // 向其他用户广播用户加入事件
      socket.to(room_id).emit('userJoined', {
        username: socket.user.username,
        participantCount: participantCount
      })

      // 输出房间状态调试信息
      currentRoom.logRoomStatus()

      cb({
        ...roomList.get(room_id).toJson(),
        userPermission: accessInfo.permission
      })
    } catch (error) {
      console.error('Join room error:', error)
      cb({ error: 'Failed to join room' })
    }
  })

  socket.on('getProducers', () => {
    if (!roomList.has(socket.room_id)) return
    console.log('Get producers', { name: `${roomList.get(socket.room_id).getPeers().get(socket.id).name}` })

    // send all the current producer to newly joined member
    let producerList = roomList.get(socket.room_id).getProducerListForPeer()

    socket.emit('newProducers', producerList)
  })

  socket.on('getMyProducers', (_, callback) => {
    if (!roomList.has(socket.room_id)) {
      callback({ producers: [] })
      return
    }

    console.log('Get my producers', { name: `${roomList.get(socket.room_id).getPeers().get(socket.id).name}` })

    const room = roomList.get(socket.room_id)
    const peer = room.getPeers().get(socket.id)

    if (!peer) {
      callback({ producers: [] })
      return
    }

    // 获取当前用户的所有生产者
    const myProducers = []
    peer.producers.forEach((producer, producer_id) => {
      const mediaType = producer.appData?.mediaType || 'unknown'
      myProducers.push({
        producer_id,
        mediaType
      })
    })

    console.log('返回我的生产者列表:', myProducers)
    callback({ producers: myProducers })
  })

  socket.on('getRouterRtpCapabilities', (_, callback) => {
    console.log('Get RouterRtpCapabilities', {
      name: `${roomList.get(socket.room_id).getPeers().get(socket.id).name}`
    })

    try {
      callback(roomList.get(socket.room_id).getRtpCapabilities())
    } catch (e) {
      callback({
        error: e.message
      })
    }
  })

  socket.on('createWebRtcTransport', async (_, callback) => {
    console.log('Create webrtc transport', {
      name: `${roomList.get(socket.room_id).getPeers().get(socket.id).name}`
    })

    try {
      const { params } = await roomList.get(socket.room_id).createWebRtcTransport(socket.id)

      callback(params)
    } catch (err) {
      console.error(err)
      callback({
        error: err.message
      })
    }
  })

  socket.on('connectTransport', async ({ transport_id, dtlsParameters }, callback) => {
    console.log('Connect transport', { name: `${roomList.get(socket.room_id).getPeers().get(socket.id).name}` })

    if (!roomList.has(socket.room_id)) return
    await roomList.get(socket.room_id).connectPeerTransport(socket.id, transport_id, dtlsParameters)

    callback('success')
  })

  socket.on('produce', async ({ kind, rtpParameters, producerTransportId }, callback) => {
    try {
      if (!roomList.has(socket.room_id)) {
        return callback({ error: '您不在任何房间中' })
      }

      // 根据媒体类型检查权限
      const requiredPermissions = {
        audio: ['audio_only', 'video_audio', 'full_access'],
        video: ['video_audio', 'full_access'],
        screen: ['full_access']
      }

      const allowedPermissions = requiredPermissions[kind] || []
      if (!allowedPermissions.includes(socket.permission)) {
        // 中文化权限名称
        const permissionNames = {
          view_only: '仅观看',
          audio_only: '仅音频',
          video_audio: '音频和视频',
          full_access: '完全访问'
        }
        const mediaNames = {
          audio: '音频',
          video: '视频',
          screen: '屏幕共享'
        }

        const currentPermissionName = permissionNames[socket.permission] || socket.permission
        const mediaName = mediaNames[kind] || kind
        const requiredPermissionNames = allowedPermissions.map(p => permissionNames[p] || p).join(' 或 ')

        return callback({
          error: `权限不足。使用${mediaName}需要 ${requiredPermissionNames} 权限，您当前的权限是 ${currentPermissionName}`
        })
      }

      let producer_id = await roomList.get(socket.room_id).produce(socket.id, producerTransportId, rtpParameters, kind)
      // Log activity
      const roomModel = await RoomModel.findByRoomId(socket.room_id)
      await roomModel.logActivity(socket.user.id, `start_${kind}`, socket.handshake.address)

      callback({
        producer_id
      })
    } catch (error) {
      console.error('媒体生产错误:', error)
      callback({ error: '启动媒体生产失败' })
    }
  })

  socket.on('consume', async ({ consumerTransportId, producerId, rtpCapabilities }, callback) => {
    try {
      if (!socket.room_id || !roomList.has(socket.room_id)) {
        console.error('Room not found for consume:', socket.room_id)
        callback({ error: 'Room not found' })
        return
      }

      let params = await roomList.get(socket.room_id).consume(socket.id, consumerTransportId, producerId, rtpCapabilities)

      if (!params) {
        console.error('Failed to consume')
        callback({ error: 'Failed to consume' })
        return
      }

      console.log('Consuming', {
        name: `${roomList.get(socket.room_id) && roomList.get(socket.room_id).getPeers().get(socket.id).name}`,
        producer_id: `${producerId}`,
        consumer_id: `${params.id}`
      })

      callback(params)
    } catch (error) {
      console.error('Consume error:', error)
      callback({ error: 'Consume failed' })
    }
  })

  socket.on('resume', async ({ consumerId }, callback) => {
    try {
      console.log('Resume request received:', { socket_id: socket.id, consumerId, room_id: socket.room_id })

      if (!socket.room_id || !roomList.has(socket.room_id)) {
        console.error('Room not found for resume:', socket.room_id)
        callback({ error: 'Room not found' })
        return
      }

      if (!consumerId) {
        console.error('Consumer ID is required for resume')
        callback({ error: 'Consumer ID is required' })
        return
      }

      const room = roomList.get(socket.room_id)
      const peer = room.getPeers().get(socket.id)

      if (!peer) {
        console.error('Peer not found for resume:', socket.id)
        callback({ error: 'Peer not found' })
        return
      }

      // 检查 consumer 是否存在
      const consumer = peer.getConsumer(consumerId)
      if (!consumer) {
        console.error('Consumer not found for resume:', consumerId)
        callback({ error: 'Consumer not found' })
        return
      }

      await room.resume(socket.id, consumerId)
      console.log('Consumer resumed successfully:', consumerId)
      callback({ success: true })
    } catch (error) {
      console.error('Resume error:', error.message, error.stack)
      callback({ error: error.message || 'Resume failed' })
    }
  })

  socket.on('getMyRoomInfo', (_, cb) => {
    cb(roomList.get(socket.room_id).toJson())
  })

  socket.on('disconnect', () => {
    console.log('Disconnect', {
      name: `${roomList.get(socket.room_id) && roomList.get(socket.room_id).getPeers().get(socket.id).name}`
    })

    if (!socket.room_id) return

    // 获取用户名用于广播
    const username = roomList.get(socket.room_id).getPeers().get(socket.id)?.name || '未知用户'

    roomList.get(socket.room_id).removePeer(socket.id)

    // 广播用户离开事件，更新参与人数
    if (roomList.has(socket.room_id)) {
      const participantCount = roomList.get(socket.room_id).getPeers().size
      console.log(`广播用户离开事件: ${username}, 房间人数: ${participantCount}`)

      // 向房间内剩余用户广播人数更新
      io.to(socket.room_id).emit('participantCountUpdated', {
        count: participantCount
      })

      // 向其他用户广播用户离开事件
      socket.to(socket.room_id).emit('userLeft', {
        username: username,
        participantCount: participantCount
      })
    }
  })

  // 处理前端发送的关闭生产者请求
  socket.on('closeProducer', ({ producer_id }, callback) => {
    console.log('收到关闭生产者请求', {
      name: `${roomList.get(socket.room_id) && roomList.get(socket.room_id).getPeers().get(socket.id).name}`,
      producer_id: producer_id
    })

    if (!roomList.has(socket.room_id)) {
      console.warn('房间不存在，无法关闭生产者')
      if (callback) callback({ error: '房间不存在' })
      return
    }

    const room = roomList.get(socket.room_id)
    const peer = room.getPeers().get(socket.id)

    if (!peer) {
      console.warn('用户不存在，无法关闭生产者')
      if (callback) callback({ error: '用户不存在' })
      return
    }

    // 获取生产者信息（在关闭前）
    const producer = peer.getProducer(producer_id)
    let mediaType = 'unknown'
    if (producer && producer.appData) {
      mediaType = producer.appData.mediaType || 'unknown'
    }

    // 关闭生产者
    room.closeProducer(socket.id, producer_id)

    // 广播生产者关闭事件给房间内其他用户
    const username = peer.name || '未知用户'
    console.log(`广播生产者关闭事件: ${username} 关闭了 ${mediaType}`)

    socket.to(socket.room_id).emit('producerClosed', {
      producer_id: producer_id,
      socket_id: socket.id,
      username: username,
      mediaType: mediaType
    })

    // 记录活动日志
    if (socket.user && socket.user.id) {
      RoomModel.findByRoomId(socket.room_id).then(roomModel => {
        if (roomModel) {
          roomModel.logActivity(socket.user.id, `stop_${mediaType}`, socket.handshake.address)
        }
      }).catch(error => {
        console.error('记录活动日志失败:', error)
      })
    }

    // 输出房间状态调试信息
    room.logRoomStatus()

    // 响应前端请求
    if (callback) callback({ success: true, mediaType })
  })

  // 兼容旧版本的 producerClosed 事件（保留以防万一）
  socket.on('producerClosed', ({ producer_id }) => {
    console.log('收到旧版本 producerClosed 事件', {
      name: `${roomList.get(socket.room_id) && roomList.get(socket.room_id).getPeers().get(socket.id).name}`,
      producer_id: producer_id
    })

    if (!roomList.has(socket.room_id)) {
      console.warn('房间不存在，无法关闭生产者')
      return
    }

    const room = roomList.get(socket.room_id)
    const peer = room.getPeers().get(socket.id)

    if (!peer) {
      console.warn('用户不存在，无法关闭生产者')
      return
    }

    // 获取生产者信息（在关闭前）
    const producer = peer.getProducer(producer_id)
    let mediaType = 'unknown'
    if (producer && producer.appData) {
      mediaType = producer.appData.mediaType || 'unknown'
    }

    // 关闭生产者
    room.closeProducer(socket.id, producer_id)

    // 广播生产者关闭事件给房间内其他用户
    const username = peer.name || '未知用户'
    console.log(`广播生产者关闭事件: ${username} 关闭了 ${mediaType}`)

    socket.to(socket.room_id).emit('producerClosed', {
      producer_id: producer_id,
      socket_id: socket.id,
      username: username,
      mediaType: mediaType
    })

    // 记录活动日志
    if (socket.user && socket.user.id) {
      RoomModel.findByRoomId(socket.room_id).then(roomModel => {
        if (roomModel) {
          roomModel.logActivity(socket.user.id, `stop_${mediaType}`, socket.handshake.address)
        }
      }).catch(error => {
        console.error('记录活动日志失败:', error)
      })
    }
  })

  socket.on('exitRoom', async (_, callback) => {
    console.log('Exit room', {
      name: `${roomList.get(socket.room_id) && roomList.get(socket.room_id).getPeers().get(socket.id).name}`
    })

    if (!roomList.has(socket.room_id)) {
      callback({
        error: '当前不在任何房间中'
      })
      return
    }

    // 获取用户名用于广播
    const username = roomList.get(socket.room_id).getPeers().get(socket.id)?.name || '未知用户'
    const room_id = socket.room_id

    // close transports
    await roomList.get(socket.room_id).removePeer(socket.id)

    // 广播用户离开事件，更新参与人数
    if (roomList.has(room_id)) {
      const participantCount = roomList.get(room_id).getPeers().size
      console.log(`广播用户主动退出房间事件: ${username}, 房间人数: ${participantCount}`)

      // 向房间内剩余用户广播人数更新
      io.to(room_id).emit('participantCountUpdated', {
        count: participantCount
      })

      // 向其他用户广播用户离开事件
      socket.to(room_id).emit('userLeft', {
        username: username,
        participantCount: participantCount
      })
    }

    if (roomList.get(room_id).getPeers().size === 0) {
      roomList.delete(room_id)
    }

    socket.room_id = null

    callback('成功退出房间')
  })
})

// TODO remove - never used?
function room() {
  return Object.values(roomList).map((r) => {
    return {
      router: r.router.id,
      peers: Object.values(r.peers).map((p) => {
        return {
          name: p.name
        }
      }),
      id: r.id
    }
  })
}

/**
 * Get next mediasoup Worker.
 */
function getMediasoupWorker() {
  const worker = workers[nextMediasoupWorkerIdx]

  if (++nextMediasoupWorkerIdx === workers.length) nextMediasoupWorkerIdx = 0

  return worker
}
