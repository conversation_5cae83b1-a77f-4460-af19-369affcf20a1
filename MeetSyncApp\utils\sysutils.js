
export function getLocalIp() {
  if (typeof window !== 'undefined') {
    return window.location.hostname
  }
  if (typeof process !== 'undefined') {
    return process.env.HOST || 'localhost'
  }
  return 'localhost'
}

export function isMobile() {
  try {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    )
  } catch (error) {
    return false
  }
}

export function isWechat() {
  try {
    return /MicroMessenger/i.test(navigator.userAgent)
  } catch (error) {
    return false
  }
}