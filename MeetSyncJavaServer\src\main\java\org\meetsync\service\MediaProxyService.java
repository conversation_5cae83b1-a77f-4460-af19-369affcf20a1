package org.meetsync.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.log4j.Log4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.meetsync.websocket.MediaServerWebSocketHandler;
import org.meetsync.websocket.ClientWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Set;
import java.util.HashSet;
import java.util.concurrent.ExecutionException;


@Service
public class MediaProxyService {

    private static final Logger logger = LoggerFactory.getLogger(MediaProxyService.class);

    @Autowired
    @Lazy
    private MediaServerWebSocketHandler mediaServerHandler;

    @Autowired
    @Lazy
    private ClientWebSocketHandler clientWebSocketHandler;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private boolean mediaServerConnected = false;

    // 跟踪活跃的生产者
    // Key: roomId_userId, Value: Set<producerId>
    private final Map<String, Set<String>> activeProducers = new ConcurrentHashMap<>();

    /**
     * 创建媒体房间
     */
    public CompletableFuture<JsonNode> createMediaRoom(String roomId) {
        ObjectNode data = objectMapper.createObjectNode();
        data.put("roomId", roomId);
        CompletableFuture<JsonNode> createRoom = mediaServerHandler.sendRequest("create_room", data);
        return createRoom;
    }
    
    /**
     * 获取RTP能力
     */
    public CompletableFuture<JsonNode> getRtpCapabilities(String roomId) {
        ObjectNode data = objectMapper.createObjectNode();
        data.put("roomId", roomId);

        CompletableFuture<JsonNode> getRtpCapabilities = mediaServerHandler.sendRequest("get_rtp_capabilities", data);
        logger.info(getRtpCapabilities.toString());
        return getRtpCapabilities;
    }
    
    /**
     * 创建WebRTC传输
     */
    public CompletableFuture<JsonNode> createTransport(String roomId, Long userId, String direction) {
        ObjectNode data = objectMapper.createObjectNode();
        data.put("roomId", roomId);
        data.put("userId", userId);
        data.put("direction", direction); // "send" or "recv"
        
        return mediaServerHandler.sendRequest("create_transport", data);
    }
    
    /**
     * 连接传输
     */
    public CompletableFuture<JsonNode> connectTransport(String roomId, Long userId, String transportId, JsonNode dtlsParameters) {
        ObjectNode data = objectMapper.createObjectNode();
        data.put("roomId", roomId);
        data.put("userId", userId);
        data.put("transportId", transportId);
        data.set("dtlsParameters", dtlsParameters);
        
        return mediaServerHandler.sendRequest("connect_transport", data);
    }
    
    /**
     * 创建生产者
     */
    public CompletableFuture<JsonNode> createProducer(String roomId, Long userId, String transportId, String kind, JsonNode rtpParameters) {
        ObjectNode data = objectMapper.createObjectNode();
        data.put("roomId", roomId);
        data.put("userId", userId);
        data.put("transportId", transportId);
        data.put("kind", kind); // "audio" or "video"
        data.set("rtpParameters", rtpParameters);

        return mediaServerHandler.sendRequest("create_producer", data)
            .whenComplete((result, throwable) -> {
                if (throwable == null && result.has("producer_id")) {
                    String producerId = result.get("producer_id").asText();
                    String key = roomId + "_" + userId;
                    activeProducers.computeIfAbsent(key, k -> ConcurrentHashMap.newKeySet()).add(producerId);
                    logger.info("生产者创建成功并记录: roomId={}, userId={}, producerId={}", roomId, userId, producerId);
                }
            });
    }
    
    /**
     * 创建消费者
     */
    public CompletableFuture<JsonNode> createConsumer(String roomId, Long userId, String transportId, String producerId, JsonNode rtpCapabilities) {
        ObjectNode data = objectMapper.createObjectNode();
        data.put("roomId", roomId);
        data.put("userId", userId);
        data.put("transportId", transportId);
        data.put("producerId", producerId);
        data.set("rtpCapabilities", rtpCapabilities);

        return createConsumerWithRetry(data, 3);
    }

    /**
     * 带重试的创建消费者
     */
    private CompletableFuture<JsonNode> createConsumerWithRetry(ObjectNode data, int maxRetries) {
        return mediaServerHandler.sendRequest("create_consumer", data)
                .handle((result, throwable) -> {
                    if (throwable != null && maxRetries > 1) {
                        logger.warn("创建消费者失败，尝试重试: {}", throwable.getMessage());
                        try {
                            Thread.sleep(100); // 短暂延迟
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                        return createConsumerWithRetry(data, maxRetries - 1).join();
                    } else if (throwable != null) {
                        throw new RuntimeException(throwable);
                    } else {
                        return result;
                    }
                });
    }
    
    /**
     * 关闭生产者
     */
    public CompletableFuture<JsonNode> closeProducer(String roomId, Long userId, String producerId) {
        logger.info("关闭生产者: roomId={}, userId={}, producerId={}", roomId, userId, producerId);

        ObjectNode data = objectMapper.createObjectNode();
        data.put("roomId", roomId);
        data.put("userId", userId);
        data.put("producerId", producerId);

        return mediaServerHandler.sendRequest("close_producer", data)
            .whenComplete((result, throwable) -> {
                if (throwable != null) {
                    logger.error("关闭生产者失败: roomId={}, userId={}, producerId={}", roomId, userId, producerId, throwable);
                } else {
                    // 从跟踪中移除生产者
                    String key = roomId + "_" + userId;
                    Set<String> userProducers = activeProducers.get(key);
                    if (userProducers != null) {
                        userProducers.remove(producerId);
                        if (userProducers.isEmpty()) {
                            activeProducers.remove(key);
                        }
                    }
                    logger.info("关闭生产者成功: roomId={}, userId={}, producerId={}", roomId, userId, producerId);
                }
            });
    }
    
    /**
     * 关闭消费者
     */
    public CompletableFuture<JsonNode> closeConsumer(String roomId, Long userId, String consumerId) {
        ObjectNode data = objectMapper.createObjectNode();
        data.put("roomId", roomId);
        data.put("userId", userId);
        data.put("consumerId", consumerId);

        return mediaServerHandler.sendRequest("close_consumer", data);
    }

    /**
     * 清理用户的所有生产者（用户断开连接时调用）
     */
    public CompletableFuture<JsonNode> cleanupUserProducers(String roomId, Long userId) {
        logger.info("开始清理用户生产者: roomId={}, userId={}", roomId, userId);

        ObjectNode data = objectMapper.createObjectNode();
        data.put("roomId", roomId);
        data.put("userId", userId);

        return mediaServerHandler.sendRequest("cleanup_user_producers", data)
            .whenComplete((result, throwable) -> {
                if (throwable != null) {
                    logger.error("清理用户生产者失败: roomId={}, userId={}", roomId, userId, throwable);
                } else {
                    // 清理本地跟踪的生产者
                    String key = roomId + "_" + userId;
                    Set<String> removedProducers = activeProducers.remove(key);
                    if (removedProducers != null) {
                        logger.info("清理用户生产者成功: roomId={}, userId={}, 清理数量={}", roomId, userId, removedProducers.size());
                    } else {
                        logger.info("清理用户生产者成功: roomId={}, userId={}, 无本地记录", roomId, userId);
                    }
                }
            });
    }

    /**
     * 恢复消费者
     */
    public CompletableFuture<JsonNode> resumeConsumer(String roomId, Long userId, String consumerId) {
        ObjectNode data = objectMapper.createObjectNode();
        data.put("roomId", roomId);
        data.put("userId", userId);
        data.put("consumerId", consumerId);

        return mediaServerHandler.sendRequest("resume_consumer", data);
    }
    
    /**
     * 获取房间内的生产者列表
     */
    public CompletableFuture<JsonNode> getProducers(String roomId, Long userId) {
        ObjectNode data = objectMapper.createObjectNode();
        data.put("roomId", roomId);
        data.put("userId", userId);
        
        return mediaServerHandler.sendRequest("get_producers", data);
    }
    
    /**
     * 检查媒体服务器连接状态
     */
    public boolean isMediaServerConnected() {
        if (mediaServerHandler == null){
            logger.info("mediaServerHandler is null");
        }
        return mediaServerConnected && mediaServerHandler.isMediaServerConnected();
    }
    
    /**
     * 设置媒体服务器连接状态
     */
    public void setMediaServerConnected(boolean connected) {
        this.mediaServerConnected = connected;
        logger.info("媒体服务器连接状态更新: {}", connected ? "已连接" : "已断开");
    }
    
    /**
     * 获取媒体服务器状态信息
     */
    public JsonNode getMediaServerStatus() {
        ObjectNode status = objectMapper.createObjectNode();
        status.put("connected", isMediaServerConnected());
        status.put("connectionStatus", mediaServerHandler.getConnectionStatus());
        status.put("timestamp", System.currentTimeMillis());
        
        return status;
    }
    
    // 处理来自媒体服务器的通知
    
    /**
     * 处理生产者状态变化通知
     */
    public void handleProducerStatusChange(JsonNode data) {
        String roomId = data.get("roomId").asText();
        Long userId = data.get("userId").asLong();
        String producerId = data.get("producerId").asText();
        String status = data.get("status").asText();
        String mediaType = data.has("mediaType") ? data.get("mediaType").asText() : "unknown";
        String reason = data.has("reason") ? data.get("reason").asText() : "unknown";

        logger.info("生产者状态变化: 房间={}, 用户={}, 生产者={}, 状态={}, 媒体类型={}",
                   roomId, userId, producerId, status, mediaType);

        // 通知前端客户端生产者状态变化
        if ("closed".equals(status)) {
            clientWebSocketHandler.notifyProducerClosed(roomId, userId, producerId, mediaType, reason);
        }
    }
    
    /**
     * 处理消费者状态变化通知
     */
    public void handleConsumerStatusChange(JsonNode data) {
        String roomId = data.get("roomId").asText();
        Long userId = data.get("userId").asLong();
        String consumerId = data.get("consumerId").asText();
        String status = data.get("status").asText();

        logger.info("消费者状态变化: 房间={}, 用户={}, 消费者={}, 状态={}",
                   roomId, userId, consumerId, status);

        // 通知前端客户端消费者状态变化
        if ("closed".equals(status)) {
            String message = clientWebSocketHandler.createMessage("consumerClosed",
                    Map.of("consumer_id", consumerId, "userId", userId));
            clientWebSocketHandler.broadcastToRoom(roomId, message);
        }
    }

    /**
     * 处理新生产者通知
     */
    public void handleNewProducers(JsonNode data) {
        String roomId = data.get("roomId").asText();
        JsonNode producers = data.get("producers");

        logger.info("收到新生产者通知: 房间={}, 生产者数量={}", roomId, producers.size());

        // 通知房间内的其他用户有新生产者
        clientWebSocketHandler.notifyNewProducers(roomId, producers);
    }
    
    /**
     * 处理用户离开房间通知
     */
    public void handleUserLeft(JsonNode data) {
        String roomId = data.get("roomId").asText();
        Long userId = data.get("userId").asLong();

        logger.info("用户离开媒体房间: 房间={}, 用户={}", roomId, userId);

        // 清理本地跟踪的生产者
        String key = roomId + "_" + userId;
        Set<String> removedProducers = activeProducers.remove(key);
        if (removedProducers != null) {
            logger.info("用户离开时清理本地生产者记录: roomId={}, userId={}, 数量={}", roomId, userId, removedProducers.size());
        }
    }

    /**
     * 检查并清理僵尸生产者
     */
    public void checkAndCleanupZombieProducers(String roomId, Long userId) {
        String key = roomId + "_" + userId;
        Set<String> userProducers = activeProducers.get(key);

        if (userProducers != null && !userProducers.isEmpty()) {
            logger.warn("发现用户可能的僵尸生产者: roomId={}, userId={}, 数量={}", roomId, userId, userProducers.size());

            // 尝试清理
            cleanupUserProducers(roomId, userId)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        logger.error("清理僵尸生产者失败: roomId={}, userId={}", roomId, userId, throwable);
                    } else {
                        logger.info("清理僵尸生产者完成: roomId={}, userId={}", roomId, userId);
                    }
                });
        }
    }

    /**
     * 获取活跃生产者统计
     */
    public Map<String, Object> getActiveProducersStats() {
        int totalProducers = activeProducers.values().stream()
            .mapToInt(Set::size)
            .sum();

        return Map.of(
            "totalUsers", activeProducers.size(),
            "totalProducers", totalProducers,
            "details", activeProducers
        );
    }
    
    /**
     * 通知媒体服务器用户加入房间
     */
    public void notifyUserJoined(String roomId, Long userId) {
        ObjectNode data = objectMapper.createObjectNode();
        data.put("roomId", roomId);
        data.put("userId", userId);
        
        mediaServerHandler.sendNotification("user_joined", data);
    }
    
    /**
     * 通知媒体服务器用户离开房间
     */
    public void notifyUserLeft(String roomId, Long userId) {
        ObjectNode data = objectMapper.createObjectNode();
        data.put("roomId", roomId);
        data.put("userId", userId);
        
        mediaServerHandler.sendNotification("user_left", data);
    }
    
    /**
     * 通知媒体服务器房间关闭
     */
    public void notifyRoomClosed(String roomId) {
        ObjectNode data = objectMapper.createObjectNode();
        data.put("roomId", roomId);
        
        mediaServerHandler.sendNotification("room_closed", data);
    }
}
