{"version": 3, "file": "Device.d.ts", "sourceRoot": "", "sources": ["../src/Device.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AAIxD,OAAO,EACN,SAAS,EACT,KAAK,gBAAgB,EAErB,MAAM,aAAa,CAAC;AACrB,OAAO,EACN,KAAK,cAAc,EAEnB,MAAM,6BAA6B,CAAC;AAarC,OAAO,KAAK,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAClE,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,kBAAkB,CAAC;AACzD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,SAAS,CAAC;AAIvC,MAAM,MAAM,kBAAkB,GAC3B,WAAW,GACX,UAAU,GACV,UAAU,GACV,UAAU,GACV,UAAU,GACV,YAAY,GACZ,WAAW,GACX,UAAU,GACV,UAAU,GACV,QAAQ,GACR,wBAAwB,GACxB,aAAa,CAAC;AAEjB,MAAM,MAAM,aAAa,GAAG;IAC3B;;OAEG;IACH,WAAW,CAAC,EAAE,kBAAkB,CAAC;IACjC;;OAEG;IACH,cAAc,CAAC,EAAE,cAAc,CAAC;CAChC,CAAC;AAEF;;;GAGG;AACH,wBAAsB,iBAAiB,CACtC,SAAS,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,kBAAkB,GAAG,SAAS,CAAC,CAUzC;AAED;;;;;GAKG;AACH,wBAAgB,YAAY,CAC3B,SAAS,CAAC,EAAE,MAAM,GAChB,kBAAkB,GAAG,SAAS,CAUhC;AAED,MAAM,MAAM,cAAc,GAAG,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAExE,MAAM,MAAM,oBAAoB,GAAG;IAClC,YAAY,EAAE,CAAC,SAAS,CAAC,CAAC;CAC1B,CAAC;AAEF,qBAAa,MAAM;IAElB,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAiB;IAEjD,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAS;IAEtC,OAAO,CAAC,OAAO,CAAS;IAExB,OAAO,CAAC,wBAAwB,CAAC,CAAM;IAEvC,OAAO,CAAC,oBAAoB,CAAC,CAAkB;IAG/C,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAmB;IAErD,OAAO,CAAC,iBAAiB,CAAC,CAAmB;IAE7C,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,cAAc,CACM;IAElD;;;;;OAKG;WACU,OAAO,CAAC,EACpB,WAAW,EACX,cAAc,GACd,GAAE,aAAkB,GAAG,OAAO,CAAC,MAAM,CAAC;IAoBvC;;;;OAIG;gBACS,EAAE,WAAW,EAAE,cAAc,EAAE,GAAE,aAAkB;IAuH/D;;OAEG;IACH,IAAI,WAAW,IAAI,MAAM,CAExB;IAED;;OAEG;IACH,IAAI,MAAM,IAAI,OAAO,CAEpB;IAED;;;;OAIG;IACH,IAAI,eAAe,IAAI,eAAe,CAMrC;IAED;;;;OAIG;IACH,IAAI,gBAAgB,IAAI,gBAAgB,CAMvC;IAED,IAAI,QAAQ,IAAI,cAAc,CAE7B;IAED;;OAEG;IACG,IAAI,CAAC,EACV,qBAAqB,EACrB,sBAA8B,GAC9B,EAAE;QACF,qBAAqB,EAAE,eAAe,CAAC;QACvC,sBAAsB,CAAC,EAAE,OAAO,CAAC;KACjC,GAAG,OAAO,CAAC,IAAI,CAAC;IAgGjB;;;;;OAKG;IACH,UAAU,CAAC,IAAI,EAAE,SAAS,GAAG,OAAO;IAUpC;;;;;OAKG;IACH,mBAAmB,CAAC,gBAAgB,SAAS,OAAO,GAAG,OAAO,EAAE,EAC/D,EAAE,EACF,aAAa,EACb,aAAa,EACb,cAAc,EACd,cAAc,EACd,UAAU,EACV,kBAAkB,EAClB,kBAAkB,EAClB,sBAAsB,EACtB,OAAO,GACP,EAAE,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,SAAS,CAAC,gBAAgB,CAAC;IAkBnE;;;;;OAKG;IACH,mBAAmB,CAAC,gBAAgB,SAAS,OAAO,GAAG,OAAO,EAAE,EAC/D,EAAE,EACF,aAAa,EACb,aAAa,EACb,cAAc,EACd,cAAc,EACd,UAAU,EACV,kBAAkB,EAClB,kBAAkB,EAClB,sBAAsB,EACtB,OAAO,GACP,EAAE,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,SAAS,CAAC,gBAAgB,CAAC;IAkBnE,OAAO,CAAC,eAAe;CAsDvB"}