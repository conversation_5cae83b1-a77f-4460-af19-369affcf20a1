import { FakeEvent } from './FakeEvent';
type FakeEventListener = (event: FakeEvent) => void;
export interface FakeListenerOptions {
    once?: boolean;
}
export declare class FakeEventTarget implements EventTarget {
    private listeners;
    addEventListener(type: string, callback: FakeEventListener, options?: FakeListenerOptions): void;
    removeEventListener(type: string, callback: FakeEventListener): void;
    dispatchEvent(event: Event): boolean;
}
export {};
//# sourceMappingURL=FakeEventTarget.d.ts.map