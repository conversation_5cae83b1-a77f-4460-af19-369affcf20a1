export declare class FakeEvent {
    /**
     * Constants.
     */
    readonly NONE = 0;
    readonly CAPTURING_PHASE = 1;
    readonly AT_TARGET = 2;
    readonly BUBBLING_PHASE = 3;
    /**
     * Members.
     */
    readonly type: string;
    readonly bubbles: boolean;
    readonly cancelable: boolean;
    defaultPrevented: boolean;
    readonly composed: boolean;
    readonly currentTarget: any;
    readonly eventPhase: number;
    readonly isTrusted: boolean;
    readonly target: any;
    readonly timeStamp: number;
    readonly cancelBubble: boolean;
    readonly returnValue: boolean;
    readonly srcElement: any;
    constructor(type: string, options?: {
        bubbles?: boolean;
        cancelable?: boolean;
    });
    preventDefault(): void;
    /**
     * Not implemented.
     */
    stopPropagation(): void;
    /**
     * Not implemented.
     */
    stopImmediatePropagation(): void;
    /**
     * Not implemented.
     */
    composedPath(): any[];
    /**
     * Not implemented.
     * @deprecated
     */
    initEvent(type: string, bubbles: boolean, cancelable: true): void;
}
//# sourceMappingURL=FakeEvent.d.ts.map