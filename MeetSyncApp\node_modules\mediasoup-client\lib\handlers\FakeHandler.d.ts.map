{"version": 3, "file": "FakeHandler.d.ts", "sourceRoot": "", "sources": ["../../src/handlers/FakeHandler.ts"], "names": [], "mappings": "AAMA,OAAO,EACN,gBAAgB,EAChB,KAAK,iBAAiB,EACtB,KAAK,kBAAkB,EACvB,KAAK,iBAAiB,EACtB,KAAK,qBAAqB,EAC1B,KAAK,oBAAoB,EACzB,KAAK,6BAA6B,EAClC,KAAK,4BAA4B,EACjC,KAAK,gCAAgC,EACrC,KAAK,+BAA+B,EACpC,MAAM,oBAAoB,CAAC;AAC5B,OAAO,KAAK,EACX,aAAa,EACb,cAAc,EAEd,iBAAiB,EACjB,eAAe,EACf,MAAM,cAAc,CAAC;AACtB,OAAO,KAAK,EAAE,eAAe,EAAiB,MAAM,kBAAkB,CAAC;AACvE,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,mBAAmB,CAAC;AAqD1D,MAAM,MAAM,cAAc,GAAG;IAC5B,6BAA6B,EAAE,MAAM,eAAe,CAAC;IACrD,8BAA8B,EAAE,MAAM,gBAAgB,CAAC;IACvD,2BAA2B,EAAE,MAAM,cAAc,CAAC;CAClD,CAAC;AAEF,qBAAa,WAAY,SAAQ,gBAAgB;IAEhD,OAAO,CAAC,OAAO,CAAS;IAExB,OAAO,CAAC,cAAc,CAAM;IAE5B,OAAO,CAAC,oBAAoB,CAAC,CAAmC;IAEhE,OAAO,CAAC,MAAM,CAA2C;IAEzD,OAAO,CAAC,eAAe,CAAS;IAEhC,OAAO,CAAC,YAAY,CAAK;IAEzB,OAAO,CAAC,OAAO,CAAmD;IAElE,OAAO,CAAC,iBAAiB,CAAK;IAE9B;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,cAAc,EAAE,cAAc,SACvC,WAAW;gBAGX,cAAc,EAAE,GAAG;IAM/B,IAAI,IAAI,IAAI,MAAM,CAEjB;IAED,KAAK,IAAI,IAAI;IAWb,oBAAoB,CAAC,iBAAiB,EAAE,iBAAiB,GAAG,IAAI;IAKhE,kBAAkB,CAAC,eAAe,EAAE,eAAe,GAAG,IAAI;IAIpD,wBAAwB,IAAI,OAAO,CAAC,eAAe,CAAC;IAMpD,yBAAyB,IAAI,OAAO,CAAC,gBAAgB,CAAC;IAM5D,GAAG,CAAC,EAEH,SAAS,EACT,aAAa,EACb,aAAa,EACb,cAAc,EACd,cAAc,EACd,UAAU,EACV,kBAAkB,EAClB,sBAAsB,EACtB,uBAAuB,GAEvB,EAAE,iBAAiB,GAAG,IAAI;IAcrB,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAO3D,UAAU,CAAC,aAAa,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC;IAMvD,iBAAiB,IAAI,OAAO,CAAC,cAAc,CAAC;IAM5C,IAAI,CAET,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE,kBAAkB,GAC3D,OAAO,CAAC,iBAAiB,CAAC;IA8CvB,WAAW,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAe3C,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAO5C,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAM7C,YAAY,CACjB,OAAO,EAAE,MAAM,EACf,KAAK,EAAE,gBAAgB,GAAG,IAAI,GAC5B,OAAO,CAAC,IAAI,CAAC;IAiBV,kBAAkB,CACvB,OAAO,EAAE,MAAM,EACf,YAAY,EAAE,MAAM,GAClB,OAAO,CAAC,IAAI,CAAC;IAUV,wBAAwB,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAWrE,cAAc,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC;IAMxD,eAAe,CAAC,EACrB,OAAO,EACP,iBAAiB,EACjB,cAAc,EACd,KAAK,EACL,QAAQ,GACR,EAAE,6BAA6B,GAAG,OAAO,CAAC,4BAA4B,CAAC;IA6BlE,OAAO,CACZ,WAAW,EAAE,qBAAqB,EAAE,GAClC,OAAO,CAAC,oBAAoB,EAAE,CAAC;IAyB5B,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAYhD,cAAc,CAEnB,QAAQ,EAAE,MAAM,EAAE,GAChB,OAAO,CAAC,IAAI,CAAC;IAMV,eAAe,CAEpB,QAAQ,EAAE,MAAM,EAAE,GAChB,OAAO,CAAC,IAAI,CAAC;IAOV,gBAAgB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC;IAM1D,kBAAkB,CAAC,EACxB,oBAAoB,EACpB,KAAK,EACL,QAAQ,GACR,EAAE,gCAAgC,GAAG,OAAO,CAAC,+BAA+B,CAAC;YAsBhE,cAAc;IA4B5B,OAAO,CAAC,eAAe;CAKvB"}