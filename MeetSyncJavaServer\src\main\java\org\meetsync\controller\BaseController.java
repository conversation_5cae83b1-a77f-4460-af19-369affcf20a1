package org.meetsync.controller;

import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import org.meetsync.ExceptionHandler.BusinessException;
import org.meetsync.ExceptionHandler.EnumBusinessError;
import org.meetsync.dto.CommonReturnType;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.Hashtable;
import java.util.Map;

@Slf4j
public class BaseController {
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CommonReturnType<Object> handlerException(Exception ex) {
        Map<String, Object> responseData = new Hashtable<>();
        if (ex instanceof BusinessException) {
            BusinessException businessException = (BusinessException) ex;
            responseData.put("errCode", businessException.getErrorCode());
            responseData.put("errMsg", businessException.getErrorMsg());
            return CommonReturnType.create(500, responseData, businessException.getErrorMsg());
        } else if (ex instanceof BadCredentialsException) {
            return CommonReturnType.error( "用户名或密码错误!");
        } else if (ex instanceof AccessDeniedException) {
//            ex.printStackTrace();
            return CommonReturnType.error( "权限不足");
        } else {
            responseData.put("errCode", EnumBusinessError.UNKNOWN_ERROR.getErrorCode());
            responseData.put("errMsg", EnumBusinessError.UNKNOWN_ERROR.getErrorMsg());
        }
        ex.printStackTrace();
        log.info("ex:" + ex.getMessage() + "\nexType:" + ex.getClass());
        return CommonReturnType.create(500, responseData, "服务器异常");
    }
}
