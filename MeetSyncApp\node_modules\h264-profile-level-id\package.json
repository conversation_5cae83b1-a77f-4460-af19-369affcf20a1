{"name": "h264-profile-level-id", "version": "2.2.0", "description": "TypeScript utility to process H264 profile-level-id values", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://inakibaz.me)", "license": "ISC", "repository": {"type": "git", "url": "https://github.com/versatica/h264-profile-level-id.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mediasoup"}, "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["npm-scripts.mjs", "lib"], "engines": {"node": ">=18"}, "keywords": ["webrtc", "rtp", "h264", "browser", "nodejs"], "scripts": {"prepare": "node npm-scripts.mjs prepare", "typescript:build": "node npm-scripts.mjs typescript:build", "typescript:watch": "node npm-scripts.mjs typescript:watch", "lint": "node npm-scripts.mjs lint", "format": "node npm-scripts.mjs format", "test": "node npm-scripts.mjs test", "coverage": "node npm-scripts.mjs coverage", "release:check": "node npm-scripts.mjs release:check", "release": "node npm-scripts.mjs release"}, "jest": {"verbose": true, "testEnvironment": "node", "testRegex": "src/tests/test.*\\.ts", "transform": {"^.*\\.ts$": ["ts-jest", {"diagnostics": {"ignoreCodes": ["TS151001"]}}]}, "coveragePathIgnorePatterns": ["src/tests", "src/Logger"], "cacheDirectory": ".cache/jest"}, "dependencies": {"debug": "^4.4.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/debug": "^4.1.12", "@types/jest": "^29.5.14", "@typescript-eslint/eslint-plugin": "^8.26.0", "@typescript-eslint/parser": "^8.26.0", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.2", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-prettier": "^5.2.3", "globals": "^16.0.0", "jest": "^29.7.0", "open-cli": "^8.0.0", "prettier": "^3.5.3", "ts-jest": "^29.2.6", "typescript": "^5.8.2", "typescript-eslint": "^8.26.0"}}