{"version": 3, "file": "RemoteSdp.d.ts", "sourceRoot": "", "sources": ["../../../src/handlers/sdp/RemoteSdp.ts"], "names": [], "mappings": "AAEA,OAAO,EACN,YAAY,EAGZ,MAAM,gBAAgB,CAAC;AACxB,OAAO,KAAK,EACX,aAAa,EACb,YAAY,EACZ,cAAc,EACd,QAAQ,EACR,kBAAkB,EAClB,MAAM,iBAAiB,CAAC;AACzB,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,gBAAgB,CAAC;AAC3D,OAAO,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACpE,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AAI3D,qBAAa,SAAS;IAErB,OAAO,CAAC,cAAc,CAAC,CAAgB;IAEvC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAiB;IAEjD,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAiB;IAElD,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAiB;IAElD,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAqB;IAE1D,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAU;IAEjC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAsB;IAErD,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAkC;IAE9D,OAAO,CAAC,SAAS,CAAC,CAAS;IAE3B,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAM;gBAErB,EACX,aAAa,EACb,aAAa,EACb,cAAc,EACd,cAAc,EACd,kBAAkB,EAClB,KAAa,GACb,EAAE;QACF,aAAa,CAAC,EAAE,aAAa,CAAC;QAC9B,aAAa,CAAC,EAAE,YAAY,EAAE,CAAC;QAC/B,cAAc,CAAC,EAAE,cAAc,CAAC;QAChC,cAAc,CAAC,EAAE,cAAc,CAAC;QAChC,kBAAkB,CAAC,EAAE,kBAAkB,CAAC;QACxC,KAAK,CAAC,EAAE,OAAO,CAAC;KAChB;IAiDD,mBAAmB,CAAC,aAAa,EAAE,aAAa,GAAG,IAAI;IAWvD,cAAc,CAAC,IAAI,EAAE,QAAQ,GAAG,IAAI;IAUpC;;OAEG;IACH,0BAA0B,IAAI,IAAI;IAMlC,sBAAsB,IAAI;QAAE,GAAG,EAAE,MAAM,CAAC;QAAC,QAAQ,CAAC,EAAE,MAAM,CAAA;KAAE;IAc5D,IAAI,CAAC,EACJ,gBAAgB,EAChB,QAAQ,EACR,kBAAkB,EAClB,mBAAmB,EACnB,YAAY,GACZ,EAAE;QACF,gBAAgB,EAAE,GAAG,CAAC;QACtB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,kBAAkB,EAAE,aAAa,CAAC;QAClC,mBAAmB,EAAE,aAAa,CAAC;QACnC,YAAY,CAAC,EAAE,oBAAoB,CAAC;KACpC,GAAG,IAAI;IA2BR,OAAO,CAAC,EACP,GAAG,EACH,IAAI,EACJ,kBAAkB,EAClB,QAAQ,EACR,OAAO,GACP,EAAE;QACF,GAAG,EAAE,MAAM,CAAC;QACZ,IAAI,EAAE,SAAS,CAAC;QAChB,kBAAkB,EAAE,aAAa,CAAC;QAClC,QAAQ,EAAE,MAAM,CAAC;QACjB,OAAO,EAAE,MAAM,CAAC;KAChB,GAAG,IAAI;IA6CR,iBAAiB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAMpC,yBAAyB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAM5C,2BAA2B,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAM9C,mBAAmB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAMtC;;;;;;OAMG;IACH,iBAAiB,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IAwBvC,wBAAwB,CACvB,GAAG,EAAE,MAAM,EACX,SAAS,EAAE,wBAAwB,EAAE,GACnC,IAAI;IAQP,kBAAkB,CAAC,EAClB,GAAG,EACH,kBAAkB,GAClB,EAAE;QACF,GAAG,EAAE,MAAM,CAAC;QACZ,kBAAkB,EAAE,aAAa,CAAC;KAClC,GAAG,IAAI;IAQR,mBAAmB,CAAC,EAAE,gBAAgB,EAAE,EAAE;QAAE,gBAAgB,EAAE,GAAG,CAAA;KAAE,GAAG,IAAI;IAa1E,sBAAsB,CAAC,EACtB,kBAA0B,GAC1B,GAAE;QAAE,kBAAkB,CAAC,EAAE,OAAO,CAAA;KAAO,GAAG,IAAI;IAe/C,MAAM,IAAI,MAAM;IAOhB,gBAAgB,CAAC,eAAe,EAAE,YAAY,GAAG,IAAI;IAkBrD,oBAAoB,CAAC,eAAe,EAAE,YAAY,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI;IAwC5E,iBAAiB,CAAC,GAAG,EAAE,MAAM,GAAG,YAAY;IAU5C,qBAAqB,IAAI,IAAI;CAU7B"}