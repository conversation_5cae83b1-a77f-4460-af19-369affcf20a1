<template>
  <view class="room-container page-container">
    <!-- 头部导航 -->
    <view class="header">
      <view class="room-info">
        <text class="room-name">{{ roomInfo.name || '会议室' }}</text>
        <text class="room-id">房间ID: {{ roomId }}</text>
      </view>
      <view class="header-actions">
        <text class="participant-count">👥 {{ participantCount }}</text>
        <button class="back-btn" @click="exitRoom">
          <text>🚪</text>
        </button>
      </view>
    </view>

    <!-- 主视频区域 -->
    <view class="main-video-container">
      <view class="main-video-area" :class="{ 'no-video': !hasMainVideo }">
        <!-- 使用 renderjs 处理 WebRTC 视频 -->
        <view
          ref="mainVideoContainer"
          class="video-container"
        ></view>
        
        <!-- 无视频占位符 -->
        <view v-if="!hasMainVideo" class="no-video-placeholder">
          <text class="placeholder-icon">♠</text>
          <text class="placeholder-text">暂无主播...</text>
        </view>
      </view>
    </view>

    <!-- 使用 renderjs 处理 WebRTC 音频 -->
    <view
      ref="audioContainer"
      class="audio-container"
    >
      <!-- 所有远程音频放这里面 方便管理 -->
    </view>

    <!-- 控制面板 -->
    <view class="control-panel">
      <view class="media-controls">
        <!-- 音频控制 -->
        <button 
          class="control-btn"
          :class="{ active: isAudioEnabled }"
          @click="toggleAudio"
          :disabled="!canUseAudio"
        >
          <text class="btn-icon">{{ isAudioEnabled ? '🎤' : '🔇' }}</text>
          <text class="btn-text">{{ isAudioEnabled ? '关闭音频' : '开启音频' }}</text>
        </button>

        <!-- 视频控制 -->
        <button 
          class="control-btn"
          :class="{ active: isVideoEnabled }"
          @click="toggleVideo"
          :disabled="!canUseVideo"
        >
          <text class="btn-icon">{{ isVideoEnabled ? '📹' : '📷' }}</text>
          <text class="btn-text">{{ isVideoEnabled ? '关闭视频' : '开启视频' }}</text>
        </button>

        <!-- 摄像头切换（仅移动端显示） -->
        <button 
          v-if="isMobile && isVideoEnabled"
          class="control-btn switch-camera"
          @click="switchCamera"
        >
          <text class="btn-icon">🔄</text>
          <text class="btn-text">切换摄像头</text>
        </button>
      </view>

      <!-- 其他控制 -->
      <view class="other-controls">
        <button class="control-btn secondary" @click="copyRoomLink">
          <text class="btn-icon">📋</text>
          <text class="btn-text">复制链接</text>
        </button>
        
        <button class="control-btn secondary" @click="showDeviceSettings">
          <text class="btn-icon">⚙️</text>
          <text class="btn-text">设备设置</text>
        </button>

      </view>
    </view>

    <!-- 设备设置模态框 -->
    <view v-if="showDeviceModal" class="modal-overlay" @click="closeDeviceModal">
      <view class="modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">设备设置</text>
          <button class="close-btn" @click="closeDeviceModal">✕</button>
        </view>
        
        <view class="modal-body">
          <view class="device-section">
            <text class="device-label">音频设备</text>
            <view class="device-list">
              <view
                v-for="(device, index) in audioDevices"
                :key="device.deviceId"
                class="device-item"
                :class="{ active: selectedAudioIndex === index }"
                @click="selectAudioDevice(index)"
              >
                <view class="device-info">
                  <text class="device-name">{{ device.label || `音频设备 ${index + 1}` }}</text>
                  <text class="device-id">{{ device.deviceId.substring(0, 20) }}...</text>
                </view>
                <view class="device-check" v-if="selectedAudioIndex === index">✓</view>
              </view>
            </view>
          </view>

          <view class="device-section">
            <text class="device-label">视频设备</text>
            <view class="device-list">
              <view
                v-for="(device, index) in videoDevices"
                :key="device.deviceId"
                class="device-item"
                :class="{ active: selectedVideoIndex === index }"
                @click="selectVideoDevice(index)"
              >
                <view class="device-info">
                  <text class="device-name">{{ device.label || `视频设备 ${index + 1}` }}</text>
                  <text class="device-id">{{ device.deviceId.substring(0, 20) }}...</text>
                </view>
                <view class="device-check" v-if="selectedVideoIndex === index">✓</view>
              </view>
            </view>
          </view>

          <view class="modal-actions">
            <button class="btn-secondary" @click="closeDeviceModal">取消</button>
            <button class="btn-primary" @click="applyDeviceSettings">确定</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import authManager from '../../utils/auth.js'
import apiManager from '../../utils/api.js'

export default {
  name: 'Room',
  data() {
    return {
      roomId: '',
      roomPassword: '',
      roomInfo: {},
      participantCount: 1,

      // 媒体状态
      isAudioEnabled: false,
      isVideoEnabled: false,
      hasMainVideo: false,

      // 权限
      userPermission: null,
      canUseAudio: false,
      canUseVideo: false,

      // 设备
      showDeviceModal: false,
      audioDevices: [],
      videoDevices: [],
      selectedAudioIndex: 0,
      selectedVideoIndex: 0,

      // 流数据
      mainVideoStream: null,  // 主视频流（本地或远程）
      remoteAudioStreams: [],
      localAudioStream: null,  // 本地音频流
      localVideoStream: null,  // 本地视频流
      audioProducer: null,     // 音频生产者
      videoProducer: null,     // 视频生产者

      // 状态控制
      switchingCamera: false,  // 摄像头切换状态

      // 移动端检测
      isMobile: true,

      // WebRTC 相关
      roomClient: null,
      socket: null,

      // 视频播放相关
      userHasInteracted: false,        // 用户是否已交互
      interactionPromptShown: false,   // 是否已显示交互提示
      pendingVideos: [],               // 待播放的视频队列
    }
  },
  computed: {
    selectedAudioDevice() {
      return this.audioDevices[this.selectedAudioIndex]
    },
    selectedVideoDevice() {
      return this.videoDevices[this.selectedVideoIndex]
    }
  },
  onLoad(options) {
    this.roomId = options.roomId || uni.getStorageSync('joinRoomId')
    this.roomPassword = uni.getStorageSync('joinRoomPassword')

    if (!this.roomId) {
      uni.showToast({
        title: '房间ID无效',
        icon: 'error'
      })
      this.goBack()
      return
    }

    // 添加全局用户交互监听
    this.setupGlobalInteractionListeners()

    // 立即测试设备列表加载
    console.log('=== 立即测试设备列表加载 ===')
    this.loadDevices().then(() => {
      console.log('设备列表加载测试完成')
    }).catch(error => {
      console.error('设备列表加载测试失败:', error)
    })

    this.initRoom()
  },
  onUnload() {
    this.cleanup()
  },

  mounted() {
    // 在mounted中也设置交互监听，确保在所有环境下都能工作
    this.setupGlobalInteractionListeners()
  },
  methods: {
    async initRoom() {
      try {
        // 检查用户权限
        const user = authManager.getCurrentUser()
        if (!user) {
          uni.reLaunch({
            url: '/pages/auth/login'
          })
          return
        }
        
        // 获取房间信息
        await this.loadRoomInfo()
        
        // 初始化 WebRTC 连接
        await this.initWebRTC()
        
        // 获取设备列表
        await this.loadDevices()
        
      } catch (error) {
        console.error('初始化房间失败:', error)
        uni.showToast({
          title: error.message || '加入房间失败',
          icon: 'error'
        })
        setTimeout(() => {
          this.goBack()
        }, 2000)
      }
    },
    
    async loadRoomInfo() {
      const fallbackResponse = await apiManager.getRoomDetails(this.roomId)
      this.roomInfo = fallbackResponse.room || {}
    },
    
    async initWebRTC() {
      try {
        console.log('初始化 WebRTC 连接...')

        // 导入 WebRTC Java 客户端（使用单例）
        const { webrtcJavaClient } = await import('../../utils/webrtc-java.js')
        this.roomClient = webrtcJavaClient
        
        this.updatePermissions()

        // 监听事件
        this.setupWebRTCListeners()

        // 连接到房间
        await this.roomClient.connect(this.roomId, this.roomPassword)

        console.log('WebRTC 连接成功')

        this.roomClient.userPermission = this.userPermission;

        console.log('WebRTC 客户端权限设置为:',  this.roomClient.userPermission);

        // 恢复之前的状态（如果有的话）
        await this.restoreMediaState()

      } catch (error) {
        console.error('WebRTC 初始化失败:', error)
        uni.showToast({
          title: '连接失败',
          icon: 'error'
        })
      }
    },

    setupWebRTCListeners() {
      if (!this.roomClient) return

      // 清除之前的监听器，避免重复绑定
      this.roomClient.removeAllListeners()

      // 监听参与者数量变化
      this.roomClient.on('participantCountUpdated', (count) => {
        console.log('参与者数量更新:', count)
        this.participantCount = count
        this.$forceUpdate()
      })

      // 监听用户加入
      this.roomClient.on('peerJoined', (data) => {
        console.log('用户加入:', data)
        if (data.participantCount !== undefined) {
          this.participantCount = data.participantCount
          this.$forceUpdate()
        }
      })

      // 监听用户离开
      this.roomClient.on('peerLeft', (data) => {
        console.log('用户离开:', data)
        if (data.participantCount !== undefined) {
          this.participantCount = data.participantCount
          this.$forceUpdate()
        }
      })

      // 监听消费者添加事件（新的远程流）
      this.roomClient.on('consumerAdded', ({ consumer }) => {
        console.log('=== 收到新的消费者事件 ===')
        console.log('消费者对象:', consumer)
        console.log('消费者ID:', consumer?.id)
        console.log('消费者类型:', consumer?.kind)
        console.log('消费者track:', consumer?.track)
        console.log('消费者track状态:', consumer?.track?.readyState)
        console.log('=== 开始处理消费者 ===')
        this.handleNewConsumer(consumer)
      })

      // 监听消费者移除事件
      this.roomClient.on('consumerRemoved', ({ consumerId }) => {
        console.log('消费者移除:', consumerId)
        this.handleConsumerRemoved(consumerId)
      })

      // 监听生产者添加
      this.roomClient.on('producerAdded', ({ mediaType, stream }) => {
        if (mediaType === 'video') {
          this.mainVideoStream = stream
          this.hasMainVideo = true
        }
      })

      // 监听新生产者事件（兼容旧版本）
      this.roomClient.on('newProducers', async (producers) => {
        console.log('收到新生产者（旧版本）:', producers)
        for (const producerData of producers) {
          try {
            const consumer = await this.roomClient.consume(producerData.producer_id)
            console.log('成功消费远程流:', consumer.id)
            this.handleNewConsumer(consumer)
          } catch (error) {
            console.error('消费远程流失败:', error)
          }
        }
      })

      // 监听生产者关闭事件
      this.roomClient.on('producerClosed', ({ producer_id, socket_id, username, mediaType, reason }) => {
        console.log('其他用户关闭了生产者:', { producer_id, socket_id, username, mediaType, reason })

        // 处理生产者下线，移除对应的消费者和UI组件
        this.handleProducerClosed(producer_id, socket_id, username, mediaType, reason)

        // 显示通知
        const reasonText = reason === 'user_disconnected' ? '离开了房间' : `关闭了${mediaType === 'audio' ? '音频' : '视频'}`
        uni.showToast({
          title: `${username} ${reasonText}`,
          icon: 'none',
          duration: 2000
        })
      })

      // 监听生产者添加事件（用于处理摄像头切换等）
      this.roomClient.on('producerAdded', ({ mediaType, producer, stream }) => {
        console.log('生产者添加/更新:', { mediaType, producer: producer.id })

        if (mediaType === 'video' && stream) {
          console.log('更新本地视频流显示')
          // 更新主视频流数据
          const videoStreamData = {
            srcObject: stream,
            id: 'local',
            producerId: producer.id,
            isLocal: true
          }

          this.mainVideoStream = videoStreamData
          this.localVideoStream = stream
          this.videoProducer = producer

          // 重新设置视频显示
          this.$nextTick(() => {
            this.setMainVideo(videoStreamData)
          })
        }
      })
    },

    // 恢复媒体状态
    async restoreMediaState() {
      try {
        console.log('开始恢复媒体状态...')

        // 等待一段时间，确保 WebRTC 连接完全建立
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 检查本地是否有活跃的生产者（Java客户端不需要查询服务端）
        if (this.roomClient && this.roomClient.producers) {
          console.log('检查本地活跃生产者状态')

          const videoProducer = this.roomClient.producers.get('video')
          const audioProducer = this.roomClient.producers.get('audio')

          console.log('本地生产者状态:', {
            hasVideo: !!videoProducer,
            hasAudio: !!audioProducer
          })

          // 如果有活跃的生产者，更新UI状态但不重新启动
          if (videoProducer && videoProducer.track && !videoProducer.track.ended) {
            console.log('发现活跃的视频生产者，更新UI状态')
            this.isVideoEnabled = true
            this.isVideoStarting = false
          }

          if (audioProducer && audioProducer.track && !audioProducer.track.ended) {
            console.log('发现活跃的音频生产者，更新UI状态')
            this.isAudioEnabled = true
            this.isAudioStarting = false
          }
        }

        // 强制更新界面
        this.$forceUpdate()

      } catch (error) {
        console.error('恢复媒体状态失败:', error)
      }
    },

    // 备用状态恢复方法
    fallbackRestoreState() {
      console.log('使用备用方法恢复状态')

      // 检查 WebRTC 客户端是否有活跃的生产者
      if (this.roomClient && this.roomClient.producers) {
        const videoProducer = this.roomClient.producers.get('video')
        const audioProducer = this.roomClient.producers.get('audio')

        console.log('检查现有生产者:', {
          hasVideo: !!videoProducer,
          hasAudio: !!audioProducer
        })

        // 恢复视频状态
        if (videoProducer && videoProducer.track) {
          console.log('发现活跃的视频生产者，恢复视频状态')
          this.isVideoEnabled = true
          this.hasMainVideo = true
          this.videoProducer = videoProducer

          // 创建视频流并显示
          const stream = new MediaStream([videoProducer.track])
          const videoStreamData = {
            srcObject: stream,
            id: 'local',
            producerId: videoProducer.id,
            isLocal: true
          }

          this.mainVideoStream = videoStreamData
          this.localVideoStream = stream

          // 显示视频
          this.$nextTick(() => {
            this.setMainVideo(videoStreamData)
          })

          console.log('视频状态恢复完成')
        }

        // 恢复音频状态
        if (audioProducer && audioProducer.track) {
          console.log('发现活跃的音频生产者，恢复音频状态')
          this.isAudioEnabled = true
          this.audioProducer = audioProducer

          const stream = new MediaStream([audioProducer.track])
          this.localAudioStream = stream

          console.log('音频状态恢复完成')
        }
      }
    },

    // 处理新的消费者
    handleNewConsumer(consumer) {
      console.log('处理新消费者:', {
        id: consumer.id,
        kind: consumer.kind,
        producerId: consumer.producerId,
        track: consumer.track,
        trackState: consumer.track?.readyState
      })

      if (consumer.kind === 'video') {
        // 检查track是否可用
        if (!consumer.track) {
          console.error('消费者没有track')
          return
        }

        if (consumer.track.readyState !== 'live') {
          console.warn('消费者track状态不是live:', consumer.track.readyState)
          // 等待track变为live状态
          consumer.track.addEventListener('unmute', () => {
            console.log('消费者track已unmute，重新处理')
            this.handleNewConsumer(consumer)
          }, { once: true })
          return
        }

        const stream = new MediaStream([consumer.track])
        console.log('创建远程视频流:', {
          consumerId: consumer.id,
          streamId: stream.id,
          tracks: stream.getTracks().length,
          trackState: consumer.track.readyState,
          hasLocalVideo: this.isVideoEnabled
        })

        // 验证stream是否有效
        if (stream.getTracks().length === 0) {
          console.error('创建的MediaStream没有tracks')
          return
        }

        // 如果本地没有视频，则显示远程视频作为主视频
        if (!this.isVideoEnabled && !this.hasMainVideo) {
          // 为主视频设置正确的格式（renderjs 期望的格式）
          this.mainVideoStream = {
            srcObject: stream,
            id: consumer.id,
            producerId: consumer.producerId,
            isLocal: false // 标记为远程流
          }
          this.hasMainVideo = true
          console.log('设置远程视频流为主视频')

          // 确保在下一个tick中设置视频
          this.$nextTick(() => {
            this.setMainVideo(this.mainVideoStream)
          })
        }
        this.$forceUpdate()
      } else if (consumer.kind === 'audio') {
        const stream = new MediaStream([consumer.track])
        const audioData = {
          id: consumer.id,
          stream: stream,
          srcObject: stream, // 为 renderjs 添加 srcObject 属性
          producerId: consumer.producerId,
          consumer: consumer
        }
        this.remoteAudioStreams.push(audioData)
        console.log('添加远程音频流, 总数:', this.remoteAudioStreams.length)

        // 使用 renderjs 添加音频元素
        this.$nextTick(() => {
          console.log(this.$refs.audioContainer);
          const audioEl = document.createElement("audio");
          this.$refs.audioContainer.$el.appendChild(audioEl);
          audioEl.srcObject = audioData.srcObject;
          audioEl.autoplay = true;
          audioEl.controls = false;
          audioEl.volume = 1.0;
          audioEl.id = `audio-${audioData.id}`;

          // 尝试播放音频
          this.tryPlayAudio(audioEl);
        })

        this.$forceUpdate()
      }
    },

    // 处理消费者移除
    handleConsumerRemoved(consumerId) {
      // 从远程音频流中移除
      const audioIndex = this.remoteAudioStreams.findIndex(item => item.id === consumerId)
      if (audioIndex !== -1) {
        this.remoteAudioStreams.splice(audioIndex, 1)
        console.log('移除远程音频流, 剩余:', this.remoteAudioStreams.length)

        // 使用 renderjs 移除音频元素
        this.$nextTick(() => {
          // 直接操作 DOM 移除音频元素
          const audioElement = document.querySelector(`#audio-${consumerId}`)
          if (audioElement) {
            audioElement.remove()
            console.log('音频元素已移除')
          }
        })

        this.$forceUpdate()
      }

      // 如果移除的是主视频，重置主视频状态
      if (this.mainVideoStream && this.mainVideoStream.id === consumerId) {
        this.mainVideoStream = null
        this.hasMainVideo = false
        console.log('移除主视频流')
        this.$forceUpdate()
      }
    },

    // 处理生产者关闭事件
    handleProducerClosed(producer_id, socket_id, username, mediaType, reason) {
      console.log('处理生产者关闭:', { producer_id, socket_id, username, mediaType, reason })

      // 查找并移除对应的消费者
      let removedConsumerId = null

      if (mediaType === 'audio') {
        // 查找对应的音频流
        const audioIndex = this.remoteAudioStreams.findIndex(item =>
          item.producerId === producer_id ||
          (item.consumer && item.consumer.appData && item.consumer.appData.producerId === producer_id)
        )

        if (audioIndex !== -1) {
          const audioStream = this.remoteAudioStreams[audioIndex]
          removedConsumerId = audioStream.id

          // 从数组中移除
          this.remoteAudioStreams.splice(audioIndex, 1)
          console.log(`移除音频流 ${removedConsumerId}, 剩余:`, this.remoteAudioStreams.length)

          // 直接操作 DOM 移除音频元素
          this.$nextTick(() => {
            const audioElement = document.querySelector(`#audio-${removedConsumerId}`)
            if (audioElement) {
              audioElement.remove()
              console.log('音频元素已移除')
            }
          })
        }
      } else if (mediaType === 'video') {
        // 处理视频流移除
        if (this.mainVideoStream && this.mainVideoStream.producerId === producer_id) {
          console.log('移除主视频流，显示暂无主播占位符')
          this.mainVideoStream = null
          this.hasMainVideo = false

          // 清除视频显示并显示占位符
          this.$nextTick(() => {
            this.clearMainVideo()
            this.showNoVideoPlaceholder()
          })
        }
      }

      this.$forceUpdate()
    },

    // 显示暂无主播占位符
    showNoVideoPlaceholder() {

    },

    updatePermissions() {
      // 根据用户权限更新可用功能
      const user = authManager.getCurrentUser()
      if (!user) {
        console.error('用户信息为空！')
        this.canUseAudio = false
        this.canUseVideo = false
        return
      }

      console.log('=== 权限更新调试 ===')
      console.log('当前用户信息:', JSON.stringify(user, null, 2))
      console.log('用户角色:', user.role, '类型:', typeof user.role)

      //role字段转小写
      user.role = user.role.toLowerCase()

      // 根据用户角色设置默认权限（临时给游客完整权限用于测试）
      const defaultPermissions = {
        guest: 'full_access',    // 临时：游客完整权限（用于测试）
        user: 'audio_only',      // 普通用户可以音频
        premium: 'video_audio',  // 高级用户可以音视频
        admin: 'full_access'     // 管理员完整权限
      }

      const permission = defaultPermissions[user.role] || 'view_only'
      this.userPermission = permission
      console.log("this.roomClient",this.roomClient);
      this.roomClient.userPermission = permission;
      console.log('角色映射结果:', {
        userRole: user.role,
        mappedPermission: permission,
        availableRoles: Object.keys(defaultPermissions)
      })

      // 根据权限设置功能可用性
      switch (permission) {
        case 'view_only':
          this.canUseAudio = false
          this.canUseVideo = false
          console.log('设置为只读权限')
          break
        case 'audio_only':
          this.canUseAudio = true
          this.canUseVideo = false
          console.log('设置为仅音频权限')
          break
        case 'video_audio':
        case 'full_access':
          this.canUseAudio = true
          this.canUseVideo = true
          console.log('设置为完整权限')
          break
        default:
          this.canUseAudio = false
          this.canUseVideo = false
          console.log('设置为默认无权限')
      }

      console.log('最终权限状态:', {
        canUseAudio: this.canUseAudio,
        canUseVideo: this.canUseVideo,
        userPermission: this.userPermission
      })
      console.log('=== 权限更新完成 ===')

    },

    // 强制启用视频权限（用于调试）
    forceEnableVideo() {
      console.log('🔧 强制启用视频权限')
      this.canUseVideo = true
      this.canUseAudio = true
      this.userPermission = 'full_access'

      uni.showToast({
        title: '视频权限已强制启用',
        icon: 'success'
      })

      console.log('强制启用后的状态:', {
        canUseVideo: this.canUseVideo,
        canUseAudio: this.canUseAudio,
        userPermission: this.userPermission
      })
    },
    
    async loadDevices() {
      try {
        console.log('开始加载设备列表...')

        // 检查是否支持 mediaDevices API
        if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
          console.warn('当前环境不支持 mediaDevices API')
          this.audioDevices = [{ deviceId: 'default', label: '默认音频设备' }]
          this.videoDevices = [{ deviceId: 'default', label: '默认视频设备' }]
          console.log('使用默认设备:', { audio: this.audioDevices, video: this.videoDevices })
          return
        }

        console.log('mediaDevices API 可用，开始获取设备权限...')

        // 先请求媒体权限以获取设备标签
        let stream = null
        try {
          stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
            video: true
          })
          console.log('媒体权限获取成功')
        } catch (permissionError) {
          console.warn('无法获取媒体权限:', permissionError)
          // 即使没有权限也尝试获取设备列表
        }

        // 获取媒体设备列表
        console.log('开始获取设备列表...')
        const devices = await navigator.mediaDevices.enumerateDevices()
        console.log('获取到的原始设备列表:', devices)

        this.audioDevices = devices
          .filter(device => device.kind === 'audioinput')
          .map((device, index) => ({
            deviceId: device.deviceId,
            label: device.label || `音频设备 ${index + 1}`
          }))

        this.videoDevices = devices
          .filter(device => device.kind === 'videoinput')
          .map((device, index) => ({
            deviceId: device.deviceId,
            label: device.label || `视频设备 ${index + 1}`
          }))

        console.log('处理后的设备列表:', {
          audioDevices: this.audioDevices,
          videoDevices: this.videoDevices
        })

        // 如果没有找到设备，添加默认设备
        if (this.audioDevices.length === 0) {
          this.audioDevices = [{ deviceId: 'default', label: '默认音频设备' }]
          console.log('没有找到音频设备，使用默认设备')
        }
        if (this.videoDevices.length === 0) {
          this.videoDevices = [{ deviceId: 'default', label: '默认视频设备' }]
          console.log('没有找到视频设备，使用默认设备')
        }

        // 关闭临时流
        if (stream) {
          stream.getTracks().forEach(track => track.stop())
          console.log('临时媒体流已关闭')
        }

        console.log('设备列表加载完成:', {
          audio: this.audioDevices,
          video: this.videoDevices
        })

      } catch (error) {
        console.error('获取设备列表失败:', error)
        // 设置默认设备
        this.audioDevices = [{ deviceId: 'default', label: '默认音频设备' }]
        this.videoDevices = [{ deviceId: 'default', label: '默认视频设备' }]
        console.log('设备列表获取失败，使用默认设备:', {
          audio: this.audioDevices,
          video: this.videoDevices
        })
      }
    },
    
    async toggleAudio() {
      if (!this.canUseAudio) return
      
      try {
        if (this.isAudioEnabled) {
          // 关闭音频
          await this.stopAudio()
          this.isAudioEnabled = false
        } else {
          // 开启音频
          await this.startAudio()
          this.isAudioEnabled = true
        }
      } catch (error) {
        console.error('音频切换失败:', error)
        uni.showToast({
          title: '音频操作失败',
          icon: 'error'
        })
      }
    },
    
    async toggleVideo() {
      console.log('=== toggleVideo 被调用 ===')
      const user = authManager.getCurrentUser()
      console.log('当前用户:', user)
      console.log('当前状态:', {
        canUseVideo: this.canUseVideo,
        isVideoEnabled: this.isVideoEnabled,
        hasMainVideo: this.hasMainVideo,
        roomClient: !!this.roomClient,
        userPermission: this.userPermission,
        userRole: user?.role
      })

      if (!this.canUseVideo) {
        console.log('❌ 没有视频权限，退出')
        uni.showToast({
          title: '没有视频权限',
          icon: 'error'
        })
        return
      }

      try {
        if (this.isVideoEnabled) {
          console.log('准备关闭视频')
          // 关闭视频（stopVideo 内部会更新状态）
          await this.stopVideo()
          console.log('视频已关闭')
        } else {
          console.log('准备开启视频')
          // 开启视频（startVideo 内部会更新状态）
          await this.startVideo()
          console.log('视频已开启')
        }
      } catch (error) {
        console.error('视频切换失败:', error)
        uni.showToast({
          title: '视频操作失败',
          icon: 'error'
        })
      }
    },
    
    async switchCamera() {
      if (!this.isVideoEnabled || !this.roomClient) return

      // 防抖：防止快速连续点击
      if (this.switchingCamera) {
        console.log('摄像头正在切换中，忽略重复请求')
        return
      }

      this.switchingCamera = true

      try {
        console.log('切换摄像头')

        // 清理旧的视频流
        if (this.localVideoStream) {
          console.log('清理旧的本地视频流')
          this.localVideoStream.getTracks().forEach(track => {
            if (track.readyState === 'live') {
              track.stop()
            }
          })
        }

        // 使用 WebRTC 客户端的切换摄像头功能
        const newStream = await this.roomClient.switchCamera()

        // 更新本地视频流显示
        if (newStream) {
          console.log('摄像头切换成功，更新本地视频显示')

          // 更新主视频流数据
          const videoStreamData = {
            srcObject: newStream,
            id: 'local',
            producerId: this.videoProducer?.id,
            isLocal: true
          }

          this.mainVideoStream = videoStreamData
          this.localVideoStream = newStream

          // 重新设置视频显示
          console.log('重新设置视频显示')
          this.$nextTick(() => {
            this.setMainVideo(videoStreamData)
          })
        }

        uni.showToast({
          title: '摄像头已切换',
          icon: 'success'
        })
      } catch (error) {
        console.error('切换摄像头失败:', error)
        uni.showToast({
          title: '切换失败',
          icon: 'error'
        })
      } finally {
        // 延迟重置状态，避免过快的重复调用
        setTimeout(() => {
          this.switchingCamera = false
        }, 1000)
      }
    },
    
    async startAudio() {
      try {
        if (!this.roomClient) {
          throw new Error('WebRTC 客户端未初始化')
        }

        console.log('开启音频')
        const { producer, stream } = await this.roomClient.produce('audio', this.selectedAudioDevice?.deviceId)

        console.log('音频生产者创建成功:', producer.id)

        // 本地音频通常不需要播放（避免回音）
        // 只保存流和生产者信息用于管理

        // 添加到本地音频流列表（如果需要的话）
        this.localAudioStream = stream
        this.audioProducer = producer
        this.isAudioEnabled = true

        // 使用 renderjs 添加本地音频元素（通常本地音频不需要播放，但为了一致性）
        console.log('本地音频开启成功')

      } catch (error) {
        console.error('开启音频失败:', error)
        throw error
      }
    },

    async stopAudio() {
      try {
        if (!this.roomClient) return

        console.log('关闭音频')
        await this.roomClient.closeProducer('audio')

        // 清理本地音频状态
        this.localAudioStream = null
        this.audioProducer = null
        this.isAudioEnabled = false

        console.log('本地音频关闭成功')

      } catch (error) {
        console.error('关闭音频失败:', error)
        throw error
      }
    },

    async startVideo() {
      try {
        if (!this.roomClient) {
          throw new Error('WebRTC 客户端未初始化')
        }

        console.log('开启视频')
        const { producer, stream } = await this.roomClient.produce('video', this.selectedVideoDevice?.deviceId)

        console.log('视频生产者创建成功:', producer.id)
        this.videoProducer = producer

        // 为本地视频设置正确的格式
        const videoStreamData = {
          srcObject: stream,
          id: 'local',
          producerId: producer.id,
          isLocal: true // 标记为本地流
        }

        this.mainVideoStream = videoStreamData
        this.hasMainVideo = true
        this.localVideoStream = stream
        this.isVideoEnabled = true

        // 调用 Vue 组件的 setMainVideo 方法显示视频
        console.log('调用 setMainVideo 显示视频')
        this.$nextTick(() => {
          this.setMainVideo(videoStreamData)
        })

        console.log('视频开启成功，设置为主视频')
      } catch (error) {
        console.error('开启视频失败:', error)
        throw error
      }
    },

    async stopVideo() {
      try {
        if (!this.roomClient) return

        console.log('关闭视频')

        // 先更新状态，避免重复调用
        this.isVideoEnabled = false
        this.hasMainVideo = false

        await this.roomClient.closeProducer('video')

        this.mainVideoStream = null
        this.localVideoStream = null
        this.videoProducer = null

        // 清除视频显示
        console.log('清除视频显示')
        this.$nextTick(() => {
          this.clearMainVideo()
        })
        console.log('视频关闭成功')

      } catch (error) {
        console.error('关闭视频失败:', error)
        // 如果关闭失败，恢复状态
        this.isVideoEnabled = true
        this.hasMainVideo = true
        throw error
      }
    },
    
    copyRoomLink() {
      const link = `${window.location.origin}/pages/room/index?roomId=${this.roomId}`
      
      // uniapp 复制到剪贴板
      uni.setClipboardData({
        data: link,
        success: () => {
          uni.showToast({
            title: '链接已复制',
            icon: 'success'
          })
        }
      })
    },
    
    showDeviceSettings() {
      console.log('显示设备设置，当前设备列表:', {
        audioDevices: this.audioDevices,
        videoDevices: this.videoDevices,
        audioCount: this.audioDevices.length,
        videoCount: this.videoDevices.length
      })
      this.showDeviceModal = true
    },
    
    closeDeviceModal() {
      this.showDeviceModal = false
    },
    
    selectAudioDevice(index) {
      this.selectedAudioIndex = index
      console.log('选择音频设备:', this.audioDevices[index])
    },

    selectVideoDevice(index) {
      this.selectedVideoIndex = index
      console.log('选择视频设备:', this.videoDevices[index])
    },

    applyDeviceSettings() {
      // 应用设备设置
      console.log('应用设备设置:', {
        audio: this.selectedAudioDevice,
        video: this.selectedVideoDevice
      })

      uni.showToast({
        title: '设备设置已保存',
        icon: 'success'
      })

      this.closeDeviceModal()
    },

    onAudioDeviceChange(e) {
      this.selectedAudioIndex = e.detail.value
      // TODO: 切换音频设备
    },

    onVideoDeviceChange(e) {
      this.selectedVideoIndex = e.detail.value
      // TODO: 切换视频设备
    },

     // 设置主视频流
    setMainVideo(newVal) {
      console.log("视频流数据", newVal);
      console.log("视频流数据类型:", typeof newVal);
      console.log("视频流数据详情:", JSON.stringify(newVal, null, 2));

      const videoStreamData = newVal

      if (!videoStreamData) {
        console.log('videoStreamData 为空，清除视频')
        this.clearMainVideo()
        return
      }

      if (!videoStreamData.srcObject) {
        console.log('videoStreamData 没有 srcObject，清除视频')
        console.log('videoStreamData 属性:', Object.keys(videoStreamData))
        this.clearMainVideo()
        return
      }

      // 检查srcObject是否是有效的MediaStream
      if (!(videoStreamData.srcObject instanceof MediaStream)) {
        console.error('srcObject 不是 MediaStream:', videoStreamData.srcObject)
        return
      }

      // 检查MediaStream是否有tracks
      const tracks = videoStreamData.srcObject.getTracks()
      console.log('MediaStream tracks:', tracks.length, tracks.map(t => ({ kind: t.kind, state: t.readyState })))

      if (tracks.length === 0) {
        console.error('MediaStream 没有 tracks')
        return
      }

      console.log('设置主视频流:', videoStreamData.id, videoStreamData.isLocal ? '本地' : '远程')

      // 清除现有的 video 元素
      this.clearMainVideo()

      // 创建新的 video 元素
      const videoElement = document.createElement('video')
      videoElement.autoplay = true
      videoElement.playsInline = true
      videoElement.controls = false
      videoElement.style.width = '100%'
      videoElement.style.height = '100%'
      videoElement.style.objectFit = 'cover'
      videoElement.style.borderRadius = '6px'
      videoElement.style.backgroundColor = '#000'
      videoElement.id = 'main-video' // 添加ID便于管理

      // 设置视频流
      videoElement.srcObject = videoStreamData.srcObject
      // 远程视频也设为静音，避免自动播放限制
      videoElement.muted = true

      // 监听视频加载事件
      videoElement.onloadedmetadata = () => {
        console.log('视频元数据加载完成:', {
          videoWidth: videoElement.videoWidth,
          videoHeight: videoElement.videoHeight,
          duration: videoElement.duration
        })
      }

      videoElement.oncanplay = () => {
        console.log('视频可以播放')
        this.tryPlayVideo(videoElement, videoStreamData.isLocal)
      }

      videoElement.onplay = () => {
        console.log('视频开始播放')
      }

      videoElement.onerror = (error) => {
        console.error('视频播放错误:', error)
      }

      videoElement.onloadstart = () => {
        console.log('视频开始加载')
      }

      // 添加到容器
      if (this.$refs.mainVideoContainer && this.$refs.mainVideoContainer.$el) {
        this.$refs.mainVideoContainer.$el.appendChild(videoElement)
        console.log('新 video 元素已添加到容器')
      } else {
        console.error('找不到视频容器')
      }
    },

    // 智能视频播放方法
    async tryPlayVideo(videoElement, isLocal = false) {
      try {
        console.log('尝试播放视频:', { isLocal, muted: videoElement.muted })

        // 首先尝试静音播放
        videoElement.muted = true
        await videoElement.play()
        console.log('视频播放成功（静音）')

        // 如果是远程视频且用户已交互，尝试取消静音
        if (!isLocal && this.userHasInteracted) {
          try {
            videoElement.muted = false
            console.log('远程视频取消静音成功')
          } catch (e) {
            console.log('远程视频取消静音失败，保持静音:', e.message)
            videoElement.muted = true
          }
        }

      } catch (error) {
        console.log('静音播放失败，等待用户交互:', error.message)

        // 添加到待播放队列
        this.pendingVideos.push({
          element: videoElement,
          isLocal: isLocal
        })

        // 显示用户交互提示
        this.showInteractionPrompt()
      }
    },

    // 显示用户交互提示
    showInteractionPrompt() {
      if (this.interactionPromptShown) return

      this.interactionPromptShown = true
      console.log('显示用户交互提示')

      // 创建交互提示覆盖层
      const overlay = document.createElement('div')
      overlay.id = 'interaction-prompt'
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        color: white;
        font-size: 18px;
        text-align: center;
        cursor: pointer;
      `

      overlay.innerHTML = `
        <div>
          <div style="font-size: 48px; margin-bottom: 20px;">🎥</div>
          <div>点击任意位置开始播放视频</div>
          <div style="font-size: 14px; margin-top: 10px; opacity: 0.7;">
            浏览器需要用户交互才能播放视频
          </div>
        </div>
      `

      // 点击事件处理
      overlay.addEventListener('click', () => {
        this.handleUserInteraction()
        overlay.remove()
      })

      document.body.appendChild(overlay)

      // 5秒后自动移除提示
      setTimeout(() => {
        if (overlay.parentNode) {
          overlay.remove()
        }
      }, 5000)
    },

    // 处理用户交互
    async handleUserInteraction() {
      console.log('用户已交互，开始播放待播放的视频')
      this.userHasInteracted = true
      this.interactionPromptShown = false

      // 播放所有待播放的媒体
      for (const { element, isLocal, isAudio } of this.pendingVideos) {
        try {
          if (element && element.parentNode) {
            if (isAudio) {
              // 音频播放
              await element.play()
              console.log('待播放音频播放成功')
            } else {
              // 视频播放
              element.muted = isLocal ? true : false
              await element.play()
              console.log('待播放视频播放成功:', { isLocal })
            }
          }
        } catch (error) {
          console.error('待播放媒体播放失败:', error)
        }
      }

      // 清空待播放队列
      this.pendingVideos = []
    },

    // 设置全局用户交互监听
    setupGlobalInteractionListeners() {
      const events = ['click', 'touchstart', 'keydown']

      const handleInteraction = () => {
        if (!this.userHasInteracted) {
          console.log('检测到用户交互')
          this.handleUserInteraction()
        }

        // 移除监听器，只需要一次交互
        events.forEach(event => {
          document.removeEventListener(event, handleInteraction)
        })
      }

      // 添加监听器
      events.forEach(event => {
        document.addEventListener(event, handleInteraction, { passive: true })
      })

      console.log('全局用户交互监听器已设置')
    },

    // 尝试播放音频
    async tryPlayAudio(audioElement) {
      try {
        await audioElement.play()
        console.log('音频播放成功')
      } catch (error) {
        console.log('音频自动播放失败，等待用户交互:', error.message)

        // 添加到待播放队列
        this.pendingVideos.push({
          element: audioElement,
          isLocal: false,
          isAudio: true
        })
      }
    },

    clearMainVideo() {
      // 查找所有 video 元素并清理
      const videoElements = this.$el.querySelectorAll('video')
      videoElements.forEach(videoElement => {
        console.log('清理 video 元素:', videoElement.id || 'unnamed')

        // 只清除 srcObject，不停止轨道（轨道由 WebRTC 客户端管理）
        if (videoElement.srcObject) {
          console.log('清除视频元素的 srcObject')
          videoElement.srcObject = null
        }

        // 移除元素
        videoElement.remove()
      })

      if (videoElements.length > 0) {
        console.log(`已清理 ${videoElements.length} 个 video 元素`)
      }

      // 如果没有主视频流，显示占位符
      if (!this.hasMainVideo) {
        this.showNoVideoPlaceholder()
      }
    },
    updateRemoteAudio(newVal) {
      if (!newVal || !Array.isArray(newVal)) {
        return
      }
      // 清除现有音频元素
      this.$el.innerHTML = ''
      // 为每个远程音频流创建 audio 元素
      newVal.forEach((audioStream, index) => {
        if (audioStream && audioStream.srcObject) {
          const audioElement = document.createElement('audio')
          audioElement.autoplay = true
          audioElement.srcObject = audioStream.srcObject
          audioElement.id = `remote-audio-${index}`
          this.$el.appendChild(audioElement)
          console.log(`Remote audio ${index} set successfully`)
        }
      })
    },

    exitRoom() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出房间吗？',
        success: (res) => {
          if (res.confirm) {
            this.cleanup()
            this.goBack()
          }
        }
      })
    },
    
    goBack() {
      uni.navigateBack({
        delta: 1,
        fail: () => {
          uni.reLaunch({
            url: '/pages/dashboard/index'
          })
        }
      })
    },
    
    cleanup() {
      // 清理 WebRTC 连接
      if (this.roomClient) {
        this.roomClient.exit()
        this.roomClient = null
      }
      
      if (this.socket) {
        this.socket.disconnect()
        this.socket = null
      }
      
      // 清理本地存储
      uni.removeStorageSync('joinRoomId')
      uni.removeStorageSync('joinRoomPassword')
    }
  }
}
</script>

<style lang="scss" scoped>
.room-container {
  background: #000;
}

.header {
  background: linear-gradient(135deg, #2D2D2D 0%, #1A1A1A 100%);
  border-bottom: 2rpx solid #FFD700;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #FFD700;
  z-index: 100;
}

.room-info {
  flex: 1;
}

.room-name {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.room-id {
  font-size: 24rpx;
  opacity: 0.8;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.participant-count {
  font-size: 24rpx;
  background: rgba(255, 215, 0, 0.2);
  border: 1rpx solid #FFD700;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.back-btn {
  background: rgba(255, 215, 0, 0.2);
  border: 1rpx solid #FFD700;
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.main-video-container {
  flex: 1;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-video-area {
  width: 100%;
  aspect-ratio: 16/9;
  max-height: 70vh;
  background: #1a1a1a;
  border: 2rpx solid #FFD700;
  border-radius: 12rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(255, 215, 0, 0.2);
}

.video-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.audio-container {
  position: absolute;
  top: -9999px;
  left: -9999px;
  visibility: hidden;
  pointer-events: none;
}

.no-video-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #B8860B;
}

.placeholder-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.placeholder-text {
  font-size: 28rpx;
  opacity: 0.8;
}

.control-panel {
  background: linear-gradient(135deg, #2D2D2D 0%, #1A1A1A 100%);
  border-top: 2rpx solid #FFD700;
  padding: 30rpx;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
}

.media-controls {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.other-controls {
  display: flex;
  justify-content: center;
  gap: 20rpx;
}

.control-btn {
  background: #444444;
  border: 1rpx solid #666666;
  border-radius: 16rpx;
  padding: 20rpx;
  min-width: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #E5E5E5;
  transition: all 0.3s;
}

.control-btn:active {
  transform: scale(0.95);
}

.control-btn.active {
  background: #FFD700;
  color: #1A1A1A;
  border-color: #FFD700;
}

.control-btn.secondary {
  background: #B8860B;
  border-color: #B8860B;
  min-width: 100rpx;
}

.control-btn:disabled {
  background: #2D2D2D !important;
  color: #666666 !important;
  border-color: #444444 !important;
  cursor: not-allowed;
  opacity: 0.6;
}

.switch-camera {
  background: #32CD32;
  border-color: #32CD32;
}

.btn-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.btn-text {
  font-size: 22rpx;
  text-align: center;
  line-height: 1.2;
}

.hidden-audio-container {
  display: none;
}

/* 远程视频样式 */
.remote-videos {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  z-index: 10;
}

.remote-video-item {
  width: 150rpx;
  height: 100rpx;
  background: #333;
  border-radius: 8rpx;
  border: 2rpx solid #555;
  position: relative;
  overflow: hidden;
}

.remote-video-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.remote-video-debug {
  background: rgba(255, 255, 0, 0.8);
  color: black;
  padding: 4rpx 8rpx;
  font-size: 20rpx;
  border-radius: 4rpx;
  margin-bottom: 8rpx;
}

.remote-video-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20rpx;
  color: #ccc;
  text-align: center;
}

.remote-video-label {
  position: absolute;
  bottom: 4rpx;
  left: 4rpx;
  font-size: 20rpx;
  color: white;
  background: rgba(0, 0, 0, 0.7);
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal {
  background: #2D2D2D;
  border: 2rpx solid #FFD700;
  border-radius: 16rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  color: #E5E5E5;
}

.modal-header {
  padding: 30rpx;
  border-bottom: 2rpx solid #444;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #FFD700;
}

.close-btn {
  background: none;
  border: none;
  font-size: 32rpx;
  color: #B8860B;
  padding: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
}

.device-section {
  margin-bottom: 30rpx;
}

.device-label {
  display: block;
  font-size: 28rpx;
  margin-bottom: 12rpx;
  color: #FFD700;
}

.device-list {
  max-height: 200rpx;
  overflow-y: auto;
}

.device-item {
  background: #1A1A1A;
  border: 2rpx solid #444444;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 12rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
}

.device-item:hover {
  background: #333333;
  border-color: #666666;
}

.device-item.active {
  background: #FFD700;
  border-color: #FFD700;
  color: #1A1A1A;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 28rpx;
  color: #E5E5E5;
  display: block;
  margin-bottom: 4rpx;
}

.device-id {
  font-size: 22rpx;
  color: #B8860B;
  opacity: 0.8;
}

.device-check {
  color: #1A1A1A;
  font-size: 32rpx;
  font-weight: bold;
}

.modal-actions {
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #444444;
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.btn-primary {
  background: #FFD700;
  color: #1A1A1A;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.btn-secondary {
  background: #B8860B;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.btn-primary:disabled {
  background: #2D2D2D !important;
  color: #8B7355 !important;
  border: 1rpx solid #444444 !important;
  cursor: not-allowed;
  opacity: 0.8;
}

.btn-secondary:disabled {
  background: #2D2D2D !important;
  color: #666666 !important;
  border: 1rpx solid #444444 !important;
  cursor: not-allowed;
  opacity: 0.8;
}

.device-select-wrapper {
  position: relative;
  z-index: 10000; /* 确保在模态框内也能正常显示 */
}

.device-picker-component {
  width: 100%;
  position: relative;
  z-index: 10001;
}

.device-picker {
  background: #333;
  border: 2rpx solid #555;
  border-radius: 8rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  cursor: pointer;
  transition: all 0.3s;
}

.device-picker:hover {
  background: #444;
  border-color: #666;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
  transition: transform 0.3s;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .main-video-area {
    aspect-ratio: 16/9;
    max-height: 50vh;
  }

  .control-btn {
    min-width: 100rpx;
    padding: 16rpx;
  }

  .btn-icon {
    font-size: 28rpx;
  }

  .btn-text {
    font-size: 20rpx;
  }

  .media-controls {
    gap: 15rpx;
  }

  .other-controls {
    gap: 15rpx;
  }
}
</style>
