package org.meetsync.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

public class RoomCreateDto {

    @NotBlank(message = "房间ID不能为空")
    @Size(max = 100, message = "房间ID长度不能超过100个字符")
    private String roomId;

    @NotBlank(message = "房间名称不能为空")
    @Size(max = 255, message = "房间名称长度不能超过255个字符")
    private String name;

    @Size(max = 1000, message = "房间描述长度不能超过1000个字符")
    private String description;

    @Min(value = 1, message = "最大参与人数至少为1")
    @Max(value = 100, message = "最大参与人数不能超过100")
    private Integer maxParticipants = 10;

    private Boolean isPublic = true;

    @Size(max = 100, message = "房间密码长度不能超过100个字符")
    private String password;

    private String requiredRole = "user";

    // Constructors
    public RoomCreateDto() {}

    public RoomCreateDto(String roomId, String name) {
        this.roomId = roomId;
        this.name = name;
    }

    public RoomCreateDto(String roomId, String name, String description, Integer maxParticipants, Boolean isPublic, String password, String requiredRole) {
        this.roomId = roomId;
        this.name = name;
        this.description = description;
        this.maxParticipants = maxParticipants;
        this.isPublic = isPublic;
        this.password = password;
        this.requiredRole = requiredRole;
    }

    // Getters and Setters
    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getMaxParticipants() {
        return maxParticipants;
    }

    public void setMaxParticipants(Integer maxParticipants) {
        this.maxParticipants = maxParticipants;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRequiredRole() {
        return requiredRole;
    }

    public void setRequiredRole(String requiredRole) {
        this.requiredRole = requiredRole;
    }

    @Override
    public String toString() {
        return "RoomCreateDto{" +
                "roomId='" + roomId + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", maxParticipants=" + maxParticipants +
                ", isPublic=" + isPublic +
                ", requiredRole='" + requiredRole + '\'' +
                '}';
    }
}
