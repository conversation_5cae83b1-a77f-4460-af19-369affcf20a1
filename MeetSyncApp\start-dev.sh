#!/bin/bash

echo "========================================"
echo "MeetSync App端 - Java后端版本启动脚本"
echo "========================================"
echo

echo "检查依赖..."
if [ ! -d "node_modules" ]; then
    echo "安装依赖包..."
    npm install
    if [ $? -ne 0 ]; then
        echo "依赖安装失败！"
        exit 1
    fi
fi

echo
echo "检查后端服务状态..."
echo "请确保以下服务正在运行："
echo "  - Java后端服务 (端口8080)"
echo "  - NodeJS媒体服务器 (端口3016)"
echo

echo "启动App端开发服务器..."
npm run dev
