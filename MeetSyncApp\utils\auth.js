/**
 * 认证工具类
 */
class AuthManager {
  constructor() {
    this.token = uni.getStorageSync('auth_token') || null
    this.user = uni.getStorageSync('user') || null
  }

  /**
   * 获取当前用户
   */
  getCurrentUser() {
    return this.user
  }

  /**
   * 获取认证令牌
   */
  getAuthToken() {
    return this.token
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn() {
    return !!(this.token && this.user)
  }

  /**
   * 设置认证信息
   */
  setAuth(token, user) {
    this.token = token
    this.user = user
    
    uni.setStorageSync('auth_token', token)
    uni.setStorageSync('user', user)
  }

  /**
   * 清除认证信息
   */
  clearAuth() {
    this.token = null
    this.user = null
    
    uni.removeStorageSync('auth_token')
    uni.removeStorageSync('user')
  }

  /**
   * 登录
   */
  async login(username, password) {
    try {
      const response = await this.apiCall('/auth/login', 'POST', {
        username,
        password
      })

      if (response.token) {
        this.setAuth(response.token, response.user)
        return response
      }

      throw new Error('登录失败')
    } catch (error) {
      throw error
    }
  }

  /**
   * 游客登录 - 已禁用
   */
  guestLogin() {
    throw new Error('游客登录功能已禁用，请使用正式账号登录')
  }

  /**
   * 登出
   */
  logout() {
    this.clearAuth()
    
    // 跳转到登录页
    uni.reLaunch({
      url: '/pages/auth/login'
    })
  }

  /**
   * API 调用
   */
  async apiCall(url, method = 'GET', data = null) {
    // 导入配置
    const config = require('../config/index.js').default
    const baseUrl = config.server.baseUrl

    const options = {
      url: baseUrl + url,
      method,
      header: {
        'Content-Type': 'application/json'
      },
      timeout: 30000,
      // SSL 兼容性配置
      sslVerify: false,
      enableHttp2: false,
      enableQuic: false,
      enableCache: false,
      enableHttpDNS: false
    }

    if (data) {
      options.data = data
    }

    if (this.token && this.token !== 'guest_token') {
      options.header.Authorization = `Bearer ${this.token}`
    }

    return new Promise((resolve, reject) => {
      uni.request({
        ...options,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(new Error((res.data && res.data.error) || '请求失败'))
          }
        },
        fail: (error) => {
          reject(new Error('网络请求失败'))
        }
      })
    })
  }
}

// 创建全局实例
const authManager = new AuthManager()

export default authManager
