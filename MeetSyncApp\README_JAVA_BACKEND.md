# MeetSync App端 - Java后端版本

## 概述

本版本的MeetSync App端已完全适配Java后端，通过统一的API和WebSocket连接实现视频会议功能。

## 架构说明

```
App端 → Java后端(8080) → NodeJS媒体服务器(3016)
      ↓
   统一认证、API管理、媒体代理
```

## 主要特性

- ✅ **统一认证**: 通过Java后端进行用户登录和权限管理
- ✅ **API集成**: 所有房间管理API通过Java后端调用
- ✅ **媒体代理**: WebRTC媒体流通过Java后端代理
- ✅ **实时通信**: WebSocket连接Java后端进行实时消息传递
- ✅ **测试工具**: 内置Java后端连接测试页面

## 快速开始

### 1. 启动后端服务

确保以下服务正在运行：

```bash
# 启动Java后端 (端口8080)
cd MeetSyncJavaServer
./mvnw spring-boot:run

# 启动NodeJS媒体服务器 (端口3016)
cd MeetSyncServer
npm start
```

### 2. 配置App端

App端配置文件 `config/index.js` 已自动配置：

```javascript
// 开发环境
{
  baseUrl: 'http://localhost:8080/api',
  wsUrl: 'ws://localhost:8080/media-ws',
  mediaApiUrl: 'http://localhost:8080/api/media'
}
```

### 3. 运行App端

```bash
cd MeetSyncApp
npm install
npm run dev
```

## 功能说明

### 用户认证
- 登录页面：`/pages/auth/login`
- 支持用户名/密码登录
- 自动获取JWT token并存储

### 房间管理
- 主页面：`/pages/dashboard/index`
- 查看公共房间列表
- 创建和管理个人房间
- 支持房间密码保护

### 视频会议
- 房间页面：`/pages/room/index`
- 音视频通话功能
- 摄像头切换（前置/后置）
- 屏幕共享支持

### 测试工具
- 测试页面：`/pages/test/java-backend`
- 验证Java后端连接状态
- 测试API和媒体服务器
- 调试WebRTC功能

## API适配说明

### 数据格式变化

**房间数据结构**:
```javascript
// 原NodeJS格式
{
  room_id: "ABC123",
  creator_name: "用户名",
  created_at: "2024-01-01T00:00:00Z",
  has_password: true
}

// 新Java后端格式
{
  roomId: "ABC123",
  creator: { username: "用户名" },
  createdAt: "2024-01-01T00:00:00",
  hasPassword: true
}
```

### WebRTC客户端

使用新的 `webrtc-java.js` 客户端：
```javascript
import { webrtcJavaClient } from '../../utils/webrtc-java.js'

// 连接房间
await webrtcJavaClient.connect(roomId, password)

// 开启视频
await webrtcJavaClient.produce('video')

// 开启音频
await webrtcJavaClient.produce('audio')
```

## 故障排除

### 1. 连接问题

如果无法连接Java后端：
1. 确认Java后端服务正在运行（端口8080）
2. 检查网络连接和防火墙设置
3. 使用测试页面验证连接状态

### 2. 媒体问题

如果音视频功能异常：
1. 确认NodeJS媒体服务器正在运行（端口3016）
2. 检查Java后端与NodeJS的WebSocket连接
3. 验证浏览器媒体权限设置

### 3. 认证问题

如果登录失败：
1. 确认用户名和密码正确
2. 检查Java后端数据库连接
3. 验证JWT token配置

## 开发调试

### 启用调试日志

在浏览器控制台中查看详细日志：
```javascript
// 查看WebRTC连接状态
console.log(webrtcJavaClient.isConnected)

// 查看当前生产者
console.log(webrtcJavaClient.producers)

// 查看当前消费者
console.log(webrtcJavaClient.consumers)
```

### 测试API连接

使用内置测试页面：
1. 在主页面点击🔧按钮
2. 运行各项连接测试
3. 查看详细错误信息

## 部署说明

### 生产环境配置

更新 `config/index.js` 中的生产环境配置：
```javascript
[ENV.PRODUCTION]: {
  baseUrl: "https://your-domain.com/api",
  wsUrl: "wss://your-domain.com/media-ws",
  mediaApiUrl: "https://your-domain.com/api/media"
}
```

### HTTPS支持

确保生产环境使用HTTPS：
- Java后端配置SSL证书
- WebSocket使用WSS协议
- 媒体流需要安全上下文

## 更新日志

### v2.0.0 (Java后端版本)
- ✅ 完全适配Java后端API
- ✅ 新增WebRTC Java客户端
- ✅ 统一认证和权限管理
- ✅ 媒体流代理功能
- ✅ 内置测试工具
- ✅ 改进错误处理和用户体验

## 技术支持

如有问题，请检查：
1. 后端服务状态
2. 网络连接
3. 浏览器兼容性
4. 控制台错误日志

更多技术细节请参考 `JAVA_BACKEND_MIGRATION.md` 文档。
