#!/usr/bin/env node

/**
 * 快速启用 PWA 功能的脚本
 */

const fs = require('fs')
const path = require('path')

const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  red: '\x1b[31m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function enablePWA() {
  log('🚀 启用 PWA 功能...', 'blue')
  
  // 1. 检查图标文件
  const iconDir = path.join(__dirname, '../static/icons')
  const requiredIcons = [
    'icon-192x192.svg',
    'icon-512x512.svg',
    'favicon-32x32.svg'
  ]
  
  log('📋 检查图标文件...', 'yellow')
  let allIconsExist = true
  
  requiredIcons.forEach(icon => {
    const iconPath = path.join(iconDir, icon)
    if (fs.existsSync(iconPath)) {
      log(`  ✅ ${icon}`, 'green')
    } else {
      log(`  ❌ ${icon} 不存在`, 'red')
      allIconsExist = false
    }
  })
  
  if (!allIconsExist) {
    log('⚠️  请先生成图标文件！', 'yellow')
    log('💡 运行以下命令生成图标：', 'blue')
    log('   1. 在浏览器中打开 scripts/generate-icons.html', 'blue')
    log('   2. 生成并下载图标文件', 'blue')
    log('   3. 将图标文件放入 static/icons/ 目录', 'blue')
    return false
  }
  
  // 2. 更新 index.html
  log('📝 更新 index.html...', 'yellow')
  const indexPath = path.join(__dirname, '../index.html')
  
  if (fs.existsSync(indexPath)) {
    let indexContent = fs.readFileSync(indexPath, 'utf8')
    
    // 恢复 PWA 相关的 meta 标签
    indexContent = indexContent.replace(
      '<!-- 基础图标 -->',
      `<!-- PWA 支持 -->
    <meta name="theme-color" content="#007bff">
    <link rel="manifest" href="./static/manifest.json">
    
    <!-- 图标 -->`
    )
    
    indexContent = indexContent.replace(
      '<link rel="icon" href="data:image/svg+xml,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 100 100\'><text y=\'.9em\' font-size=\'90\'>📹</text></svg>">',
      `<link rel="icon" type="image/svg+xml" href="./static/icons/favicon-32x32.svg">
    <link rel="apple-touch-icon" sizes="180x180" href="./static/icons/icon-192x192.svg">`
    )
    
    // 恢复 Service Worker 注册
    indexContent = indexContent.replace(
      /\/\* PWA 支持已暂时禁用[\s\S]*?\*\//,
      `// PWA 支持
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./sw.js')
                    .then(registration => {
                        console.log('✅ SW registered:', registration);
                    })
                    .catch(error => {
                        console.log('❌ SW registration failed:', error);
                    });
            });
        }`
    )
    
    fs.writeFileSync(indexPath, indexContent)
    log('  ✅ index.html 已更新', 'green')
  }
  
  // 3. 更新 public/index.html
  log('📝 更新 public/index.html...', 'yellow')
  const publicIndexPath = path.join(__dirname, '../public/index.html')
  
  if (fs.existsSync(publicIndexPath)) {
    let publicIndexContent = fs.readFileSync(publicIndexPath, 'utf8')
    
    publicIndexContent = publicIndexContent.replace(
      '<!-- 基础图标 - 使用 emoji 作为临时图标 -->',
      `<!-- PWA 支持 -->
    <meta name="theme-color" content="#007bff">
    <link rel="manifest" href="<%= BASE_URL %>static/manifest.json">
    
    <!-- 图标 -->`
    )
    
    publicIndexContent = publicIndexContent.replace(
      '<link rel="icon" href="data:image/svg+xml,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 100 100\'><text y=\'.9em\' font-size=\'90\'>📹</text></svg>">',
      `<link rel="icon" type="image/svg+xml" href="<%= BASE_URL %>static/icons/favicon-32x32.svg">
    <link rel="apple-touch-icon" sizes="180x180" href="<%= BASE_URL %>static/icons/icon-192x192.svg">`
    )
    
    fs.writeFileSync(publicIndexPath, publicIndexContent)
    log('  ✅ public/index.html 已更新', 'green')
  }
  
  log('🎉 PWA 功能已启用！', 'green')
  log('📱 现在您的应用支持：', 'blue')
  log('  • 离线缓存', 'blue')
  log('  • 添加到主屏幕', 'blue')
  log('  • 应用图标', 'blue')
  log('  • 启动画面', 'blue')
  
  log('🔄 请重新启动开发服务器以查看效果', 'yellow')
  
  return true
}

// 运行脚本
if (require.main === module) {
  enablePWA()
}

module.exports = { enablePWA }
