const express = require('express')
const RoomModel = require('../models/Room')
const { verifyToken, requireRole, requirePermission, optionalAuth } = require('../middleware/auth')

const router = express.Router()

// Get public rooms list
router.get('/public', optionalAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 20
    const offset = (page - 1) * limit

    const rooms = await RoomModel.getPublicRooms(limit, offset)
    
    res.json({
      rooms: rooms.map(room => room.toJSON()),
      pagination: {
        page,
        limit,
        hasMore: rooms.length === limit
      }
    })
  } catch (error) {
    console.error('Get public rooms error:', error)
    res.status(500).json({ error: 'Failed to fetch rooms' })
  }
})

// Get user's rooms
router.get('/my-rooms', verifyToken, async (req, res) => {
  try {
    console.log(`请求获取自己的房间：${req.user}`);
    
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 20
    const offset = (page - 1) * limit

    const rooms = await RoomModel.getRoomsByCreator(req.user.id, limit, offset)
    
    res.json({
      rooms: rooms.map(room => room.toJSON()),
      pagination: {
        page,
        limit,
        hasMore: rooms.length === limit
      }
    })
  } catch (error) {
    console.error('Get user rooms error:', error)
    res.status(500).json({ error: 'Failed to fetch your rooms' })
  }
})

// Get room details
router.get('/:roomId', optionalAuth, async (req, res) => {
  try {
    const { roomId } = req.params
    const room = await RoomModel.findByRoomId(roomId)

    if (!room) {
      return res.status(404).json({ error: 'Room not found' })
    }

    let canAccess = false
    let userPermission = null

    if (req.user) {
      const accessInfo = await req.user.canAccessRoom(roomId)
      canAccess = accessInfo.canAccess
      userPermission = accessInfo.permission
    } else {
      // Guest access for public rooms
      const roleHierarchy = { guest: 0, user: 1, premium: 2, admin: 3 }
      const requiredRoleLevel = roleHierarchy[room.required_role] || 0
      canAccess = requiredRoleLevel === 0 // Only if guest access is allowed
      userPermission = 'view_only'
    }

    const roomData = room.toJSON()
    roomData.canAccess = canAccess
    roomData.userPermission = userPermission

    res.json({ room: roomData })
  } catch (error) {
    console.error('Get room details error:', error)
    res.status(500).json({ error: 'Failed to fetch room details' })
  }
})

// Create new room
router.post('/', verifyToken, requireRole('user'), async (req, res) => {
  try {
    const { 
      room_id, 
      name, 
      description, 
      max_participants, 
      is_public, 
      password,
      required_role 
    } = req.body

    // Validation
    if (!room_id || !name) {
      return res.status(400).json({ error: 'Room ID and name are required' })
    }

    if (room_id.length < 3 || room_id.length > 50) {
      return res.status(400).json({ error: 'Room ID must be between 3 and 50 characters' })
    }

    const roomIdRegex = /^[a-zA-Z0-9_-]+$/
    if (!roomIdRegex.test(room_id)) {
      return res.status(400).json({ error: 'Room ID can only contain letters, numbers, hyphens, and underscores' })
    }

    if (name.length < 3 || name.length > 100) {
      return res.status(400).json({ error: 'Room name must be between 3 and 100 characters' })
    }

    // Check if user can create rooms with the specified required_role
    if (required_role) {
      const roleHierarchy = { guest: 0, user: 1, premium: 2, admin: 3 }
      const userRoleLevel = roleHierarchy[req.user.role] || 0
      const requiredRoleLevel = roleHierarchy[required_role] || 0
      
      if (userRoleLevel < requiredRoleLevel) {
        return res.status(403).json({ 
          error: `Cannot create room with required role higher than your own role` 
        })
      }
    }

    const roomData = {
      room_id,
      name,
      description: description || '',
      creator_id: req.user.id,
      max_participants: max_participants || 10,
      is_public: is_public !== false, // Default to true
      password,
      required_role: required_role || 'user'
    }

    const room = await RoomModel.create(roomData)

    // Grant full access to creator
    await room.grantPermission(req.user.id, 'full_access', req.user.id)

    res.status(201).json({
      message: 'Room created successfully',
      room: room.toJSON()
    })
  } catch (error) {
    console.error('Create room error:', error)
    
    if (error.message === 'Room ID already exists') {
      return res.status(409).json({ error: error.message })
    }
    
    res.status(500).json({ error: 'Failed to create room' })
  }
})

// Update room
router.put('/:roomId', verifyToken, async (req, res) => {
  try {
    const { roomId } = req.params
    const room = await RoomModel.findByRoomId(roomId)

    if (!room) {
      return res.status(404).json({ error: 'Room not found' })
    }

    // Check if user is the creator or admin
    if (room.creator_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Only room creator or admin can update room' })
    }

    const { name, description, max_participants, is_public, required_role } = req.body
    const updateData = {}

    if (name) {
      if (name.length < 3 || name.length > 100) {
        return res.status(400).json({ error: 'Room name must be between 3 and 100 characters' })
      }
      updateData.name = name
    }

    if (description !== undefined) {
      updateData.description = description
    }

    if (max_participants) {
      if (max_participants < 2 || max_participants > 100) {
        return res.status(400).json({ error: 'Max participants must be between 2 and 100' })
      }
      updateData.max_participants = max_participants
    }

    if (is_public !== undefined) {
      updateData.is_public = is_public
    }

    if (required_role) {
      updateData.required_role = required_role
    }

    const updatedRoom = await room.update(updateData)

    res.json({
      message: 'Room updated successfully',
      room: updatedRoom.toJSON()
    })
  } catch (error) {
    console.error('Update room error:', error)
    res.status(500).json({ error: 'Failed to update room' })
  }
})

// Set room password
router.put('/:roomId/password', verifyToken, async (req, res) => {
  try {
    const { roomId } = req.params
    const { password } = req.body
    
    const room = await RoomModel.findByRoomId(roomId)

    if (!room) {
      return res.status(404).json({ error: 'Room not found' })
    }

    // Check if user is the creator or admin
    if (room.creator_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Only room creator or admin can set password' })
    }

    await room.setPassword(password)

    res.json({ message: 'Room password updated successfully' })
  } catch (error) {
    console.error('Set room password error:', error)
    res.status(500).json({ error: 'Failed to set room password' })
  }
})

// Delete room
router.delete('/:roomId', verifyToken, async (req, res) => {
  try {
    const { roomId } = req.params
    const room = await RoomModel.findByRoomId(roomId)

    if (!room) {
      return res.status(404).json({ error: 'Room not found' })
    }

    // Check if user is the creator or admin
    if (room.creator_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Only room creator or admin can delete room' })
    }

    await room.deactivate()

    res.json({ message: 'Room deleted successfully' })
  } catch (error) {
    console.error('Delete room error:', error)
    res.status(500).json({ error: 'Failed to delete room' })
  }
})

// Grant permission to user
router.post('/:roomId/permissions', verifyToken, async (req, res) => {
  try {
    const { roomId } = req.params
    const { user_id, permission, expires_at } = req.body

    const room = await RoomModel.findByRoomId(roomId)

    if (!room) {
      return res.status(404).json({ error: 'Room not found' })
    }

    // Check if user is the creator or admin
    if (room.creator_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Only room creator or admin can grant permissions' })
    }

    const validPermissions = ['view_only', 'audio_only', 'video_audio', 'full_access']
    if (!validPermissions.includes(permission)) {
      return res.status(400).json({ error: 'Invalid permission level' })
    }

    await room.grantPermission(user_id, permission, req.user.id, expires_at)

    res.json({ message: 'Permission granted successfully' })
  } catch (error) {
    console.error('Grant permission error:', error)
    res.status(500).json({ error: 'Failed to grant permission' })
  }
})

// Get room participants
router.get('/:roomId/participants', verifyToken, requirePermission('view'), async (req, res) => {
  try {
    const { roomId } = req.params
    const room = await RoomModel.findByRoomId(roomId)

    if (!room) {
      return res.status(404).json({ error: 'Room not found' })
    }

    const participants = await room.getParticipants()

    res.json({ participants })
  } catch (error) {
    console.error('Get participants error:', error)
    res.status(500).json({ error: 'Failed to fetch participants' })
  }
})

module.exports = router
