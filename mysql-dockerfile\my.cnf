[mysqld]
# 基本设置
default-authentication-plugin=mysql_native_password
skip-host-cache
skip-name-resolve

# 性能优化
innodb_buffer_pool_size=512M
innodb_log_file_size=128M
innodb_flush_log_at_trx_commit=2
innodb_flush_method=O_DIRECT

# 连接设置
max_connections=500
max_allowed_packet=64M

# 字符集设置
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# 日志设置
slow_query_log=1
slow_query_log_file=/var/log/mysql/mysql-slow.log
long_query_time=2

# 安全设置
local-infile=0

# MySQL 5.7 特定设置
explicit_defaults_for_timestamp=1
sql_mode=NO_ENGINE_SUBSTITUTION,STRICT_TRANS_TABLES

[client]
default-character-set=utf8mb4

[mysql]
default-character-set=utf8mb4
