const mysql = require('mysql2/promise')
const fs = require('fs')
const path = require('path')

async function initializeDatabase() {
  try {
    console.log('? Initializing database...')

    // Read SQL file
    const sqlPath = path.join(__dirname, '..', 'src', 'database', 'init.sql')
    const sqlContent = fs.readFileSync(sqlPath, 'utf8')

    // Split SQL content by statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0)

    // Connect to MySQL (without specifying database first)
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      multipleStatements: true
    })

    console.log('? Connected to MySQL server')

    // Execute each statement
    for (const statement of statements) {
      try {
        await connection.execute(statement)
        console.log('? Executed:', statement.substring(0, 50) + '...')
      } catch (error) {
        console.error('? Error executing statement:', statement.substring(0, 50) + '...')
        console.error('Error:', error.message)
      }
    }

    await connection.end()
    console.log('? Database initialization completed!')

  } catch (error) {
    console.error('? Database initialization failed:', error.message)
    process.exit(1)
  }
}

// Run if called directly
if (require.main === module) {
  require('dotenv').config()
  initializeDatabase()
}

module.exports = initializeDatabase
