-- 创建邀请码表
CREATE TABLE IF NOT EXISTS invite_codes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    invite_code VARCHAR(20) NOT NULL UNIQUE COMMENT '邀请码（唯一）',
    inviter_id BIGINT NOT NULL COMMENT '邀请人ID',
    invite_time DATETIME NOT NULL COMMENT '邀请时间',
    is_used BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已使用',
    use_time DATETIME NULL COMMENT '使用时间',
    user_id BIGINT NULL COMMENT '使用人ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_invite_code (invite_code),
    INDEX idx_inviter_id (inviter_id),
    INDEX idx_user_id (user_id),
    INDEX idx_is_used (is_used),
    INDEX idx_invite_time (invite_time),
    
    -- 外键约束（如果需要的话）
    CONSTRAINT fk_invite_codes_inviter FOREIGN KEY (inviter_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_invite_codes_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邀请码表';

-- 插入一些初始邀请码（可选，用于测试）
-- 注意：这里假设存在ID为1的管理员用户
INSERT IGNORE INTO invite_codes (invite_code, inviter_id, invite_time, is_used, created_at, updated_at) VALUES
('WELCOME1', 1, NOW(), FALSE, NOW(), NOW()),
('WELCOME2', 1, NOW(), FALSE, NOW(), NOW()),
('WELCOME3', 1, NOW(), FALSE, NOW(), NOW()),
('TESTCODE', 1, NOW(), FALSE, NOW(), NOW()),
('ADMINKEY', 1, NOW(), FALSE, NOW(), NOW());
