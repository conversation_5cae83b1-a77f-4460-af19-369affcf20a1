package org.meetsync.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.meetsync.dto.*;
import org.meetsync.entity.User;
import org.meetsync.service.UserService;
import org.meetsync.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/auth")
@CrossOrigin(origins = "*", maxAge = 3600)
@Tag(name = "认证管理", description = "用户认证相关API - 注册、登录、用户信息管理")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    @Operation(
            summary = "用户注册",
            description = "注册新用户账号。出于安全考虑，所有新注册用户默认为USER角色，不允许前端指定角色。"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "注册成功",
                    content = @Content(mediaType = "application/json")),
            @ApiResponse(responseCode = "400", description = "注册失败 - 用户名或邮箱已存在",
                    content = @Content(mediaType = "application/json"))
    })
    @PostMapping("/register")
    public ResponseEntity<?> registerUser(
            @Parameter(description = "用户注册信息", required = true)
            @Valid @RequestBody UserRegistrationDto registrationDto) {
        try {
            logger.info("Registration attempt for username: {}", registrationDto.getUsername());

            UserResponseDto user = userService.registerUser(registrationDto);

            Map<String, Object> response = new HashMap<>();
            response.put("message", "用户注册成功");
            response.put("user", user);

            return ResponseEntity.status(HttpStatus.CREATED).body(response);
            
        } catch (RuntimeException e) {
            logger.error("Registration failed for username: {}, error: {}", 
                        registrationDto.getUsername(), e.getMessage());
            
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
        } catch (Exception e) {
            logger.error("Unexpected error during registration", e);

            Map<String, String> error = new HashMap<>();
            error.put("error", "注册失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    @Operation(
            summary = "用户登录",
            description = "使用用户名/邮箱和密码登录，返回JWT令牌"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "登录成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = JwtResponseDto.class))),
            @ApiResponse(responseCode = "401", description = "登录失败 - 用户名或密码错误",
                    content = @Content(mediaType = "application/json"))
    })
    @PostMapping("/login")
    public ResponseEntity<?> loginUser(
            @Parameter(description = "用户登录信息", required = true)
            @Valid @RequestBody UserLoginDto loginDto) {
        try {
            logger.info("Login attempt for username: {}", loginDto.getUsername());

            Optional<User> userOpt = userService.authenticateUser(loginDto.getUsername(), loginDto.getPassword());
            
            if (userOpt.isPresent()) {
                User user = userOpt.get();
                String token = jwtUtil.generateToken(user);
                
                JwtResponseDto response = new JwtResponseDto(
                    token,
                    new UserResponseDto(user),
                    "登录成功"
                );
                
                logger.info("Login successful for user: {}", user.getUsername());
                return ResponseEntity.ok(response);
            } else {
                logger.warn("Login failed for username: {}", loginDto.getUsername());
                
                Map<String, String> error = new HashMap<>();
                error.put("error", "用户名或密码错误");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(error);
            }

        } catch (Exception e) {
            logger.error("Unexpected error during login", e);

            Map<String, String> error = new HashMap<>();
            error.put("error", "登录失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    @Operation(
            summary = "获取当前用户信息",
            description = "获取当前登录用户的详细信息",
            security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = UserResponseDto.class))),
            @ApiResponse(responseCode = "401", description = "未认证",
                    content = @Content(mediaType = "application/json")),
            @ApiResponse(responseCode = "404", description = "用户不存在",
                    content = @Content(mediaType = "application/json"))
    })
    @GetMapping("/profile")
    public ResponseEntity<?> getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication != null && authentication.getPrincipal() instanceof User user) {

                // Fetch fresh user data
                Optional<UserResponseDto> userOpt = userService.findById(user.getId());
                
                if (userOpt.isPresent()) {
                    return ResponseEntity.ok(userOpt.get());
                } else {
                    Map<String, String> error = new HashMap<>();
                    error.put("error", "用户不存在");
                    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
                }
            } else {
                Map<String, String> error = new HashMap<>();
                error.put("error", "用户未认证");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(error);
            }

        } catch (Exception e) {
            logger.error("Error getting current user profile", e);

            Map<String, String> error = new HashMap<>();
            error.put("error", "获取用户信息失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    /**
     * Update user profile
     */
    @PutMapping("/profile")
    public ResponseEntity<?> updateProfile(@Valid @RequestBody UserRegistrationDto updateDto) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication != null && authentication.getPrincipal() instanceof User user) {

                UserResponseDto updatedUser = userService.updateUserProfile(user.getId(), updateDto);
                
                Map<String, Object> response = new HashMap<>();
                response.put("message", "用户信息更新成功");
                response.put("user", updatedUser);

                return ResponseEntity.ok(response);
            } else {
                Map<String, String> error = new HashMap<>();
                error.put("error", "用户未认证");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(error);
            }

        } catch (RuntimeException e) {
            logger.error("Profile update failed: {}", e.getMessage());

            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
        } catch (Exception e) {
            logger.error("Unexpected error during profile update", e);

            Map<String, String> error = new HashMap<>();
            error.put("error", "用户信息更新失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    /**
     * Change password
     */
    @PutMapping("/change-password")
    public ResponseEntity<?> changePassword(@RequestBody Map<String, String> passwordData) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication != null && authentication.getPrincipal() instanceof User user) {

                String currentPassword = passwordData.get("currentPassword");
                String newPassword = passwordData.get("newPassword");
                
                if (currentPassword == null || newPassword == null) {
                    Map<String, String> error = new HashMap<>();
                    error.put("error", "当前密码和新密码不能为空");
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
                }

                userService.changePassword(user.getId(), currentPassword, newPassword);

                Map<String, String> response = new HashMap<>();
                response.put("message", "密码修改成功");

                return ResponseEntity.ok(response);
            } else {
                Map<String, String> error = new HashMap<>();
                error.put("error", "用户未认证");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(error);
            }

        } catch (RuntimeException e) {
            logger.error("Password change failed: {}", e.getMessage());

            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
        } catch (Exception e) {
            logger.error("Unexpected error during password change", e);

            Map<String, String> error = new HashMap<>();
            error.put("error", "密码修改失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    /**
     * Validate token
     */
    @PostMapping("/validate")
    public ResponseEntity<?> validateToken(@RequestHeader("Authorization") String authHeader) {
        try {
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                String token = authHeader.substring(7);
                
                if (jwtUtil.validateToken(token)) {
                    Long userId = jwtUtil.getUserIdFromToken(token);
                    Optional<UserResponseDto> userOpt = userService.findById(userId);
                    
                    if (userOpt.isPresent()) {
                        Map<String, Object> response = new HashMap<>();
                        response.put("valid", true);
                        response.put("user", userOpt.get());
                        return ResponseEntity.ok(response);
                    }
                }
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("valid", false);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Token validation error", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("valid", false);
            return ResponseEntity.ok(response);
        }
    }
}
