/**
 * API 工具类
 */
import authManager from './auth.js'
import networkManager from './network.js'

class ApiManager {
  constructor() {
    // 导入配置
    const config = require('../config/index.js').default
    this.baseUrl = config.server.baseUrl
    this.mediaApiUrl = config.server.mediaApiUrl
  }

  /**
   * 通用 API 调用方法
   */
  async request(url, method = 'GET', data = null, options = {}) {
    const token = authManager.getAuthToken()

    // 判断是否为媒体API请求
    const isMediaApi = url.startsWith('/media')
    const baseUrl = isMediaApi ? this.mediaApiUrl : this.baseUrl

    const requestOptions = {
      url: baseUrl + (isMediaApi ? url.replace('/media', '') : url),
      method,
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      timeout: options.timeout || 30000,
      // SSL 配置
      sslVerify: false, // 开发环境可以设置为 false
      enableHttp2: false, // 禁用 HTTP/2
      enableQuic: false, // 禁用 QUIC
      // 兼容性配置
      enableCache: false,
      enableHttpDNS: false
    }

    if (data) {
      requestOptions.data = data
    }

    if (token) {
      requestOptions.header.Authorization = `Bearer ${token}`
    }

    try {
      // 使用网络管理器进行请求
      const result = await networkManager.requestWithRetry(requestOptions)
      return result
    } catch (error) {
      // 特殊处理认证错误
      if (error.message.includes('401') || error.message.includes('认证失败')) {
        authManager.clearAuth()
        uni.reLaunch({
          url: '/pages/auth/login'
        })
        throw new Error('认证失败，请重新登录')
      }

      // 诊断网络问题
      const diagnosis = await networkManager.diagnoseNetworkIssue(error)
      console.error('API请求失败:', diagnosis)

      throw error
    }
  }

  /**
   * GET 请求
   */
  get(url, options = {}) {
    return this.request(url, 'GET', null, options)
  }

  /**
   * POST 请求
   */
  post(url, data, options = {}) {
    return this.request(url, 'POST', data, options)
  }

  /**
   * PUT 请求
   */
  put(url, data, options = {}) {
    return this.request(url, 'PUT', data, options)
  }

  /**
   * DELETE 请求
   */
  delete(url, options = {}) {
    return this.request(url, 'DELETE', null, options)
  }

  // ========== 房间相关 API ==========

  /**
   * 获取公共房间列表
   */
  async getPublicRooms() {
    return this.get('/rooms/public')
  }

  /**
   * 获取我的房间列表
   */
  async getMyRooms() {
    return this.get('/rooms/my-rooms')
  }

  /**
   * 获取房间详情
   */
  async getRoomDetails(roomId) {
    return this.get(`/rooms/${roomId}`)
  }

  /**
   * 创建房间
   */
  async createRoom(roomData) {
    return this.post('/rooms', roomData)
  }

  /**
   * 更新房间
   */
  async updateRoom(roomId, roomData) {
    return this.put(`/rooms/${roomId}`, roomData)
  }

  /**
   * 删除房间
   */
  async deleteRoom(roomId) {
    return this.delete(`/rooms/${roomId}`)
  }

  /**
   * 加入房间
   */
  async joinRoom(roomId, password = null) {
    const data = password ? { password } : {}
    return this.post(`/rooms/${roomId}/join`, data)
  }

  /**
   * 检查房间访问权限
   */
  async checkRoomAccess(roomId, password = null) {
    const data = password ? { password } : {}
    return this.post(`/rooms/${roomId}/check-access`, data)
  }

  // ========== 认证相关 API ==========

  /**
   * 用户登录
   */
  async login(username, password) {
    return this.post('/auth/login', { username, password })
  }

  /**
   * 用户注册
   */
  async register(userData) {
    return this.post('/auth/register', userData)
  }

  /**
   * 验证令牌
   */
  async verifyToken() {
    return this.get('/auth/verify')
  }

  // ========== 媒体相关 API ==========

  /**
   * 获取媒体服务器状态
   */
  async getMediaServerStatus() {
    return this.get('/media/status')
  }

  /**
   * 创建媒体房间
   */
  async createMediaRoom(roomId) {
    return this.post('/media/rooms', { roomId })
  }

  /**
   * 获取RTP能力
   */
  async getRtpCapabilities(roomId) {
    return this.get(`/media/rooms/${roomId}/rtp-capabilities`)
  }

  /**
   * 创建WebRTC传输
   */
  async createTransport(roomId, direction) {
    return this.post(`/media/rooms/${roomId}/transports`, { direction })
  }

  /**
   * 连接传输
   */
  async connectTransport(roomId, transportId, dtlsParameters) {
    return this.post(`/media/rooms/${roomId}/transports/${transportId}/connect`, {
      dtlsParameters
    })
  }

  /**
   * 创建生产者
   */
  async createProducer(roomId, transportId, kind, rtpParameters, appData) {
    return this.post(`/media/rooms/${roomId}/producers`, {
      transportId,
      kind,
      rtpParameters,
      appData
    })
  }

  /**
   * 创建消费者
   */
  async createConsumer(roomId, transportId, producerId, rtpCapabilities) {
    return this.post(`/media/rooms/${roomId}/consumers`, {
      transportId,
      producerId,
      rtpCapabilities
    })
  }

  /**
   * 恢复消费者
   */
  async resumeConsumer(roomId, consumerId) {
    return this.post(`/media/rooms/${roomId}/consumers/${consumerId}/resume`)
  }

  /**
   * 获取生产者列表
   */
  async getProducers(roomId) {
    return this.get(`/media/rooms/${roomId}/producers`)
  }

  /**
   * 关闭生产者
   */
  async closeProducer(roomId, producerId) {
    try {
      return await this.delete(`/media/rooms/${roomId}/producers/${producerId}`)
    } catch (error) {
      // 如果是404或500错误，可能是生产者已经不存在
      if (error.response && (error.response.status === 404 || error.response.status === 500)) {
        console.warn(`生产者 ${producerId} 可能已不存在:`, error.message)
        // 返回一个成功的响应，避免前端报错
        return { success: true, message: '生产者已清理' }
      }
      throw error
    }
  }

  /**
   * 关闭消费者
   */
  async closeConsumer(roomId, consumerId) {
    return this.delete(`/media/rooms/${roomId}/consumers/${consumerId}`)
  }

  /**
   * 清理用户的所有生产者（用于页面刷新后的清理）
   */
  async cleanupUserProducers(roomId) {
    return this.post(`/media/rooms/${roomId}/cleanup-producers`)
  }
}

// 创建全局实例
const apiManager = new ApiManager()

export default apiManager
