{"version": 3, "file": "HandlerInterface.d.ts", "sourceRoot": "", "sources": ["../../src/handlers/HandlerInterface.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,mBAAmB,CAAC;AACzD,OAAO,KAAK,EACX,aAAa,EACb,YAAY,EACZ,cAAc,EACd,iBAAiB,EACjB,eAAe,EACf,MAAM,cAAc,CAAC;AACtB,OAAO,KAAK,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,MAAM,aAAa,CAAC;AAC7E,OAAO,KAAK,EAAE,qBAAqB,EAAE,MAAM,aAAa,CAAC;AACzD,OAAO,KAAK,EACX,eAAe,EACf,kBAAkB,EAClB,aAAa,EACb,qBAAqB,EACrB,MAAM,kBAAkB,CAAC;AAC1B,OAAO,KAAK,EACX,gBAAgB,EAChB,cAAc,EACd,oBAAoB,EACpB,MAAM,mBAAmB,CAAC;AAE3B,MAAM,MAAM,cAAc,GAAG,MAAM,gBAAgB,CAAC;AAEpD,MAAM,MAAM,iBAAiB,GAAG;IAC/B,SAAS,EAAE,MAAM,GAAG,MAAM,CAAC;IAC3B,aAAa,EAAE,aAAa,CAAC;IAC7B,aAAa,EAAE,YAAY,EAAE,CAAC;IAC9B,cAAc,EAAE,cAAc,CAAC;IAC/B,cAAc,CAAC,EAAE,cAAc,CAAC;IAChC,UAAU,CAAC,EAAE,YAAY,EAAE,CAAC;IAC5B,kBAAkB,CAAC,EAAE,qBAAqB,CAAC;IAC3C,kBAAkB,CAAC,EAAE,GAAG,CAAC;IACzB,sBAAsB,CAAC,EAAE,GAAG,CAAC;IAC7B,uBAAuB,EAAE,GAAG,CAAC;CAC7B,CAAC;AAEF,MAAM,MAAM,kBAAkB,GAAG;IAChC,KAAK,EAAE,gBAAgB,CAAC;IACxB,SAAS,CAAC,EAAE,qBAAqB,EAAE,CAAC;IACpC,YAAY,CAAC,EAAE,oBAAoB,CAAC;IACpC,KAAK,CAAC,EAAE,kBAAkB,CAAC;IAC3B,WAAW,CAAC,EAAE,mBAAmB,CAAC;CAClC,CAAC;AAEF,MAAM,MAAM,iBAAiB,GAAG;IAC/B,OAAO,EAAE,MAAM,CAAC;IAChB,aAAa,EAAE,aAAa,CAAC;IAC7B,SAAS,CAAC,EAAE,YAAY,CAAC;CACzB,CAAC;AAEF,MAAM,MAAM,qBAAqB,GAAG;IACnC,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,OAAO,GAAG,OAAO,CAAC;IACxB,aAAa,EAAE,aAAa,CAAC;IAC7B;;;;;OAKG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,aAAa,CAAC,EAAE,qBAAqB,CAAC;CACtC,CAAC;AAEF,MAAM,MAAM,oBAAoB,GAAG;IAClC,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,gBAAgB,CAAC;IACxB,WAAW,CAAC,EAAE,cAAc,CAAC;CAC7B,CAAC;AAEF,MAAM,MAAM,6BAA6B,GAAG,oBAAoB,CAAC;AAEjE,MAAM,MAAM,4BAA4B,GAAG;IAC1C,WAAW,EAAE,cAAc,CAAC;IAC5B,oBAAoB,EAAE,oBAAoB,CAAC;CAC3C,CAAC;AAEF,MAAM,MAAM,gCAAgC,GAAG;IAC9C,oBAAoB,EAAE,oBAAoB,CAAC;IAC3C,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,QAAQ,CAAC,EAAE,MAAM,CAAC;CAClB,CAAC;AAEF,MAAM,MAAM,+BAA+B,GAAG;IAC7C,WAAW,EAAE,cAAc,CAAC;CAC5B,CAAC;AAEF,MAAM,MAAM,aAAa,GAAG;IAC3B,QAAQ,EAAE,EAAE,CAAC;IACb,UAAU,EAAE;QACX;YAAE,cAAc,EAAE,cAAc,CAAA;SAAE;QAClC,MAAM,IAAI;QACV,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI;KACtB,CAAC;IACF,0BAA0B,EAAE,CAAC,iBAAiB,CAAC,CAAC;IAChD,oBAAoB,EAAE,CAAC,8BAA8B,CAAC,CAAC;IACvD,wBAAwB,EAAE,CAAC,eAAe,CAAC,CAAC;CAC5C,CAAC;AAEF,8BAAsB,gBAAiB,SAAQ,oBAAoB,CAAC,aAAa,CAAC;;IAKjF,QAAQ,KAAK,IAAI,IAAI,MAAM,CAAC;IAE5B,QAAQ,CAAC,KAAK,IAAI,IAAI;IAEtB,QAAQ,CAAC,wBAAwB,IAAI,OAAO,CAAC,eAAe,CAAC;IAE7D,QAAQ,CAAC,yBAAyB,IAAI,OAAO,CAAC,gBAAgB,CAAC;IAE/D,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,iBAAiB,GAAG,IAAI;IAE9C,QAAQ,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAEpE,QAAQ,CAAC,UAAU,CAAC,aAAa,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC;IAEhE,QAAQ,CAAC,iBAAiB,IAAI,OAAO,CAAC,cAAc,CAAC;IAErD,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAEtE,QAAQ,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAEpD,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAErD,QAAQ,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAEtD,QAAQ,CAAC,YAAY,CACpB,OAAO,EAAE,MAAM,EACf,KAAK,EAAE,gBAAgB,GAAG,IAAI,GAC5B,OAAO,CAAC,IAAI,CAAC;IAEhB,QAAQ,CAAC,kBAAkB,CAC1B,OAAO,EAAE,MAAM,EACf,YAAY,EAAE,MAAM,GAClB,OAAO,CAAC,IAAI,CAAC;IAEhB,QAAQ,CAAC,wBAAwB,CAChC,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,GAAG,GACT,OAAO,CAAC,IAAI,CAAC;IAEhB,QAAQ,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC;IAEjE,QAAQ,CAAC,eAAe,CACvB,OAAO,EAAE,6BAA6B,GACpC,OAAO,CAAC,4BAA4B,CAAC;IAExC,QAAQ,CAAC,OAAO,CACf,WAAW,EAAE,qBAAqB,EAAE,GAClC,OAAO,CAAC,oBAAoB,EAAE,CAAC;IAElC,QAAQ,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAEzD,QAAQ,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAE1D,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAE3D,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC;IAEnE,QAAQ,CAAC,kBAAkB,CAC1B,OAAO,EAAE,gCAAgC,GACvC,OAAO,CAAC,+BAA+B,CAAC;CAC3C"}